<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Email Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            text-align: center;
        }

        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: white;
            color: #141414;
            border-radius: 20px;
            overflow: hidden;
            padding-top: 0;
            /* background-image: url(https://temoc-token.vercel.app/assets/images/email/background.png); */
            padding: 30px;
            background-size: 100% 100%;
        }

        /* .section1 {
        padding: 45px 30px 0px 30px;
        background-image: url(https://www.nuttyhunt.com/assets/images/email/congbg.png);
        background-size: 100% 100%;
      }
      .section2 {
        padding: 0px 30px 45px 30px;
        background-image: url(https://www.nuttyhunt.com/assets/images/email/footer.png);

        background-image: url();
        background-size: 100% 100%;
      } */
        .header {
            text-align: center;
            margin-bottom: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 25px;
            text-transform: uppercase;
        }

        .content {
            font-size: 16px;
            line-height: 1.1;
            /* text-align: center; */
        }

        .content p {
            margin-top: 4px;
            font-size: 16px;
        }

        .button {
            display: inline-block;
            background-color: #FF8000;
            color: white;
            padding: 15px 28px;
            border: none;
            border-radius: 50px;
            text-decoration: none;
            font-size: 16px;
            cursor: pointer;
        }

        .button:hover {
            background-color: #e67300;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-bottom: 10px;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div style="text-align: center; margin-bottom: 20px">
        </div>
        <div class="section1">
            <!-- <div class="header">
                <h1>🎉 Fan Login Welcome Email</h1>
            </div> -->
            <p style="font-size: 18px; text-align: center; color: #FF8000; font-weight: 600;">
                Welcome to TEMOC – The Future of Music is Yours 🎵
            </p>
            <div class="content">
                <p>
                    Hi
                    <strong>{{fanName}}</strong>
                </p>
                <p>
                    Welcome to TEMOC! You're now part of a global movement where fans
                    connect directly with their favorite artists, unlock exclusive
                    content, and own a piece of the music they love.
                </p>
                <p>
                    Start by exploring artists, collecting tokens, and diving into gated
                    content made just for fans like you.
                </p>
            </div>
            <a href="https://temoc-frontend.vercel.app/" target="_blank"> <button class="button">LOGIN NOW!</button></a>
            <p>
                <span style="display: block; margin-top:8px"> Rock on,<br />
                    The TEMOC Team
                </span>
            </p>
        </div>
    </div>
</body>

</html>
