# Make sure you're inside the NestJS project root

# Base folders
mkdir -p src/auth/dynamic src/auth/guards src/auth/decorators src/auth/strategies
mkdir -p src/users/dto src/users/schemas
mkdir -p src/tokens/dto src/tokens/schemas
mkdir -p src/content src/database src/config
mkdir -p scripts test


# Auth module and nested dynamic module
nest g module auth
nest g controller auth
nest g service auth

nest g module auth/dynamic
nest g controller auth/dynamic
nest g service auth/dynamic

# Users module
nest g module users
nest g controller users
nest g service users

# Tokens module
nest g module tokens
nest g controller tokens
nest g service tokens

# Placeholder config files

