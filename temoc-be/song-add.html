<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Email Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            text-align: center;
        }

        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 20px auto;
            background-color: white;
            color: #141414;
            border-radius: 20px;
            overflow: hidden;
            padding: 30px;
            background-size: 100% 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 25px;
            text-transform: uppercase;
        }

        .content {
            font-size: 16px;
            line-height: 1.1;
        }

        .content p {
            margin-top: 4px;
            font-size: 16px;
        }

        .button {
            display: inline-block;
            background-color: #FF8000;
            color: white;
            padding: 15px 28px;
            border: none;
            border-radius: 50px;
            text-decoration: none;
            font-size: 16px;
            cursor: pointer;
        }

        .button:hover {
            background-color: #e67300;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-bottom: 10px;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="email-container">
        <div class="section1">
            <!-- <div class="header">
                <h1>🎵 New Song Added</h1>
            </div> -->
            <p style="font-size: 18px; text-align: center; color: #FF8000; font-weight: 600;">
                Your Song Has Been Added 🎧
            </p>
            <div class="content">
                <p>
                    Hi <strong>{{artistName}}</strong>,
                </p>
                <p>
                    You've successfully added a new song to your library: <strong>"{{songTitle}}"</strong>
                </p>
                <p>
                    Make sure to tag it with a cover image and set access levels to maximize fan engagement.
                </p>
                <p>
                    Manage your content
                </p>
            </div>
            <a href="https://temoc-frontend.vercel.app/" target="_blank">
                <button class="button">GO TO LIBRARY</button>
            </a>
            <p style="margin-top: 20px;">
                <span style="display: block;">
                    Keep dropping fire,<br />
                    The TEMOC Team
                </span>
            </p>
        </div>
    </div>
</body>

</html>
