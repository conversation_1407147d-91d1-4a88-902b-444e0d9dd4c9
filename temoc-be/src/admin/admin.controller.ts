import {
  Controller,
  Post,
  Body,
  Get,
  UseGuards,
  Param,
  Request,
  UnauthorizedException,
  HttpCode,
  HttpStatus,
} from "@nestjs/common";
import { AdminService } from "./admin.service";
import { CreateAdminDto } from "./dto/create-admin.dto";
import { LoginAdminDto } from "./dto/login-admin.dto";
import { AdminJwtAuthGuard } from "../auth/guards/admin-jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { UserRole } from "../users/schemas/user.schema";
import { AuthUser } from "src/auth/decorators/user.decorator";

@Controller("admin")
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post("signup")
  async signup(@Body() createAdminDto: CreateAdminDto) {
    const admin = await this.adminService.create(createAdminDto);
    return {
      success: true,
      message: "Admin created successfully",
      admin: {
        id: admin.id,
        email: admin.email,
        firstName: admin.firstName,
        lastName: admin.lastName,
      },
    };
  }

  @Post("login")
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginAdminDto: LoginAdminDto) {
    return this.adminService.login(loginAdminDto);
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get("artist-applications/pending")
  async getPendingArtistApplications() {
    const applications = await this.adminService.getPendingArtistApplications();
    return {
      success: true,
      applications,
    };
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get("artist-applications/approved")
  async getApprovedArtistApplications() {
    const applications =
      await this.adminService.getApprovedArtistApplications();
    return {
      success: true,
      applications,
    };
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get("artist-applications/rejected")
  async getRejectedArtistApplications() {
    const applications =
      await this.adminService.getRejectedArtistApplications();
    return {
      success: true,
      applications,
    };
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post("artist-applications/:userId/approve")
  async approveArtistApplication(
    @Param("userId") userId: string,
    @Request() req
  ) {
    // Fix: Use sub from JWT payload instead of userId
    const adminId = req.user.sub || req.user._id;
    const updatedUser = await this.adminService.approveArtistApplication(
      userId,
      adminId
    );
    return {
      success: true,
      message: "Artist application approved successfully",
      user: updatedUser,
    };
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post("artist-applications/:userId/reject")
  async rejectArtistApplication(
    @Param("userId") userId: string,
    @Body("rejectionReason") rejectionReason: string,
    @Request() req
  ) {
    // Fix: Use sub from JWT payload instead of userId
    const adminId = req.user.sub || req.user._id;
    const updatedUser = await this.adminService.rejectArtistApplication(
      userId,
      adminId,
      rejectionReason
    );
    return {
      success: true,
      message: "Artist application rejected successfully",
      user: updatedUser,
    };
  }

  @UseGuards(AdminJwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get("me")
  async getMe(@AuthUser() admin: any) {
    return admin;
  }
}
