// src/admin/admin.module.ts
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { Admin, AdminSchema } from './schemas/admin.schema';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module'; // Import AuthModule instead of JwtModule
import { AdminJwtAuthGuard } from '../auth/guards/admin-jwt-auth.guard';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Admin.name, schema: AdminSchema }]),
    UsersModule,
    AuthModule, // Use AuthModule which already has JWT configured
  ],
  controllers: [AdminController],
  providers: [AdminService, AdminJwtAuthGuard],
  exports: [AdminService],
})
export class AdminModule {}