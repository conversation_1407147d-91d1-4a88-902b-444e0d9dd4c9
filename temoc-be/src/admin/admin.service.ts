// src/admin/admin.service.ts
import { Injectable, NotFoundException, ConflictException, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { Admin } from './schemas/admin.schema';
import { CreateAdminDto } from './dto/create-admin.dto';
import { LoginAdminDto } from './dto/login-admin.dto';
import { UsersService } from '../users/users.service';
import { UserRole } from '../users/schemas/user.schema';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(Admin.name) private adminModel: Model<Admin>,
    private jwtService: JwtService,
    private usersService: UsersService,
  ) {}

  async create(createAdminDto: CreateAdminDto): Promise<Admin> {
    const { email, password } = createAdminDto;

    // Check if admin already exists
    const existingAdmin = await this.adminModel.findOne({ email }).exec();
    if (existingAdmin) {
      throw new ConflictException('Admin with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new admin
    const newAdmin = new this.adminModel({
      ...createAdminDto,
      password: hashedPassword,
    });

    return newAdmin.save();
  }

  async login(loginAdminDto: LoginAdminDto) {
    const { email, password } = loginAdminDto;

    try {
      // Find admin by email
      const admin = await this.adminModel.findOne({ email }).exec();
      if (!admin) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, admin.password);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Invalid credentials');
      }

      // Update last login
      admin.lastLogin = new Date();
      await admin.save();

      // Create clean JWT payload
      const payload = {
        sub: admin._id.toString(), // Convert ObjectId to string
        email: admin.email,
        role: UserRole.ADMIN, // Use the enum value
        isAdmin: true,
        iat: Math.floor(Date.now() / 1000), // Issued at timestamp
      };

      console.log('Admin login - JWT payload:', payload); // Debug log

      // Sign token with explicit options
      const accessToken = this.jwtService.sign(payload, {
        algorithm: 'HS256',
        expiresIn: '7d',
      });

      console.log('Admin login - Token generated successfully'); // Debug log

      return {
        success: true,
        access_token: accessToken,
        token_type: 'Bearer',
        expires_in: 604800, // 7 days in seconds
        admin: {
          id: admin._id.toString(),
          email: admin.email,
          firstName: admin.firstName,
          lastName: admin.lastName,
          role: UserRole.ADMIN,
          isAdmin: true,
        },
      };

    } catch (error) {
      console.error('Admin login error:', error);

      if (error instanceof UnauthorizedException) {
        throw error;
      }

      throw new UnauthorizedException('Login failed. Please try again.');
    }
  }

  async getPendingArtistApplications() {
    return this.usersService.findArtistApplicationsByStatus('pending');
  }

  async getApprovedArtistApplications() {
    return this.usersService.findArtistApplicationsByStatus('approved');
  }

  async getRejectedArtistApplications() {
    return this.usersService.findArtistApplicationsByStatus('rejected');
  }

  async approveArtistApplication(userId: string, adminId: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.artistProfile || !user.artistProfile.applicationSubmitted) {
      throw new NotFoundException('No artist application found for this user');
    }

    // Update artist profile AND change user role to artist
    return this.usersService.update(userId, {
      role: 'artist', // Change user role from fan to artist
      'artistProfile.status': 'approved',
      'artistProfile.isVerified': true,
      'artistProfile.applicationReviewedBy': adminId,
      'artistProfile.applicationReviewDate': new Date(),
      // Set initial viewMode to artist when approved
      'metadata.viewMode': 'artist',
      'metadata.lastViewModeSwitch': new Date(),
    });
  }

  async rejectArtistApplication(userId: string, adminId: string, rejectionReason: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.artistProfile || !user.artistProfile.applicationSubmitted) {
      throw new NotFoundException('No artist application found for this user');
    }

    // Update artist profile
    return this.usersService.update(userId, {
      'artistProfile.status': 'rejected',
      'artistProfile.isVerified': false,
      'artistProfile.applicationReviewedBy': adminId,
      'artistProfile.applicationReviewDate': new Date(),
      'artistProfile.rejectionReason': rejectionReason,
    });
  }
}