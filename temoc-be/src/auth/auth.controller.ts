import {
  Controller,
  Post,
  Body,
  UnauthorizedException,
  BadRequestException,
  Get,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from "@nestjs/common";
import { AuthService } from "./auth.service";
import { UsersService } from "../users/users.service";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "@nestjs/config";
import { lastValueFrom } from "rxjs";
import { AuthUser } from "./decorators/user.decorator";

@Controller("auth")
export class AuthController {
  constructor(
    private authService: AuthService,
    private usersService: UsersService,
    private httpService: HttpService,
    private configService: ConfigService
  ) {}

  @Post("dynamic-login")
  @HttpCode(HttpStatus.OK)
  async dynamicLogin(
    @Body() body: { dynamicToken: string; dynamicUserId: string }
  ) {
    try {
      // Verify the token with Dynamic's API
      const dynamicApiKey = this.configService.get<string>("dynamic.apiKey");
      const dynamicEnvId = this.configService.get<string>(
        "dynamic.environmentId"
      );

      if (!dynamicApiKey || !dynamicEnvId) {
        throw new BadRequestException(
          "Dynamic API configuration is incomplete"
        );
      }

      // Optionally validate the token with Dynamic's API
      // This is a simplified example - in a real implementation, you'd want to use
      // Dynamic's SDK or API to validate the token
      try {
        const verifyResponse = await lastValueFrom(
          this.httpService.post(
            `https://api.dynamic.xyz/v1/environments/${dynamicEnvId}/auth/verify`,
            { token: body.dynamicToken },
            {
              headers: {
                Authorization: `Bearer ${dynamicApiKey}`,
                "Content-Type": "application/json",
              },
            }
          )
        );

        // If token verification fails, Dynamic's API would return an error
        // If we got here, the token is valid
      } catch (error) {
        throw new UnauthorizedException("Invalid Dynamic token");
      }

      // Find the user by Dynamic User ID
      const user = await this.usersService.findByDynamicUserId(
        body.dynamicUserId
      );

      if (!user) {
        throw new UnauthorizedException("User not found");
      }

      // Update last login time
      await this.usersService.update(user.id, { lastLogin: new Date() });

      // Return JWT token and user info
      return this.authService.login(user);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new UnauthorizedException("Authentication failed");
    }
  }

  @Post("verify-wallet")
  @HttpCode(HttpStatus.OK)
  async verifyWallet(
    @Body() body: { address: string; signature: string; message: string }
  ) {
    try {
      // Here you would verify the signature against the wallet address and message
      // This is a simplified example - in a real implementation, you'd use ethers.js
      // or web3.js to verify the signature

      // For now, we'll just check if a user with this wallet address exists
      const user = await this.usersService.findByWalletAddress(body.address);

      if (!user) {
        throw new UnauthorizedException(
          "User not found with this wallet address"
        );
      }

      // Update last login time
      await this.usersService.update(user.id, { lastLogin: new Date() });

      // Return JWT token and user info
      return this.authService.login(user);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new UnauthorizedException("Wallet verification failed");
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get("me")
  async getProfile(@Request() req) {
    // console.log("req.user", req.user._id);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new UnauthorizedException("User not found");
    }

    return user;
    // artistProfile: user.role === "artist" ? user.artistProfile : undefined,
  }

  // @UseGuards(JwtAuthGuard)
  // @Get("me")
  // getAdminByAddress(@AuthUser() user: any) {
  //   const authUser = user?.verified_credentials?.find((item) => item.address);
  //   if (!authUser?.address) {
  //     return null;
  //   }
  //   return this.usersService.findOne({
  //     "wallets.address": { $regex: new RegExp(authUser.address, "i") },
  //   });
  // }

  @UseGuards(JwtAuthGuard)
  @Post("refresh-token")
  @HttpCode(HttpStatus.OK)
  async refreshToken(@Request() req) {
    const userId = req._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new UnauthorizedException("User not found");
    }

    // Return a new JWT token
    return this.authService.login(user);
  }

  @UseGuards(JwtAuthGuard)
  @Post("logout")
  @HttpCode(HttpStatus.OK)
  async logout(@Request() req) {
    // In a stateless JWT approach, you can't actually invalidate tokens
    // You would typically handle this on the client side by removing the token

    // However, you can track logout events or implement a token blacklist
    // For now, we'll just return a success message
    return { success: true, message: "Logged out successfully" };
  }
}
