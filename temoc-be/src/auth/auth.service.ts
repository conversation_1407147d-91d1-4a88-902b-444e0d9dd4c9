import { Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { UsersService } from "../users/users.service";
import { User, UserRole, AuthProvider } from "../users/schemas/user.schema";
import { HttpService } from "@nestjs/axios";
import { lastValueFrom } from "rxjs";
// import { EmailService } from '../email/email.service';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private httpService: HttpService,
    // private emailService: EmailService,
  ) {}

  async validateDynamicUser(dynamicUserId: string): Promise<User> {
    return this.usersService.findByDynamicUserId(dynamicUserId);
  }

  async login(user: User) {
    const payload = {
      sub: user._id,
      email: user.email,
      role: user.role,
      isAdmin: user.isAdmin,
      dynamicUserId: user.dynamicUserId,
    };

    // // Send welcome email for fans on first login
    // if (user.role === UserRole.FAN && !user.lastLogin) {
    //   await this.emailService.sendFanWelcomeEmail(
    //     user.email,
    //     user.displayName || user.firstName || user.email.split('@')[0]
    //   );
    // }

    // // Update lastLogin
    // if (user._id) {
    //   await this.usersService.update(user._id, { lastLogin: new Date() });
    // }

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        displayName: user.displayName,
        avatarUrl: user.avatarUrl,
        isAdmin: user.isAdmin,
        dynamicUserId: user.dynamicUserId,
        wallets:
          user.wallets?.map((w) => ({
            address: w.address,
            chainId: w.chainId,
            provider: w.provider,
          })) || [],
      },
    };
  }

  async validateDynamicToken(
    token: string,
    dynamicUserId: string
  ): Promise<boolean> {
    const dynamicApiKey = this.configService.get<string>("dynamic.apiKey");
    const dynamicEnvId = this.configService.get<string>(
      "dynamic.environmentId"
    );

    if (!dynamicApiKey || !dynamicEnvId) {
      throw new Error("Dynamic API configuration is incomplete");
    }

    try {
      // This is a simplified example - in a real implementation, you'd want to use
      // Dynamic's SDK or API to validate the token properly
      const response = await lastValueFrom(
        this.httpService.post(
          `https://api.dynamic.xyz/v1/environments/${dynamicEnvId}/auth/verify`,
          { token, userId: dynamicUserId },
          {
            headers: {
              Authorization: `Bearer ${dynamicApiKey}`,
              "Content-Type": "application/json",
            },
          }
        )
      );

      return response.data?.success === true;
    } catch (error) {
      return false;
    }
  }

  async createUserFromDynamic(dynamicUserData: any): Promise<User> {
    const { id, email, wallets, displayName, avatarUrl } = dynamicUserData;

    // Determine auth provider based on data
    let authProvider = AuthProvider.EMAIL;
    if (wallets && wallets.length > 0) {
      authProvider = AuthProvider.WALLET;
    } else if (email && email.includes("google")) {
      authProvider = AuthProvider.GOOGLE;
    }

    const newUser = await this.usersService.create({
      email,
      dynamicUserId: id,
      role: UserRole.FAN, // Default role is fan
      displayName: displayName || email.split("@")[0],
      avatarUrl,
      authProvider,
      address: wallets?.[0]?.address,
      lastLogin: new Date(),
      metadata: {
        viewMode: 'fan', // Default viewMode is fan
        createdAt: new Date(),
      },
      wallets:
        wallets?.map((w) => ({
          address: w.address,
          chainId: w.chainId,
          provider: w.provider,
        })) || [],
    });

    return newUser;
  }

  // Get Dynamic user data - useful for webhooks or other integrations
  async getDynamicUserData(dynamicUserId: string): Promise<any> {
    const dynamicApiKey = this.configService.get<string>("dynamic.apiKey");
    const dynamicEnvId = this.configService.get<string>(
      "dynamic.environmentId"
    );

    if (!dynamicApiKey || !dynamicEnvId) {
      throw new Error("Dynamic API configuration is incomplete");
    }

    try {
      const response = await lastValueFrom(
        this.httpService.get(
          `https://api.dynamic.xyz/v1/environments/${dynamicEnvId}/users/${dynamicUserId}`,
          {
            headers: {
              Authorization: `Bearer ${dynamicApiKey}`,
              "Content-Type": "application/json",
            },
          }
        )
      );

      return response.data?.user;
    } catch (error) {
      throw new Error(`Failed to get Dynamic user data: ${error.message}`);
    }
  }
}
