
import {
    Body,
    Controller,
    Post,
    Headers,
    Logger,
    HttpException,
    HttpStatus,
    Inject,
    Injectable
  } from '@nestjs/common';
  import { ConfigService } from '@nestjs/config';
  import { UsersService } from '../users/users.service';
  import { AuthProvider, UserRole } from '../users/schemas/user.schema';
  import * as crypto from 'crypto';
  import { EmailService } from '../email/email.service';

  @Controller('webhooks/dynamic')
  export class DynamicWebhookController {
    private readonly logger = new Logger(DynamicWebhookController.name);

    constructor(
      private readonly usersService: UsersService,
      private readonly configService: ConfigService,
      private emailService: EmailService,
    ) {}

    @Post()
    async handleDynamicWebhook(
      @Headers() headers: Record<string, string>,
      @Body() payload: any,
    ) {
      try {
        this.logger.log(`Received webhook: ${JSON.stringify(payload.eventName)}`);
        this.logger.debug(`Webhook payload: ${JSON.stringify(payload)}`);

        // Only log headers in development
        if (process.env.NODE_ENV === 'development') {
          this.logger.debug(`Webhook headers: ${JSON.stringify(headers)}`);
        }

        // Verify signature if needed
        const signature = headers['x-dynamic-signature-256'];
        if (signature) {
          // Extract signature without prefix
          const signatureValue = signature.startsWith('sha256=')
            ? signature.slice(7)
            : signature;

          this.verifySignature(signatureValue, payload);
        }

        // Process the webhook based on the event name
        const { eventName, data, userId } = payload;

        this.logger.log(`Processing event: ${eventName} for user: ${userId}`);

        switch (eventName) {
          case 'user.created':
            await this.handleUserCreated(userId, data);
            break;

          case 'user.updated':
            await this.handleUserUpdated(userId, data);
            break;

          case 'wallet.linked':
            await this.handleWalletLinked(userId, data);
            break;

          case 'wallet.unlinked':
            await this.handleWalletUnlinked(userId, data);
            break;

          case 'user.session.created':
            await this.handleSessionCreated(userId, data);
            break;

          case 'wallet.created':
            await this.handleWalletCreated(userId, data);
            break;

          default:
            this.logger.log(`Unhandled event type: ${eventName}`);
        }

        // Always return success to prevent Dynamic from retrying
        return { success: true };
      } catch (error) {
        this.logger.error(`Error processing webhook: ${error.message}`, error.stack);

        // Return 200 OK even on error to prevent retries
        return {
          success: false,
          message: 'Error processing webhook, but received successfully'
        };
      }
    }

    private verifySignature(signature: string, payload: any): boolean {
      try {
        const webhookSecret = this.configService.get<string>('DYNAMIC_WEBHOOK_SECRET');

        if (!webhookSecret) {
          this.logger.warn('Webhook secret not configured, skipping verification');
          return true;
        }

        // Create the string to sign
        const stringToSign = JSON.stringify(payload);

        // Compute HMAC signature
        const computedSignature = crypto
          .createHmac('sha256', webhookSecret)
          .update(stringToSign)
          .digest('hex');

        // Compare signatures (use a constant-time comparison in production)
        const isValid = computedSignature === signature;

        if (!isValid) {
          this.logger.warn('Invalid webhook signature');
        }

        return isValid;
      } catch (error) {
        this.logger.error(`Signature verification error: ${error.message}`);
        return false;
      }
    }

    private async handleUserCreated(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing user.created for ${userId}`);

        // First check if this user already exists in our database
        let user = await this.usersService.findByDynamicUserId(userId);

        if (user) {
          this.logger.log(`User ${userId} already exists in database, updating`);
          await this.updateExistingUser(user, data);
          return;
        }

        // Extract authentication info
        const { email, verifiedCredentials = [] } = data;

        // Determine auth provider based on verified credentials
        let authProvider = AuthProvider.EMAIL;
        if (email && email.toLowerCase().includes('@gmail.com')) {
          authProvider = AuthProvider.GOOGLE;
        }

        // Extract wallet info if available
        const wallets = this.extractWallets(verifiedCredentials);
        if (wallets.length > 0) {
          authProvider = AuthProvider.WALLET;
        }

        // Check if user exists by email
        if (email) {
          user = await this.usersService.findByEmail(email);

          if (user) {
            this.logger.log(`Found existing user by email ${email}, updating Dynamic ID`);
            await this.usersService.update(user.id, {
              dynamicUserId: userId,
              wallets: wallets.length > 0 ? wallets : user.wallets,
              lastLogin: new Date(),
            });
            return;
          }
        }

        // Check if user exists by wallet address
        if (wallets.length > 0) {
          for (const wallet of wallets) {
            const userByWallet = await this.usersService.findByWalletAddress(wallet.address);

            if (userByWallet) {
              this.logger.log(`Found existing user by wallet ${wallet.address}, updating Dynamic ID`);
              await this.usersService.update(userByWallet.id, {
                dynamicUserId: userId,
                email: email || userByWallet.email,
                lastLogin: new Date(),
              });
              return;
            }
          }
        }

        // Create new user if not found by any method
        this.logger.log(`Creating new user for Dynamic ID ${userId}`);

        const displayName = data.displayName || (email ? email.split('@')[0] : userId.substring(0, 8));
            console.log(data , 'user data');

        const newUser = await this.usersService.create({
          email,
          dynamicUserId: userId,
          role: UserRole.FAN, // Default role is fan
          displayName,
          authProvider,
          wallets,
          lastLogin: new Date(),
          // Extract any user profile fields that might be present
          firstName: data.firstName,
          lastName: data.lastName,
          username: data.username,
          metadata: {
            viewMode: 'fan', // Default viewMode is fan
            createdAt: new Date(),
            dynamic: {
              firstVisit: data.firstVisit,
              lastVisit: data.lastVisit,
              lists: data.lists,
            }
          }
        });

        // Send welcome email for fans
        // if (newUser.role === UserRole.FAN) {
          await this.emailService.sendFanWelcomeEmail(
            newUser.email,
            newUser.displayName || newUser.firstName || newUser.email.split('@')[0]
          );
        // }

        this.logger.log(`Created new user ${newUser.id} for Dynamic ID ${userId}`);
      } catch (error) {
        this.logger.error(`Error in handleUserCreated: ${error.message}`, error.stack);
        throw error;
      }
    }

    private async handleUserUpdated(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing user.updated for ${userId}`);

        const user = await this.usersService.findByDynamicUserId(userId);

        if (!user) {
          this.logger.warn(`User ${userId} not found, creating from update event`);
          await this.handleUserCreated(userId, data);
          return;
        }

        await this.updateExistingUser(user, data);
      } catch (error) {
        this.logger.error(`Error in handleUserUpdated: ${error.message}`, error.stack);
        throw error;
      }
    }

    private async updateExistingUser(user: any, data: any): Promise<void> {
      const updateData: any = {
        lastLogin: new Date(),
      };
      console.log(data,'data');

      // Only update fields that are present in the data
      if (data.email) updateData.email = data.email;
      if (data.displayName) updateData.displayName = data.displayName;
      if (data.firstName) updateData.firstName = data.firstName;
      if (data.lastName) updateData.lastName = data.lastName;
      if (data.username) updateData.username = data.username;

      // Update metadata
      updateData.metadata = {
        ...user.metadata,
        dynamic: {
          ...(user.metadata?.dynamic || {}),
          lastVisit: data.lastVisit,
          lists: data.lists,
        }
      };

      // Extract wallet info if available
      if (data.verifiedCredentials && data.verifiedCredentials.length > 0) {
        const wallets = this.extractWallets(data.verifiedCredentials);

        if (wallets.length > 0) {
          // Merge with existing wallets, avoiding duplicates
          const existingWallets = user.wallets || [];
          const existingAddresses = new Set(existingWallets.map(w => w.address.toLowerCase()));

          const newWallets = wallets.filter(w => !existingAddresses.has(w.address.toLowerCase()));

          if (newWallets.length > 0) {
            updateData.wallets = [...existingWallets, ...newWallets];
          }
        }
      }

      await this.usersService.update(user.id, updateData);
      this.logger.log(`Updated user ${user.id}`);
    }

    private async handleWalletLinked(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing wallet.linked for ${userId}`);

        const user = await this.usersService.findByDynamicUserId(userId);

        if (!user) {
          this.logger.warn(`User ${userId} not found for wallet linking`);
          return;
        }

        const { address, chainId = 1, provider = 'unknown' } = data.wallet || {};

        if (!address) {
          this.logger.warn(`No wallet address in wallet.linked event`);
          return;
        }

        const existingWallets = user.wallets || [];
        const walletExists = existingWallets.some(w =>
          w.address.toLowerCase() === address.toLowerCase()
        );

        if (!walletExists) {
          const updatedWallets = [
            ...existingWallets,
            { address, chainId, provider }
          ];

          await this.usersService.update(user.id, { wallets: updatedWallets });
          this.logger.log(`Added wallet ${address} to user ${user.id}`);
        }
      } catch (error) {
        this.logger.error(`Error in handleWalletLinked: ${error.message}`, error.stack);
        throw error;
      }
    }

    private async handleWalletUnlinked(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing wallet.unlinked for ${userId}`);

        const user = await this.usersService.findByDynamicUserId(userId);

        if (!user) {
          this.logger.warn(`User ${userId} not found for wallet unlinking`);
          return;
        }

        const { address } = data.wallet || {};

        if (!address) {
          this.logger.warn(`No wallet address in wallet.unlinked event`);
          return;
        }

        const existingWallets = user.wallets || [];
        const updatedWallets = existingWallets.filter(w =>
          w.address.toLowerCase() !== address.toLowerCase()
        );

        if (updatedWallets.length !== existingWallets.length) {
          await this.usersService.update(user.id, { wallets: updatedWallets });
          this.logger.log(`Removed wallet ${address} from user ${user.id}`);
        }
      } catch (error) {
        this.logger.error(`Error in handleWalletUnlinked: ${error.message}`, error.stack);
        throw error;
      }
    }

    private async handleSessionCreated(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing user.session.created for ${userId}`);

        const user = await this.usersService.findByDynamicUserId(userId);

        if (!user) {
          this.logger.warn(`User ${userId} not found for session creation`);
          return;
        }

        // Update last login time
        await this.usersService.update(user.id, { lastLogin: new Date() });
        this.logger.log(`Updated last login for user ${user.id}`);
      } catch (error) {
        this.logger.error(`Error in handleSessionCreated: ${error.message}`, error.stack);
        throw error;
      }
    }

    private async handleWalletCreated(userId: string, data: any): Promise<void> {
      try {
        this.logger.log(`Processing wallet.created for ${userId}`);

        // This is similar to wallet.linked but might have slightly different data structure
        const user = await this.usersService.findByDynamicUserId(userId);

        if (!user) {
          this.logger.warn(`User ${userId} not found for wallet creation`);
          return;
        }

        const walletData = data.wallet || data;
        const { address, chainId = 1, provider = 'unknown' } = walletData;

        if (!address) {
          this.logger.warn(`No wallet address in wallet.created event`);
          return;
        }

        const existingWallets = user.wallets || [];
        const walletExists = existingWallets.some(w =>
          w.address.toLowerCase() === address.toLowerCase()
        );

        if (!walletExists) {
          const updatedWallets = [
            ...existingWallets,
            { address, chainId, provider }
          ];

          await this.usersService.update(user.id, { wallets: updatedWallets });
          this.logger.log(`Added new wallet ${address} to user ${user.id}`);
        }
      } catch (error) {
        this.logger.error(`Error in handleWalletCreated: ${error.message}`, error.stack);
        throw error;
      }
    }

    private extractWallets(verifiedCredentials: any[]): Array<{ address: string, chainId: number, provider: string }> {
      if (!verifiedCredentials || !Array.isArray(verifiedCredentials)) {
        return [];
      }

      return verifiedCredentials
        .filter(cred =>
          cred.format === 'blockchain' ||
          (cred.address && (cred.chain || cred.publicIdentifier))
        )
        .map(cred => ({
          address: cred.address || cred.publicIdentifier,
          chainId: this.getChainId(cred.chain),
          provider: cred.walletProvider || cred.walletName || 'unknown',
        }));
    }

    private getChainId(chain: string): number {
      // Map chain identifiers to chain IDs
      const chainMap: Record<string, number> = {
        'eip155': 1, // Ethereum Mainnet
        'eip155:1': 1, // Ethereum Mainnet
        'eip155:5': 5, // Goerli Testnet
        'eip155:137': 137, // Polygon Mainnet
        'solana': 999, // Solana (using arbitrary ID)
        'flow': 998, // Flow (using arbitrary ID)
      };

      return chainMap[chain] || 1; // Default to Ethereum Mainnet
    }
  }
