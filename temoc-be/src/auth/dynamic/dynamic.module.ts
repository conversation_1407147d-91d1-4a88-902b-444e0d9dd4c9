// import { Module } from '@nestjs/common';
// import { DynamicService } from './dynamic.service';
// import { DynamicController } from './dynamic.controller';
// import { ConfigModule } from '@nestjs/config';
// import { UsersModule } from '../../users/users.module';

// @Module({
//   imports: [ConfigModule, UsersModule],
//   controllers: [DynamicController],
//   providers: [DynamicService],
//   exports: [DynamicService],
// })
// export class DynamicModule {}