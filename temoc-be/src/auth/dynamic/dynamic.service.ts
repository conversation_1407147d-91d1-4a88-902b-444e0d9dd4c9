// import { Injectable, Logger } from '@nestjs/common';
// import { ConfigService } from '@nestjs/config';
// import { UsersService } from '../../users/users.service';
// import { AuthProvider, User, UserRole } from '../../users/schemas/user.schema';
// import { DynamicSDK } from '@dynamic-labs/sdk-server-node';


// @Injectable()
// export class DynamicService {
//   private readonly logger = new Logger(DynamicService.name);
//   private readonly dynamicSDK: DynamicSDK;

//   constructor(
//     private readonly configService: ConfigService,
//     private readonly usersService: UsersService,
//   ) {
//     const apiKey = this.configService.get<string>('dynamic.apiKey');
//     this.dynamicSDK = new DynamicSDK({ apiKey });
//   }

//   async validateWebhookSignature(signature: string, body: string): Promise<boolean> {
//     const webhookSecret = this.configService.get<string>('dynamic.webhookSecret');
//     try {
//       return this.dynamicSDK.verifyWebhookSignature({
//         signature,
//         body,
//         webhookSecret,
//       });
//     } catch (error) {
//       this.logger.error(`Webhook signature validation failed: ${error.message}`);
//       return false;
//     }
//   }

//   async handleUserCreated(payload: any): Promise<User> {
//     this.logger.log(`Processing user created webhook: ${JSON.stringify(payload)}`);
    
//     const { user } = payload;
//     const { id: dynamicUserId, email, wallets = [] } = user;
    
//     // Check if user already exists
//     let existingUser = await this.usersService.findByEmail(email);
    
//     if (!existingUser) {
//       // Determine auth provider
//       let authProviders: AuthProvider[] = [];
//       if (user.verifiedCredentials?.some(cred => cred.id === 'email')) {
//         authProviders.push(AuthProvider.EMAIL);
//       }
//       if (user.verifiedCredentials?.some(cred => cred.id === 'google')) {
//         authProviders.push(AuthProvider.GOOGLE);
//       }
//       if (wallets.length > 0) {
//         authProviders.push(AuthProvider.WALLET);
//       }

//       // Create new user
//       existingUser = await this.usersService.create({
//         email,
//         dynamicUserId,
//         authProviders,
//         displayName: user.alias || email.split('@')[0],
//         avatarUrl: user.profileImage || '',
//         role: UserRole.FAN,
//       });

//       // Add wallets if any
//       for (const wallet of wallets) {
//         await this.usersService.addWallet(existingUser.id, {
//           address: wallet.address,
//           chainId: wallet.chainId,
//           provider: wallet.connector || '',
//         });
//       }
//     } else {
//       // Update existing user with Dynamic user ID
//       await this.usersService.update(existingUser.id, {
//         dynamicUserId,
//         avatarUrl: user.profileImage || existingUser.avatarUrl,
//       });
//     }

//     return existingUser;
//   }

//   async handleWalletConnected(payload: any): Promise<User> {
//     this.logger.log(`Processing wallet connected webhook: ${JSON.stringify(payload)}`);
    
//     const { wallet, userId: dynamicUserId } = payload;
    
//     // Find user by Dynamic ID
//     const user = await this.usersService.findByDynamicUserId(dynamicUserId);
    
//     if (!user) {
//       this.logger.error(`User not found for Dynamic ID: ${dynamicUserId}`);
//       return null;
//     }
    
//     // Add wallet to user
//     return this.usersService.addWallet(user.id, {
//       address: wallet.address,
//       chainId: wallet.chainId,
//       provider: wallet.connector || '',
//     });
//   }

//   async verifyDynamicToken(token: string): Promise<any> {
//     try {
//       const result = await this.dynamicSDK.verifyToken(token);
//       return result;
//     } catch (error) {
//       this.logger.error(`Token verification failed: ${error.message}`);
//       return null;
//     }
//   }
// }