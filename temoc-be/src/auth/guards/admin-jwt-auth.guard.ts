import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class AdminJwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authorization = request.headers.authorization;

    if (!authorization) {
      throw new UnauthorizedException("Missing authorization header");
    }

    const token = authorization.split(" ")[1];
    if (!token) {
      throw new UnauthorizedException("Missing token");
    }

    try {
      // Verify JWT using HS256 algorithm for admin tokens
      const jwtSecret = this.configService.get<string>('JWT_SECRET');
      const decoded = this.jwtService.verify(token, {
        secret: jwtSecret,
        algorithms: ["HS256"],
      });

      // Validate that this is an admin token
      if (!decoded.isAdmin) {
        throw new UnauthorizedException("Not an admin token");
      }

      // Create admin user object from JWT payload
      const adminUser = {
        _id: decoded.sub,
        sub: decoded.sub,
        email: decoded.email,
        role: decoded.role,
        isAdmin: decoded.isAdmin,
      };

      request.user = adminUser; // attach admin user to request
      return true;
    } catch (error) {
      console.log("Admin JWT verification error:", error);
      throw new UnauthorizedException("Invalid admin token");
    }
  }
}
