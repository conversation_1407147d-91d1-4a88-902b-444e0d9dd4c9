import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { jwtConstants } from "src/constants/jwt.constant";
import { UsersService } from "src/users/users.service";

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly userService: UsersService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authorization = request.headers.authorization;

    console.log("JWT Auth Guard - Authorization header present:", !!authorization);

    if (!authorization) {
      throw new UnauthorizedException("Missing authorization header");
    }

    const token = authorization.split(" ")[1];
    if (!token) {
      throw new UnauthorizedException("Missing token");
    }

    console.log("JWT Auth Guard - Token extracted:", !!token);

    try {
      // Verify JWT
      const decoded = this.jwtService.verify(token, {
        secret: jwtConstants.publicKey,
        algorithms: ["RS256"],
      });

      console.log("JWT Auth Guard - Token decoded:", decoded);

      // Look up user from DB using decoded.sub
      const user = await this.userService.findOne({
        dynamicUserId: decoded.sub,
      });

      console.log("JWT Auth Guard - User found:", !!user);
      console.log("JWT Auth Guard - User ID:", user?._id);

      if (!user) {
        throw new UnauthorizedException("User not found");
      }

      request.user = user; // attach DB user to request
      return true;
    } catch (error) {
      console.log("JWT verification error:", error);
      throw new UnauthorizedException("Invalid token");
    }
  }

}
