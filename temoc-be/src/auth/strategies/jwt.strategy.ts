// src/auth/strategies/jwt.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    const secret = configService.get<string>('JWT_SECRET');
    
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
      algorithms: ['HS256'], // Explicitly specify algorithm
    });
  }

  async validate(payload: any) {
    return { 
      _id: payload.sub,
      userId: payload.sub, 
      sub: payload.sub,
      email: payload.email, 
      role: payload.role,
      isAdmin: payload.isAdmin,
      dynamicUserId: payload.dynamicUserId 
    };
  }
}