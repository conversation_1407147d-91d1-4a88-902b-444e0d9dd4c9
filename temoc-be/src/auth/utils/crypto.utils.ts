
import * as crypto from 'crypto';

export class CryptoUtils {
  /**
   * Verify HMAC signature
   */
  static verifyHmacSignature(
    payload: any, 
    signature: string, 
    secret: string, 
    timestamp?: string
  ): boolean {
    try {
      const stringToSign = timestamp 
        ? `${timestamp}.${JSON.stringify(payload)}`
        : JSON.stringify(payload);
      
      const computedSignature = crypto
        .createHmac('sha256', secret)
        .update(stringToSign)
        .digest('hex');
      
      return crypto.timingSafeEqual(
        Buffer.from(computedSignature), 
        Buffer.from(signature)
      );
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Generate a random string for CSRF tokens, etc.
   */
  static generateRandomToken(length = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
}