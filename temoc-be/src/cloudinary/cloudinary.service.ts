
import { Injectable, Logger } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CloudinaryService {
  private readonly logger = new Logger(CloudinaryService.name);

  constructor(private configService: ConfigService) {
    // Initialize cloudinary configuration
    cloudinary.config({
      cloud_name: configService.get<string>('CLOUDINARY_CLOUD_NAME'),
      api_key: configService.get<string>('CLOUDINARY_API_KEY'),
      api_secret: configService.get<string>('CLOUDINARY_API_SECRET'),
    });
  }
  async uploadAudio(
    file: Express.Multer.File,
    folderName: string = 'temoc-tracks'
  ): Promise<any> {
    try {
      return new Promise((resolve, reject) => {
        const uploadStream = cloudinary.uploader.upload_stream(
          { 
            folder: folderName,
            resource_type: 'auto' // This is crucial - allows automatic detection of resource type
          },
          (error, result) => {
            if (error) return reject(error);
            resolve(result);
          }
        );
  
        // Pass the file buffer to the upload stream
        uploadStream.end(file.buffer);
      });
    } catch (error) {
      this.logger.error(`Error uploading audio to Cloudinary: ${error.message}`);
      throw error;
    }
  }
  async uploadImage(
    file: Express.Multer.File,
    folderName: string = 'temoc'
  ): Promise<any> {
    try {
      return new Promise((resolve, reject) => {
        const uploadStream = cloudinary.uploader.upload_stream(
          { folder: folderName },
          (error, result) => {
            if (error) return reject(error);
            resolve(result);
          }
        );

        // Pass the file buffer to the upload stream
        uploadStream.end(file.buffer);
      });
    } catch (error) {
      this.logger.error(`Error uploading image to Cloudinary: ${error.message}`);
      throw error;
    }
  }

  async deleteImage(publicId: string): Promise<any> {
    try {
      return await cloudinary.uploader.destroy(publicId);
    } catch (error) {
      this.logger.error(`Error deleting image from Cloudinary: ${error.message}`);
      throw error;
    }
  }
  
  async deleteAudio(publicId: string): Promise<any> {
    try {
      return await cloudinary.uploader.destroy(publicId, { resource_type: 'video' });
    } catch (error) {
      this.logger.error(`Error deleting audio from Cloudinary: ${error.message}`);
      throw error;
    }
  }
}
