import { BadRequestException } from '@nestjs/common';

export const DEMO_TRACK_FILE_TYPES = [
  'audio/mp3', 
  'audio/mpeg', 
  'audio/wav', 
  'audio/x-wav',
  'audio/aac',
  'audio/ogg',
  'video/mp4', 
  'video/quicktime', 
  'video/x-msvideo',
  'video/avi'
];

export const MAX_DEMO_TRACK_SIZE = 50 * 1024 * 1024; // 50MB

export function validateDemoTrackFile(file: Express.Multer.File): void {
  if (!file) {
    throw new BadRequestException('No file provided');
  }

  if (!DEMO_TRACK_FILE_TYPES.includes(file.mimetype)) {
    throw new BadRequestException(
      `Invalid file type. Supported types: ${DEMO_TRACK_FILE_TYPES.join(', ')}`
    );
  }

  if (file.size > MAX_DEMO_TRACK_SIZE) {
    throw new BadRequestException(
      `File too large. Maximum size is ${MAX_DEMO_TRACK_SIZE / (1024 * 1024)}MB`
    );
  }
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}