
export default () => ({
    port: parseInt(process.env.PORT, 10) || 3000,
    database: {
      uri: process.env.MONGODB_URL || 'mongodb://localhost/temoc', // Changed from MONGODB_URI
    },
    jwt: {
      secret: process.env.JWT_SECRET || 'your-secret-key',
      expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    },
    dynamic: {
      webhookSecret: process.env.DYNAMIC_WEBHOOK_SECRET,
      apiKey: process.env.DYNAMIC_API_KEY,
      environmentId: process.env.DYNAMIC_ENVIRONMENT_ID,
    },
  });

  // And in any other places where you directly access process.env.MONGODB_URI