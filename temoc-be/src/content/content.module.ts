import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { Library, LibrarySchema } from "./schemas/library.schema";
import { Album, AlbumSchema } from "./schemas/album.schema";
import { Track, TrackSchema } from "./schemas/track.schema";
import { ContentService } from "./content.service";
import { ContentController } from "./content.controller";
import { CloudinaryModule } from "../cloudinary/cloudinary.module";
import { UsersModule } from "../users/users.module";
import { AuthModule } from "../auth/auth.module";
import { EmailModule } from "../email/email.module";
import { TrackInteractionController } from "./track-interaction.controller";
import { TrackInteractionService } from "./track-interaction.service";
import { TrackLike, TrackLikeSchema, TrackComment, TrackCommentSchema } from "./schemas/track-interaction.schema";
import { User, UserSchema } from "../users/schemas/user.schema";
import { DebugCommentsController } from "./debug-comments.controller";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Library.name, schema: LibrarySchema },
      { name: Album.name, schema: AlbumSchema },
      { name: Track.name, schema: TrackSchema },
      { name: TrackLike.name, schema: TrackLikeSchema },
      { name: TrackComment.name, schema: TrackCommentSchema },
      { name: User.name, schema: UserSchema },
    ]),
    CloudinaryModule,
    forwardRef(() => UsersModule),
    forwardRef(() => AuthModule), // Use forwardRef to avoid circular dependency
    EmailModule,
  ],
  providers: [ContentService, TrackInteractionService],
  controllers: [ContentController, TrackInteractionController, DebugCommentsController],
  exports: [ContentService, TrackInteractionService],
})
export class ContentModule {}
