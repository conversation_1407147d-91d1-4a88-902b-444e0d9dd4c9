import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { Library } from "./schemas/library.schema";
import { Album } from "./schemas/album.schema";
import { Track, TrackPrivacy } from "./schemas/track.schema";
import { CloudinaryService } from "../cloudinary/cloudinary.service";
import { UsersService } from "../users/users.service";
import { CreateLibraryDto } from "./dto/create-library.dto";
import { CreateAlbumDto } from "./dto/create-album.dto";
import { CreateTrackDto } from "./dto/create-track.dto";
import { UserRole } from "../users/schemas/user.schema";
import { EmailService } from "../email/email.service";

@Injectable()
export class ContentService {
  private readonly logger = new Logger(ContentService.name);

  constructor(
    @InjectModel(Library.name) private libraryModel: Model<Library>,
    @InjectModel(Album.name) private albumModel: Model<Album>,
    @InjectModel(Track.name) private trackModel: Model<Track>,
    private cloudinaryService: CloudinaryService,
    private usersService: UsersService,
    private emailService: EmailService
  ) {}

  // LIBRARY METHODS
  async createLibrary(
    userId: string,
    createLibraryDto: CreateLibraryDto,
    thumbnail?: Express.Multer.File
  ): Promise<Library> {
    this.logger.log(`Creating library for user ${userId}`);

    // Check if user exists and is an artist
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can create libraries");
    }

    // Check if user has created a token
    if (!user.tokenCreated) {
      throw new BadRequestException(
        "You must create a token before creating a library"
      );
    }

    // Create library data object
    const libraryData: any = {
      ...createLibraryDto,
      creator: userId,
    };

    // Upload thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-libraries"
        );
        libraryData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(
          `Failed to upload library thumbnail: ${error.message}`
        );
        throw new Error(`Failed to upload library thumbnail: ${error.message}`);
      }
    }

    // Create and save library
    const library = new this.libraryModel(libraryData);
    const result = await library.save();

    // Send email notification
    if (user) {
      await this.emailService.sendLibraryCreationEmail(
        user.email,
        user.displayName || user.firstName || user.email.split("@")[0]
      );
    }

    return result;
  }

  async findAllLibraries(userId: string): Promise<Library[]> {
    return this.libraryModel.find({ creator: userId }).exec();
  }

  async findLibraryById(libraryId: string, userId: string): Promise<Library> {
    const library = await this.libraryModel.findById(libraryId).exec();

    if (!library) {
      throw new NotFoundException("Library not found");
    }

    // Check if the user is the creator
    if (library.creator.toString() !== userId.toString()) {
      throw new ForbiddenException("You do not have access to this library");
    }

    return library;
  }

  async updateLibrary(
    libraryId: string,
    userId: string,
    updateData: any,
    thumbnail?: Express.Multer.File
  ): Promise<Library> {
    // Verify library exists and belongs to user
    const library = await this.findLibraryById(libraryId, userId);

    // Upload new thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-libraries"
        );
        updateData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(
          `Failed to upload library thumbnail: ${error.message}`
        );
        throw new Error(`Failed to upload library thumbnail: ${error.message}`);
      }
    }

    // Update library
    return this.libraryModel
      .findByIdAndUpdate(libraryId, updateData, { new: true })
      .exec();
  }

  async deleteLibrary(libraryId: string, userId: string): Promise<void> {
    // Verify library exists and belongs to user
    const library = await this.findLibraryById(libraryId, userId);

    // Delete all albums in the library
    const albums = await this.albumModel.find({ library: libraryId }).exec();
    for (const album of albums) {
      await this.deleteAlbum(album.id, userId);
    }

    // Delete the library
    await this.libraryModel.findByIdAndDelete(libraryId).exec();
  }

  // ALBUM METHODS
  async createAlbum(
    userId: string,
    createAlbumDto: CreateAlbumDto,
    thumbnail?: Express.Multer.File
  ): Promise<Album> {
    this.logger.log(`Creating album for user ${userId}`);

    // Check if library exists and belongs to user
    const library = await this.findLibraryById(
      createAlbumDto.libraryId,
      userId
    );
    console.log(library);

    // Create album data object
    const albumData: any = {
      ...createAlbumDto,
      creator: userId,
      library: createAlbumDto.libraryId,
    };

    // Upload thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-albums"
        );
        albumData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(`Failed to upload album thumbnail: ${error.message}`);
        throw new Error(`Failed to upload album thumbnail: ${error.message}`);
      }
    }

    // Create and save album
    const album = new this.albumModel(albumData);
    const result = await album.save();

    // Send email notification
    const user = await this.usersService.findById(userId);
    if (user) {
      await this.emailService.sendAlbumCreationEmail(
        user.email,
        user.displayName || user.firstName || user.email.split("@")[0]
      );
    }

    return result;
  }

  async findAllAlbums(libraryId: string, userId: string): Promise<Album[]> {
    // Verify library exists and belongs to user
    await this.findLibraryById(libraryId, userId);

    return this.albumModel.find({ library: libraryId }).exec();
  }

  async findAlbumById(albumId: string, userId: string): Promise<Album> {
    const album = await this.albumModel.findById(albumId).exec();

    if (!album) {
      throw new NotFoundException("Album not found");
    }

    // // Check if the user is the creator
    // if (album.creator.toString() !== userId.toString()) {
    //   throw new ForbiddenException("You do not have access to this album");
    // }

    return album;
  }

  async updateAlbum(
    albumId: string,
    userId: string,
    updateData: any,
    thumbnail?: Express.Multer.File
  ): Promise<Album> {
    // Verify album exists and belongs to user
    const album = await this.findAlbumById(albumId, userId);

    // Upload new thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-albums"
        );
        updateData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(`Failed to upload album thumbnail: ${error.message}`);
        throw new Error(`Failed to upload album thumbnail: ${error.message}`);
      }
    }

    // Update album
    return this.albumModel
      .findByIdAndUpdate(albumId, updateData, { new: true })
      .exec();
  }

  async deleteAlbum(albumId: string, userId: string): Promise<void> {
    // Verify album exists and belongs to user
    const album = await this.findAlbumById(albumId, userId);

    // Delete all tracks in the album
    await this.trackModel.deleteMany({ album: albumId }).exec();

    // Delete the album
    await this.albumModel.findByIdAndDelete(albumId).exec();
  }

  // TRACK METHODS
  async createTrack(
    userId: string,
    createTrackDto: CreateTrackDto,
    audioFile: Express.Multer.File,
    thumbnail?: Express.Multer.File
  ): Promise<Track> {
    this.logger.log(`Creating track for user ${userId}`);

    // Verify album exists and belongs to user
    const album = await this.findAlbumById(createTrackDto.albumId, userId);

    // Create track data object
    const trackData: any = {
      ...createTrackDto,
      creator: userId,
      album: createTrackDto.albumId,
    };

    // Upload audio file
    try {
      const result = await this.cloudinaryService.uploadAudio(
        audioFile,
        "temoc-tracks"
      );
      trackData.fileUrl = result.secure_url;

      // Extract duration and other metadata if available
      if (result.duration) {
        trackData.duration = result.duration;
      }

      // Add file metadata
      trackData.metadata = {
        fileType: audioFile.mimetype,
        fileSize: audioFile.size,
        originalName: audioFile.originalname,
        publicId: result.public_id,
      };
    } catch (error) {
      this.logger.error(`Failed to upload track audio: ${error.message}`);
      throw new Error(`Failed to upload track audio: ${error.message}`);
    }

    // Upload thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-track-thumbnails"
        );
        trackData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(`Failed to upload track thumbnail: ${error.message}`);
        throw new Error(`Failed to upload track thumbnail: ${error.message}`);
      }
    }

    // Create and save track
    const track = new this.trackModel(trackData);
    const result = await track.save();

    // Send email notification
    const user = await this.usersService.findById(userId);
    if (user) {
      await this.emailService.sendSongAddedEmail(
        user.email,
        user.displayName || user.firstName || user.email.split("@")[0],
        createTrackDto.title
      );
    }

    return result;
  }

  async findAllTracks(albumId: string, userId: string): Promise<Track[]> {
    // Verify album exists and belongs to user
    await this.findAlbumById(albumId, userId);

    return this.trackModel.find({ album: albumId }).exec();
  }

  async findTrackById(trackId: string, userId: string): Promise<Track> {
    const track = await this.trackModel.findById(trackId).exec();

    if (!track) {
      throw new NotFoundException("Track not found");
    }

    // Check if the user is the creator
    // if (track.creator.toString() !== userId.toString()) {
    //   throw new ForbiddenException("You do not have access to this track");
    // }

    return track;
  }

  async updateTrack(
    trackId: string,
    userId: string,
    updateData: any,
    audioFile?: Express.Multer.File,
    thumbnail?: Express.Multer.File
  ): Promise<Track> {
    // Verify track exists and belongs to user
    const track = await this.findTrackById(trackId, userId);

    // Upload new audio file if provided
    if (audioFile) {
      try {
        const result = await this.cloudinaryService.uploadAudio(
          audioFile,
          "temoc-tracks"
        );
        updateData.fileUrl = result.secure_url;

        // Extract duration and other metadata if available
        if (result.duration) {
          updateData.duration = result.duration;
        }

        // Update file metadata
        updateData.metadata = {
          ...track.metadata,
          fileType: audioFile.mimetype,
          fileSize: audioFile.size,
          originalName: audioFile.originalname,
          publicId: result.public_id,
        };
      } catch (error) {
        this.logger.error(`Failed to upload track audio: ${error.message}`);
        throw new Error(`Failed to upload track audio: ${error.message}`);
      }
    }

    // Upload new thumbnail if provided
    if (thumbnail) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          thumbnail,
          "temoc-track-thumbnails"
        );
        updateData.thumbnailUrl = result.secure_url;
      } catch (error) {
        this.logger.error(`Failed to upload track thumbnail: ${error.message}`);
        throw new Error(`Failed to upload track thumbnail: ${error.message}`);
      }
    }

    // Update track
    return this.trackModel
      .findByIdAndUpdate(trackId, updateData, { new: true })
      .exec();
  }

  async deleteTrack(trackId: string, userId: string): Promise<void> {
    // Verify track exists and belongs to user
    const track = await this.findTrackById(trackId, userId);

    // Delete the track
    await this.trackModel.findByIdAndDelete(trackId).exec();
  }

  // Track play count
  async incrementTrackPlayCount(trackId: string): Promise<void> {
    await this.trackModel
      .findByIdAndUpdate(trackId, { $inc: { playCount: 1 } })
      .exec();
  }

  // FAN ACCESS METHODS
  async getPublicLibraries(): Promise<Library[]> {
    return this.libraryModel
      .find({ requiresSubscription: false })
      .populate("creator", "firstName lastName username avatarUrl")
      .exec();
  }

  async getPublicAlbums(libraryId: string): Promise<Album[]> {
    return this.albumModel
      .find({
        library: libraryId,
        requiresTokens: false,
      })
      .populate("creator", "firstName lastName username avatarUrl")
      .exec();
  }

  async getPublicTracks(albumId: string): Promise<Track[]> {
    const album = await this.albumModel.findById(albumId).exec();

    if (!album) {
      throw new NotFoundException("Album not found");
    }

    if (album.requiresTokens) {
      return []; // Return empty array for token-gated albums
    }

    return this.trackModel
      .find({
        album: albumId,
        privacy: TrackPrivacy.PUBLIC,
      })
      .populate("creator", "firstName lastName username avatarUrl")
      .exec();
  }

  async getPublicLibrariesByUser(userId: string): Promise<Library[]> {
    this.logger.log(`Getting public libraries for user ${userId}`);

    return this.libraryModel
      .find({
        creator: userId,
        requiresSubscription: false,
      })
      .populate("creator", "firstName lastName username avatarUrl")
      .exec();
  }

  async getPublicAlbumsByUser(userId: string): Promise<any[]> {
    this.logger.log(
      `Getting public albums with track counts for user ${userId}`
    );

    // Get all public libraries for the user
    const libraries = await this.libraryModel
      .find({
        creator: userId,
        requiresSubscription: false,
      })
      .select("_id")
      .exec();

    const libraryIds = libraries.map((lib) => lib._id);

    // Get albums from these libraries with track counts
    const albums = await this.albumModel.aggregate([
      {
        $match: {
          library: { $in: libraryIds },
          requiresTokens: false,
        },
      },
      {
        $lookup: {
          from: "tracks",
          let: { albumId: "$_id" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$album", "$$albumId"] },
                    { $eq: ["$privacy", "PUBLIC"] },
                  ],
                },
              },
            },
          ],
          as: "tracks",
        },
      },
      {
        $addFields: {
          trackCount: { $size: "$tracks" },
        },
      },
      {
        $match: {
          trackCount: { $gt: 0 }, // Only include albums with tracks
        },
      },
      {
        $sort: { trackCount: -1 }, // Sort by track count descending
      },
      {
        $limit: 10, // Limit to top 10 albums
      },
      {
        $lookup: {
          from: "users",
          localField: "creator",
          foreignField: "_id",
          as: "creator",
          pipeline: [
            {
              $project: {
                firstName: 1,
                lastName: 1,
                username: 1,
                avatarUrl: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: "$creator",
      },
      {
        $project: {
          tracks: 0, // Remove the tracks array from output
        },
      },
    ]);

    return albums;
  }

  async getPublicTracksByUser(userId: string): Promise<any[]> {
    this.logger.log(`Getting public tracks for user ${userId}`);

    // Get all public libraries for the user
    const libraries = await this.libraryModel
      .find({
        creator: userId,
        requiresSubscription: false,
      })
      .select("_id")
      .exec();

    const libraryIds = libraries.map((lib) => lib._id);

    // Get albums from these libraries
    const albums = await this.albumModel
      .find({
        library: { $in: libraryIds },
        requiresTokens: false,
      })
      .select("_id")
      .exec();

    const albumIds = albums.map((album) => album._id);

    // Get recent public tracks from these albums
    const tracks = await this.trackModel
      .find({
        album: { $in: albumIds },
        privacy: TrackPrivacy.PUBLIC,
      })
      .populate("creator", "firstName lastName username avatarUrl")
      .populate("album", "title")
      .sort({ createdAt: -1 }) // Sort by newest first
      .limit(20) // Limit to 20 most recent tracks
      .exec();

    return tracks;
  }
  async updateAlbumAccess(body: { albumId: string; tokensRequired: number }[]) {
    if (!Array.isArray(body) || body.length === 0) {
      throw new BadRequestException(
        "Body must be a non-empty array of albums."
      );
    }

    const updateResults = [];

    for (const item of body) {
      const { albumId, tokensRequired } = item;

      if (!albumId || tokensRequired === undefined) {
        continue; // Skip invalid items
      }

      const id = new Types.ObjectId(albumId);

      const result = await this.albumModel.findByIdAndUpdate(
        id,
        {
          requiredTokenAmount: tokensRequired,
          requiresTokens: true,
        },
        { new: true }
      );

      if (result) {
        updateResults.push(result);
      } else {
        console.warn(`Album not found: ${albumId}`);
      }
    }

    return {
      message: "Access updated for albums",
      updatedCount: updateResults.length,
      updatedAlbums: updateResults,
    };
  }

  async findByCreatorId(creatorId: string) {
    return this.albumModel
      .find({ creator: new Types.ObjectId(creatorId) })
      .exec();
  }

  async findById(id: string): Promise<Album> {
    return this.albumModel.findById(id).exec();
  }
}
