import { Controller, Get, Param } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TrackComment } from './schemas/track-interaction.schema';

@Controller('debug')
export class DebugCommentsController {
  constructor(
    @InjectModel(TrackComment.name) private trackCommentModel: Model<TrackComment>,
  ) {}

  @Get('comments/:trackId')
  async debugComments(@Param('trackId') trackId: string) {
    // Get all comments for this track (including replies)
    const allComments = await this.trackCommentModel
      .find({ track: trackId, isDeleted: false })
      .populate('user', 'username displayName')
      .sort({ createdAt: 1 })
      .lean();

    // Get top-level comments only
    const topLevelComments = await this.trackCommentModel
      .find({ 
        track: trackId, 
        isDeleted: false,
        parentComment: { $exists: false }
      })
      .populate('user', 'username displayName')
      .sort({ createdAt: 1 })
      .lean();

    // Get replies only
    const replies = await this.trackCommentModel
      .find({ 
        track: trackId, 
        isDeleted: false,
        parentComment: { $exists: true }
      })
      .populate('user', 'username displayName')
      .sort({ createdAt: 1 })
      .lean();

    return {
      trackId,
      totalComments: allComments.length,
      topLevelComments: topLevelComments.length,
      replies: replies.length,
      allComments: allComments.map(c => ({
        _id: c._id,
        content: c.content,
        user: c.user,
        parentComment: c.parentComment,
        createdAt: (c as any).createdAt
      })),
      topLevel: topLevelComments.map(c => ({
        _id: c._id,
        content: c.content,
        user: c.user,
        createdAt: (c as any).createdAt
      })),
      replyList: replies.map(c => ({
        _id: c._id,
        content: c.content,
        user: c.user,
        parentComment: c.parentComment,
        createdAt: (c as any).createdAt
      }))
    };
  }
}
