
import { IsString, IsOptional, IsBoolean, IsN<PERSON>ber, IsMongoId } from 'class-validator';

export class CreateAlbumDto {
  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsMongoId()
  libraryId: string;

  @IsBoolean()
  @IsOptional()
  requiresTokens?: boolean;

  @IsNumber()
  @IsOptional()
  requiredTokenAmount?: number;

  @IsBoolean()
  @IsOptional()
  freeToListen?: boolean;
}