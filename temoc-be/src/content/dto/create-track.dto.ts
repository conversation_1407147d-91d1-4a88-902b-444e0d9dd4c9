
import { IsString, IsOptional, IsEnum, IsArray, IsMongoId } from 'class-validator';
import { TrackPrivacy } from '../schemas/track.schema';
// import { TrackPrivacy } from '../schemas/track.schema';

export class CreateTrackDto {
  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  artist?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  genre?: string;

  @IsArray()
  @IsOptional()
  tags?: string[];

  @IsEnum(TrackPrivacy)
  @IsOptional()
  privacy?: TrackPrivacy;

  @IsMongoId()
  albumId: string;
}