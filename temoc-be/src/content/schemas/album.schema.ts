
import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Library } from './library.schema';

@Schema({ timestamps: true })
export class Album extends Document {
  @Prop({ required: true })
  title: string;

  @Prop()
  description: string;

  @Prop()
  thumbnailUrl: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  creator: User | MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Library', required: true })
  library: Library | MongooseSchema.Types.ObjectId;

  @Prop({ default: false })
  requiresTokens: boolean;

  @Prop({ default: 0 })
  requiredTokenAmount: number;

  @Prop({ default: false })
  freeToListen: boolean;

  @Prop({ type: Object })
  metadata: Record<string, any>; // For any additional metadata
}

export const AlbumSchema = SchemaFactory.createForClass(Album);