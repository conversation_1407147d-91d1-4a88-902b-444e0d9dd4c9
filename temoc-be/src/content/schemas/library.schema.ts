
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';

@Schema({ timestamps: true })
export class Library extends Document {
  @Prop({ required: true })
  title: string;

  @Prop()
  description: string;

  @Prop()
  type: string; // Type of collection (e.g., Music, Video)

  @Prop()
  genre: string;

  @Prop()
  thumbnailUrl: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  creator: User | MongooseSchema.Types.ObjectId;

  @Prop({ default: false })
  requiresSubscription: boolean;

  @Prop({ type: Object })
  metadata: Record<string, any>; // For any additional metadata
}

export const LibrarySchema = SchemaFactory.createForClass(Library);