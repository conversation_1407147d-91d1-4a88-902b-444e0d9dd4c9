import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Track } from './track.schema';

// Track Like Schema
@Schema({ timestamps: true })
export class TrackLike extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Track', required: true })
  track: Track | MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  user: User | MongooseSchema.Types.ObjectId;
}

export const TrackLikeSchema = SchemaFactory.createForClass(TrackLike);

// Track Comment Schema
@Schema({ timestamps: true })
export class TrackComment extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Track', required: true })
  track: Track | MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  user: User | MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  content: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'TrackComment' })
  parentComment?: TrackComment | MongooseSchema.Types.ObjectId;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'User' }], default: [] })
  mentions: (User | MongooseSchema.Types.ObjectId)[];

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'User' }], default: [] })
  likes: (User | MongooseSchema.Types.ObjectId)[];

  @Prop({ default: false })
  isEdited: boolean;

  @Prop()
  editedAt: Date;

  @Prop({ default: false })
  isDeleted: boolean;

  @Prop()
  deletedAt: Date;
}

export const TrackCommentSchema = SchemaFactory.createForClass(TrackComment);

// Create compound indexes for better performance
TrackLikeSchema.index({ track: 1, user: 1 }, { unique: true });
TrackCommentSchema.index({ track: 1, createdAt: -1 });
TrackCommentSchema.index({ parentComment: 1, createdAt: 1 });
