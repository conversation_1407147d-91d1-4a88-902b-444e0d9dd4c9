
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from '../../users/schemas/user.schema';
import { Album } from './album.schema';

export enum TrackPrivacy {
  PUBLIC = 'public',
  PRIVATE = 'private',
}

@Schema({ timestamps: true })
export class Track extends Document {
  @Prop({ required: true })
  title: string;

  @Prop()
  description: string;

  @Prop()
  artist: string;

  @Prop()
  genre: string;

  @Prop({ type: [String] })
  tags: string[];

  @Prop()
  fileUrl: string;

  @Prop()
  thumbnailUrl: string;

  @Prop()
  duration: number;

  @Prop({ enum: Object.values(TrackPrivacy), default: TrackPrivacy.PUBLIC })
  privacy: TrackPrivacy;

  @Prop({ default: 0 })
  playCount: number;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  creator: User | MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Album', required: true })
  album: Album | MongooseSchema.Types.ObjectId;

  @Prop({ type: Object })
  metadata: Record<string, any>; // For audio metadata, file info, etc.
}

export const TrackSchema = SchemaFactory.createForClass(Track);