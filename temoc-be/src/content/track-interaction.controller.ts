import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TrackInteractionService } from './track-interaction.service';
import { CreateCommentDto, UpdateCommentDto, GetCommentsDto } from './dto/track-interaction.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthUser } from '../auth/decorators/user.decorator';

@ApiTags('Track Interactions')
@ApiBearerAuth('JWT-auth')
@Controller('tracks')
export class TrackInteractionController {
  constructor(private readonly trackInteractionService: TrackInteractionService) {}

  // LIKE ENDPOINTS
  @UseGuards(JwtAuthGuard)
  @Post(':trackId/like')
  @ApiOperation({ summary: 'Toggle like on a track' })
  @ApiResponse({ status: 200, description: 'Like toggled successfully' })
  async toggleLike(@Param('trackId') trackId: string, @AuthUser() user: any) {
    return this.trackInteractionService.toggleLike(trackId, user._id);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':trackId/like-status')
  @ApiOperation({ summary: 'Get user like status for a track' })
  @ApiResponse({ status: 200, description: 'Like status retrieved' })
  async getLikeStatus(@Param('trackId') trackId: string, @AuthUser() user: any) {
    return this.trackInteractionService.getLikeStatus(trackId, user._id);
  }

  @Get(':trackId/likes-count')
  @ApiOperation({ summary: 'Get total likes count for a track' })
  @ApiResponse({ status: 200, description: 'Likes count retrieved' })
  async getLikesCount(@Param('trackId') trackId: string) {
    return this.trackInteractionService.getLikesCount(trackId);
  }

  // COMMENT ENDPOINTS
  @UseGuards(JwtAuthGuard)
  @Post(':trackId/comments')
  @ApiOperation({ summary: 'Create a comment on a track' })
  @ApiResponse({ status: 201, description: 'Comment created successfully' })
  async createComment(
    @Param('trackId') trackId: string,
    @AuthUser() user: any,
    @Body() createCommentDto: CreateCommentDto,
  ) {
    return this.trackInteractionService.createComment(trackId, user._id, createCommentDto);
  }

  @Get(':trackId/comments')
  @ApiOperation({ summary: 'Get comments for a track' })
  @ApiResponse({ status: 200, description: 'Comments retrieved successfully' })
  async getComments(
    @Param('trackId') trackId: string,
    @Query() getCommentsDto: GetCommentsDto,
  ) {
    console.log(`[DEBUG CONTROLLER] GET /tracks/${trackId}/comments called with:`, getCommentsDto);
    const result = await this.trackInteractionService.getComments(trackId, getCommentsDto);
    console.log(`[DEBUG CONTROLLER] Returning ${result.comments.length} comments`);
    return result;
  }

  @UseGuards(JwtAuthGuard)
  @Put('comments/:commentId')
  @ApiOperation({ summary: 'Update a comment' })
  @ApiResponse({ status: 200, description: 'Comment updated successfully' })
  async updateComment(
    @Param('commentId') commentId: string,
    @AuthUser() user: any,
    @Body() updateCommentDto: UpdateCommentDto,
  ) {
    console.log(`[DEBUG CONTROLLER UPDATE] User object:`, user);
    console.log(`[DEBUG CONTROLLER UPDATE] User._id:`, user._id, `Type: ${typeof user._id}`);
    return this.trackInteractionService.updateComment(commentId, user._id.toString(), updateCommentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('comments/:commentId')
  @ApiOperation({ summary: 'Delete a comment' })
  @ApiResponse({ status: 200, description: 'Comment deleted successfully' })
  async deleteComment(@Param('commentId') commentId: string, @AuthUser() user: any) {
    console.log(`[DEBUG CONTROLLER DELETE] User object:`, user);
    console.log(`[DEBUG CONTROLLER DELETE] User._id:`, user._id, `Type: ${typeof user._id}`);
    return this.trackInteractionService.deleteComment(commentId, user._id.toString());
  }

  @UseGuards(JwtAuthGuard)
  @Post('comments/:commentId/like')
  @ApiOperation({ summary: 'Toggle like on a comment' })
  @ApiResponse({ status: 200, description: 'Comment like toggled successfully' })
  async toggleCommentLike(@Param('commentId') commentId: string, @AuthUser() user: any) {
    return this.trackInteractionService.toggleCommentLike(commentId, user._id);
  }

  @Get(':trackId/stats')
  @ApiOperation({ summary: 'Get track interaction stats' })
  @ApiResponse({ status: 200, description: 'Track stats retrieved successfully' })
  async getTrackStats(@Param('trackId') trackId: string, @Query('userId') userId?: string) {
    return this.trackInteractionService.getTrackStats(trackId, userId);
  }
}
