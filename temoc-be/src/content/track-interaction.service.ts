import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { TrackLike, TrackComment } from './schemas/track-interaction.schema';
import { Track } from './schemas/track.schema';
import { User } from '../users/schemas/user.schema';
import { CreateCommentDto, UpdateCommentDto, GetCommentsDto } from './dto/track-interaction.dto';

@Injectable()
export class TrackInteractionService {
  constructor(
    @InjectModel(TrackLike.name) private trackLikeModel: Model<TrackLike>,
    @InjectModel(TrackComment.name) private trackCommentModel: Model<TrackComment>,
    @InjectModel(Track.name) private trackModel: Model<Track>,
    @InjectModel(User.name) private userModel: Model<User>,
  ) {}

  // LIKE FUNCTIONALITY
  async toggleLike(trackId: string, userId: string) {
    const trackObjectId = new Types.ObjectId(trackId);
    const userObjectId = new Types.ObjectId(userId);

    // Check if track exists
    const track = await this.trackModel.findById(trackObjectId);
    if (!track) {
      throw new NotFoundException('Track not found');
    }

    // Check if like already exists
    const existingLike = await this.trackLikeModel.findOne({
      track: trackObjectId,
      user: userObjectId,
    });

    if (existingLike) {
      // Unlike
      await this.trackLikeModel.deleteOne({ _id: existingLike._id });
      return { liked: false, message: 'Track unliked successfully' };
    } else {
      // Like
      const newLike = new this.trackLikeModel({
        track: trackObjectId,
        user: userObjectId,
      });
      await newLike.save();
      return { liked: true, message: 'Track liked successfully' };
    }
  }

  async getLikeStatus(trackId: string, userId: string) {
    const like = await this.trackLikeModel.findOne({
      track: new Types.ObjectId(trackId),
      user: new Types.ObjectId(userId),
    });
    return { liked: !!like };
  }

  async getLikesCount(trackId: string) {
    const count = await this.trackLikeModel.countDocuments({
      track: new Types.ObjectId(trackId),
    });
    return { count };
  }

  // COMMENT FUNCTIONALITY
  async createComment(trackId: string, userId: string, createCommentDto: CreateCommentDto) {
    const trackObjectId = new Types.ObjectId(trackId);
    const userObjectId = new Types.ObjectId(userId);

    // Check if track exists
    const track = await this.trackModel.findById(trackObjectId);
    if (!track) {
      throw new NotFoundException('Track not found');
    }

    // If it's a reply, check if parent comment exists
    if (createCommentDto.parentComment) {
      const parentComment = await this.trackCommentModel.findById(createCommentDto.parentComment);
      if (!parentComment || parentComment.track.toString() !== trackId) {
        throw new BadRequestException('Parent comment not found or belongs to different track');
      }
    }

    // Validate mentioned users
    let mentionedUsers: Types.ObjectId[] = [];
    if (createCommentDto.mentions && createCommentDto.mentions.length > 0) {
      const users = await this.userModel.find({
        _id: { $in: createCommentDto.mentions.map(id => new Types.ObjectId(id)) }
      });
      mentionedUsers = users.map(user => user._id as Types.ObjectId);
    }

    const comment = new this.trackCommentModel({
      track: trackObjectId,
      user: userObjectId,
      content: createCommentDto.content,
      parentComment: createCommentDto.parentComment ? new Types.ObjectId(createCommentDto.parentComment) : undefined,
      mentions: mentionedUsers as any,
    });

    await comment.save();
    
    // Populate user data for response
    await comment.populate('user', 'username displayName avatarUrl');
    await comment.populate('mentions', 'username displayName');

    return comment;
  }

  // Helper method to recursively get all replies for a comment
  private async getAllRepliesRecursive(commentId: Types.ObjectId): Promise<any[]> {
    console.log(`[DEBUG] Getting replies for comment: ${commentId}`);

    const directReplies = await this.trackCommentModel
      .find({
        parentComment: commentId,
        isDeleted: false,
      })
      .populate('user', 'username displayName avatarUrl')
      .populate('mentions', 'username displayName')
      .sort({ createdAt: 1 })
      .lean();

    console.log(`[DEBUG] Found ${directReplies.length} direct replies for comment ${commentId}`);

    const allReplies = [];
    for (const reply of directReplies) {
      // Add the reply itself
      allReplies.push(reply);
      console.log(`[DEBUG] Added reply: ${reply._id} with content: "${reply.content}"`);

      // Recursively get replies to this reply
      const nestedReplies = await this.getAllRepliesRecursive(reply._id as Types.ObjectId);
      console.log(`[DEBUG] Found ${nestedReplies.length} nested replies for reply ${reply._id}`);
      allReplies.push(...nestedReplies);
    }

    console.log(`[DEBUG] Total replies for comment ${commentId}: ${allReplies.length}`);
    return allReplies;
  }

  async getComments(trackId: string, getCommentsDto: GetCommentsDto) {
    console.log(`[DEBUG SERVICE] getComments called for track ${trackId} with:`, getCommentsDto);

    const page = parseInt(getCommentsDto.page) || 1;
    const limit = parseInt(getCommentsDto.limit) || 20;
    const skip = (page - 1) * limit;

    const filter: any = {
      track: new Types.ObjectId(trackId),
      isDeleted: false,
    };

    // If parentComment is provided, get ALL replies in that thread recursively
    if (getCommentsDto.parentComment) {
      console.log(`[DEBUG SERVICE] Getting replies for parent comment: ${getCommentsDto.parentComment}`);
      const allReplies = await this.getAllRepliesRecursive(new Types.ObjectId(getCommentsDto.parentComment));
      console.log(`[DEBUG SERVICE] Total recursive replies found: ${allReplies.length}`);

      // Apply pagination to the recursive results
      const paginatedReplies = allReplies.slice(skip, skip + limit);
      console.log(`[DEBUG SERVICE] Returning ${paginatedReplies.length} paginated replies`);

      return {
        comments: paginatedReplies,
        pagination: {
          page,
          limit,
          total: allReplies.length,
          pages: Math.ceil(allReplies.length / limit),
        },
      };
    } else {
      // Get top-level comments
      filter.parentComment = { $exists: false };

      const comments = await this.trackCommentModel
        .find(filter)
        .populate('user', 'username displayName avatarUrl')
        .populate('mentions', 'username displayName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean();

      // Get reply counts for top-level comments (count ALL replies recursively)
      for (const comment of comments) {
        const allReplies = await this.getAllRepliesRecursive(comment._id as Types.ObjectId);
        (comment as any).replyCount = allReplies.length;
      }

      const total = await this.trackCommentModel.countDocuments(filter);

      return {
        comments,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }
  }

  async updateComment(commentId: string, userId: string, updateCommentDto: UpdateCommentDto) {
    console.log(`[DEBUG UPDATE] Attempting to update comment ${commentId} by user ${userId}`);
    const comment = await this.trackCommentModel.findById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    console.log(`[DEBUG UPDATE] Comment owner: ${comment.user.toString()}, Current user: ${userId}`);
    if (comment.user.toString() !== userId) {
      console.log(`[DEBUG UPDATE] Ownership check failed - Comment owner: "${comment.user.toString()}" !== Current user: "${userId}"`);
      throw new ForbiddenException('You can only edit your own comments');
    }

    if (comment.isDeleted) {
      throw new BadRequestException('Cannot edit deleted comment');
    }

    // Validate mentioned users
    let mentionedUsers: Types.ObjectId[] = [];
    if (updateCommentDto.mentions && updateCommentDto.mentions.length > 0) {
      const users = await this.userModel.find({
        _id: { $in: updateCommentDto.mentions.map(id => new Types.ObjectId(id)) }
      });
      mentionedUsers = users.map(user => user._id as Types.ObjectId);
    }

    comment.content = updateCommentDto.content;
    comment.mentions = mentionedUsers as any;
    comment.isEdited = true;
    comment.editedAt = new Date();

    await comment.save();
    await comment.populate('user', 'username displayName avatarUrl');
    await comment.populate('mentions', 'username displayName');

    return comment;
  }

  async deleteComment(commentId: string, userId: string) {
    console.log(`[DEBUG DELETE] Attempting to delete comment ${commentId} by user ${userId}`);
    const comment = await this.trackCommentModel.findById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    console.log(`[DEBUG DELETE] Comment owner: ${comment.user.toString()}, Current user: ${userId}`);
    if (comment.user.toString() !== userId) {
      console.log(`[DEBUG DELETE] Ownership check failed - Comment owner: "${comment.user.toString()}" !== Current user: "${userId}"`);
      throw new ForbiddenException('You can only delete your own comments');
    }

    comment.isDeleted = true;
    comment.deletedAt = new Date();
    await comment.save();

    console.log(`[DEBUG DELETE] Comment ${commentId} successfully marked as deleted`);
    return { message: 'Comment deleted successfully' };
  }

  async toggleCommentLike(commentId: string, userId: string) {
    const comment = await this.trackCommentModel.findById(commentId);

    if (!comment) {
      throw new NotFoundException('Comment not found');
    }

    const userObjectId = new Types.ObjectId(userId);
    const userIndex = comment.likes.findIndex(id => id.toString() === userId);

    if (userIndex > -1) {
      // Unlike
      comment.likes.splice(userIndex, 1);
      await comment.save();
      return { liked: false, likesCount: comment.likes.length };
    } else {
      // Like
      comment.likes.push(userObjectId as any);
      await comment.save();
      return { liked: true, likesCount: comment.likes.length };
    }
  }

  async getTrackStats(trackId: string, userId?: string) {
    const likesCount = await this.trackLikeModel.countDocuments({
      track: new Types.ObjectId(trackId),
    });

    const commentsCount = await this.trackCommentModel.countDocuments({
      track: new Types.ObjectId(trackId),
      isDeleted: false,
    });

    console.log(`[DEBUG STATS] Track ${trackId} - Comments count: ${commentsCount} (excluding deleted)`);

    let userLiked = false;
    if (userId) {
      const userLike = await this.trackLikeModel.findOne({
        track: new Types.ObjectId(trackId),
        user: new Types.ObjectId(userId),
      });
      userLiked = !!userLike;
    }

    return {
      likesCount,
      commentsCount,
      userLiked,
    };
  }
}
