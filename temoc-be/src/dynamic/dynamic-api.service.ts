
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class DynamicApiService {
  private readonly logger = new Logger(DynamicApiService.name);
  
  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}
  
//   async updateUserProfile(dynamicUserId: string, userData: any): Promise<any> {
//     try {
//       const apiKey = this.configService.get<string>('DYNAMIC_API_KEY');
//       const environmentId = this.configService.get<string>('DYNAMIC_ENVIRONMENT_ID');
      
//       if (!apiKey || !environmentId) {
//         this.logger.warn('Dynamic API configuration is missing');
//         return null;
//       }
      
//       // This is the endpoint that Dynamic's SDK likely uses for user updates
//       const url = `https://app.dynamic.xyz/api/v0/environments/${environmentId}/users/${dynamicUserId}`;
      
//       this.logger.log(`Updating Dynamic user: ${dynamicUserId}`);
      
//       // Only send fields that Dynamic expects (firstName, lastName, username, email)
//       const updatedFields = {};
//       if (userData.firstName !== undefined) updatedFields['firstName'] = userData.firstName;
//       if (userData.lastName !== undefined) updatedFields['lastName'] = userData.lastName;
//       if (userData.username !== undefined) updatedFields['username'] = userData.username;
//       // Don't include email unless you're prepared to handle verification
//       // if (userData.email !== undefined) updatedFields['email'] = userData.email;
      
//       const response = await lastValueFrom(
//         this.httpService.patch(
//           url,
//           updatedFields,
//           {
//             headers: {
//               'Authorization': `Bearer ${apiKey}`,
//               'Content-Type': 'application/json',
//             },
//           }
//         )
//       );
      
//       // Check if email verification is required
//       if (response.data?.isEmailVerificationRequired) {
//         this.logger.log('Email verification required for user update');
//         // Your backend would need to handle this case
//       }
      
//       return response.data;
//     } catch (error) {
//       this.logger.error(`Failed to update Dynamic user: ${error.message}`);
      
//       // Instead of throwing, return an error object
//       return {
//         success: false,
//         error: error.message,
//         message: 'Could not update user in Dynamic'
//       };
//     }
//   }
// src/dynamic/dynamic-api.service.ts
async updateUserProfile(dynamicUserId: string, userData: any): Promise<any> {
    try {
      const apiKey = this.configService.get<string>('DYNAMIC_API_KEY');
      const environmentId = this.configService.get<string>('DYNAMIC_ENVIRONMENT_ID');
      
      if (!apiKey || !environmentId) {
        this.logger.warn('Dynamic API configuration is missing');
        return null;
      }
      
      // Same URL, but we'll use PUT instead of PATCH
      const url = `https://app.dynamic.xyz/api/v0/environments/${environmentId}/users/${dynamicUserId}`;
      
      this.logger.log(`Updating Dynamic user: ${dynamicUserId} with method PUT`);
      
      // Only send fields that Dynamic expects
      const updatedFields = {};
      if (userData.firstName !== undefined) updatedFields['firstName'] = userData.firstName;
      if (userData.lastName !== undefined) updatedFields['lastName'] = userData.lastName;
      if (userData.username !== undefined) updatedFields['username'] = userData.username;
      
      // Try PUT instead of PATCH
      const response = await lastValueFrom(
        this.httpService.put(
          url,
          updatedFields,
          {
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
            },
          }
        )
      );
      
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to update Dynamic user: ${error.message}`);
      
      // If we still get a 405, let's try POST as a fallback
      if (error.response?.status === 405) {
        try {
          this.logger.log(`Retrying with POST method for Dynamic user: ${dynamicUserId}`);
          const apiKey = this.configService.get<string>('DYNAMIC_API_KEY');
          const environmentId = this.configService.get<string>('DYNAMIC_ENVIRONMENT_ID');
          
          const url = `https://app.dynamic.xyz/api/v0/environments/${environmentId}/users/${dynamicUserId}/update`;
          
          const updatedFields = {};
          if (userData.firstName !== undefined) updatedFields['firstName'] = userData.firstName;
          if (userData.lastName !== undefined) updatedFields['lastName'] = userData.lastName;
          if (userData.username !== undefined) updatedFields['username'] = userData.username;
          
          const response = await lastValueFrom(
            this.httpService.post(
              url,
              updatedFields,
              {
                headers: {
                  'Authorization': `Bearer ${apiKey}`,
                  'Content-Type': 'application/json',
                },
              }
            )
          );
          
          return response.data;
        } catch (postError) {
          this.logger.error(`Failed POST attempt: ${postError.message}`);
        }
      }
      
      return {
        success: false,
        error: error.message,
        message: 'Could not update user in Dynamic'
      };
    }
  }
}