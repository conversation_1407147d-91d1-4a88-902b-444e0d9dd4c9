import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailService.name);

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get<string>('MAIL_HOST') || 'smtp.mailtrap.io',
      port: this.configService.get<number>('MAIL_PORT') || 2525,
      auth: {
        user: this.configService.get<string>('MAIL_USER') || '',
        pass: this.configService.get<string>('MAIL_PASS') || '',
      },
    });
  }

  private async readTemplate(templateName: string): Promise<string> {
    const templatePath = path.join(process.cwd(), templateName);
    return fs.promises.readFile(templatePath, 'utf8');
  }

  private async compileTemplate(templateName: string, data: any): Promise<string> {
    const templateContent = await this.readTemplate(templateName);
    const template = handlebars.compile(templateContent);
    return template(data);
  }

  async sendEmail(
    to: string,
    subject: string,
    templateName: string,
    data: any,
  ): Promise<boolean> {
    try {
      const html = await this.compileTemplate(templateName, data);
      
      const mailOptions = {
        from: this.configService.get<string>('MAIL_FROM') || '<EMAIL>',
        to,
        subject,
        html,
      };

      await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent to ${to} with subject: ${subject}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      return false;
    }
  }

  async sendFanWelcomeEmail(to: string, fanName: string): Promise<boolean> {
    console.log(to,fanName ,'login.html');
    
    return this.sendEmail(
      to,
      'Welcome to TEMOC – The Future of Music is Yours 🎵',
      'login.html',
      { fanName },
    );
  }

  async sendBecomeArtistEmail(to: string, artistName: string): Promise<boolean> {
    return this.sendEmail(
      to,
      'Ready to Launch Your Music Career on TEMOC?',
      'become-artist.html',
      { artistName },
    );
  }

  async sendLibraryCreationEmail(to: string, artistName: string): Promise<boolean> {
    return this.sendEmail(
      to,
      'Your New Content Library is Live',
      'library-creation.html',
      { artistName },
    );
  }

  async sendAlbumCreationEmail(to: string, artistName: string): Promise<boolean> {
    return this.sendEmail(
      to,
      'Your Album Is Ready to Go Live',
      'album-creation.html',
      { artistName },
    );
  }

  async sendSongAddedEmail(to: string, artistName: string, songTitle: string): Promise<boolean> {
    return this.sendEmail(
      to,
      'Your Song Has Been Added 🎧',
      'song-add.html',
      { artistName, songTitle },
    );
  }
}