import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Types } from "mongoose";
import { Document } from "mongoose";

export type LiquidityDocument = Liquidity & Document;

@Schema({ timestamps: true })
export class Liquidity {
  @Prop({ type: Types.ObjectId, ref: "User" })
  userId: Types.ObjectId;

  @Prop({ lowercase: true })
  tokenAAddress: string;
  @Prop()
  tokenASymbol: string;

  @Prop({ lowercase: true })
  tokenBAddress: string;

  @Prop()
  tokenBSymbol: string;

  @Prop()
  amountA: string;

  @Prop()
  amountB: string;

  @Prop()
  pairAddress: string;

  @Prop()
  transactionHash: string;

  @Prop({ lowercase: true })
  poolAddress: string;

  @Prop({ lowercase: true })
  userAddress: string;

  @Prop()
  positionId: string;

  @Prop()
  fee: number;

  @Prop()
  tickLower: number;

  @Prop()
  tickUpper: number;

  @Prop()
  timestamp: string;
}

export const LiquiditySchema = SchemaFactory.createForClass(Liquidity);
