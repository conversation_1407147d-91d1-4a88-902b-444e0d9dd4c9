import { Controller, Post, Body, Get, Query, UseGuards } from "@nestjs/common";
import { LiquidityService } from "./liquidity.service";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { AuthUser } from "src/auth/decorators/user.decorator";
import { Types } from "mongoose";

@Controller("liquidity")
export class LiquidityController {
  constructor(private readonly liquidityService: LiquidityService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  async create(@AuthUser() req, @Body() body: any) {
    const data = {
      ...body,
      userId: new Types.ObjectId(req._id),
    };
    return this.liquidityService.create(data);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  async getAll(@AuthUser() req) {
    const userId = req._id;
    return this.liquidityService.findAll(userId);
  }

  @Get("user")
  async getByUser(@Query("address") address: string) {
    return this.liquidityService.findByUser(address);
  }
}
