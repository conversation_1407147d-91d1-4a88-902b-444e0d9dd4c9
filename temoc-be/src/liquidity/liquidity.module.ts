import { Modu<PERSON> } from "@nestjs/common";
import { LiquidityService } from "./liquidity.service";
import { LiquidityController } from "./liquidity.controller";
import { UsersModule } from "src/users/users.module";
import { Liquidity, LiquiditySchema } from "./entities/liquidity.entity";
import { CloudinaryModule } from "src/cloudinary/cloudinary.module";
import { MongooseModule } from "@nestjs/mongoose";
import { JwtService } from "@nestjs/jwt";
import { Token, TokenSchema } from "src/tokens/entities/token.entity";
import { TokenService } from "src/tokens/tokens.service";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Liquidity.name, schema: LiquiditySchema },
      { name: Token.name, schema: TokenSchema },
    ]),
    CloudinaryModule,
    UsersModule,
  ],
  controllers: [LiquidityController],
  providers: [LiquidityService, JwtService, TokenService],
})
export class LiquidityModule {}
