import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { Liquidity, LiquidityDocument } from "./entities/liquidity.entity";
import { TokenService } from "src/tokens/tokens.service";

@Injectable()
export class LiquidityService {
  constructor(
    @InjectModel(Liquidity.name)
    private liquidityModel: Model<LiquidityDocument>,
    private readonly tokenService: TokenService
  ) {}

  async create(data: Partial<Liquidity>): Promise<Liquidity> {
    const liquidity = await this.liquidityModel.create(data);

    if (data.tokenAAddress) {
      // Update token status to 'launched' or any other status
      await this.tokenService.updateStatus(
        data.tokenAAddress.toLocaleLowerCase(),
        "launched"
      );
    }

    return liquidity;
  }

  async findAll(userId: string): Promise<Liquidity[]> {
    const id = new Types.ObjectId(userId);
    return this.liquidityModel.find({ userId: id }).sort({ createdAt: -1 });
  }

  async findByUser(userAddress: string): Promise<Liquidity[]> {
    return this.liquidityModel.find({ userAddress });
  }
}
