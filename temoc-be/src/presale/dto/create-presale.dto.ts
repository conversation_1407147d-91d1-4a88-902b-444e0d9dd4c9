// src/presale/dto/create-presale.dto.ts

import {
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from "class-validator";

export class CreatePresaleDto {
  // Token Info
  @IsString()
  @IsNotEmpty()
  tokenName: string;

  @IsString()
  @IsNotEmpty()
  tokenSymbol: string;

  @IsString()
  @IsNotEmpty()
  tokenAddress: string;

  @IsNumber()
  tokenDecimals: number;

  @IsString()
  totalSupply: string;

  // Presale Details
  @IsString()
  presaleRate: string;

  @IsString()
  softCap: string;

  @IsString()
  hardCap: string;

  @IsString()
  minBuy: string;

  @IsString()
  maxBuy: string;

  @IsDateString()
  startTime: Date;

  @IsDateString()
  endTime: Date;

  // Listing Details
  @IsOptional()
  @IsString()
  listingRate?: string;

  @IsOptional()
  @IsString()
  listingOn?: string;

  @IsOptional()
  @IsBoolean()
  autoListing?: boolean;

  @IsOptional()
  @IsNumber()
  liquidityPercent?: number;

  @IsOptional()
  @IsNumber()
  liquidityLockupDays?: number;

  // Access / Verification
  @IsOptional()
  @IsBoolean()
  isWhitelisted?: boolean;

  @IsOptional()
  @IsBoolean()
  requiresKYC?: boolean;

  @IsOptional()
  whitelistAddresses?: string[];

  // Vesting
  @IsOptional()
  @IsBoolean()
  hasVesting?: boolean;

  @IsOptional()
  @IsDateString()
  vestingStartTime?: Date;

  @IsOptional()
  @IsNumber()
  vestingCliffMonths?: number;

  @IsOptional()
  @IsNumber()
  vestingDurationMonths?: number;

  // Blockchain Info
  @IsString()
  @IsNotEmpty()
  ownerAddress: string;

  @IsOptional()
  @IsString()
  network?: string;

  // Links
  @IsOptional()
  @IsString()
  auditReportUrl?: string;

  @IsOptional()
  @IsString()
  projectWebsite?: string;

  @IsOptional()
  @IsString()
  telegramLink?: string;

  @IsOptional()
  @IsString()
  twitterLink?: string;

  @IsOptional()
  @IsString()
  logoUrl?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsBoolean()
  affiliateProgram?: boolean;
}
