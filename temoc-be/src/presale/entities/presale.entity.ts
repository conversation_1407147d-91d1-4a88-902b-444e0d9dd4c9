import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

export type PresaleDocument = Presale & Document;

@Schema({ timestamps: true })
export class Presale extends Document {
  @Prop({ type: Types.ObjectId, ref: "User", required: true })
  userId: Types.ObjectId;
  // Token Info
  @Prop()
  tokenName: string;

  @Prop()
  chainId: number;

  @Prop()
  fairLaunchAmount: number;

  @Prop()
  tokenSymbol: string;

  @Prop()
  tokenAddress: string;

  @Prop({ default: 18 })
  tokenDecimals: number;

  @Prop()
  totalSupply: string; // or use `number` if you prefer

  // Presale Details
  @Prop()
  presaleRate: string; // tokens per ETH/BNB/etc.

  @Prop()
  softCap: number;

  @Prop()
  hardCap: string;

  @Prop()
  minBuy: number;

  @Prop()
  maxBuy: number;

  @Prop()
  presaleStart: Date;

  @Prop()
  presaleEnd: Date;

  // Listing Details
  @Prop()
  listingRate: string;

  @Prop()
  liquidityUnlock: Date;

  @Prop()
  listingOn: number; // e.g., PancakeSwap, Uniswap

  @Prop({ default: false })
  autoListing: boolean;

  @Prop()
  liquidityPercent: number;

  @Prop()
  liquidityLockupDays: number;

  // Vesting (optional)
  @Prop({ default: false })
  hasVesting: boolean;

  @Prop()
  vestingStartTime: Date;

  @Prop()
  vestingCliffMonths: number;

  @Prop()
  vestingDurationMonths: number;

  // Status Flags
  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  isFinalized: boolean;

  @Prop({ default: false })
  isCancelled: boolean;

  // Owner/Network Info
  @Prop()
  ownerAddress: string;

  @Prop({ default: "ETH" }) // or 'BNB', 'Polygon', etc.
  network: string;

  @Prop()
  currency: string;

  @Prop()
  logoUrl: string;

  @Prop({
    default: "disable-affiliate",
    enum: ["disable-affiliate", "enable-affiliate"],
  })
  affiliateProgram: string;
}

export const PresaleSchema = SchemaFactory.createForClass(Presale);
