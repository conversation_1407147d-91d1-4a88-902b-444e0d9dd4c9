import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  NotFoundException,
  ForbiddenException,
  Query,
} from "@nestjs/common";
import { PresaleService } from "./presale.service";
import { CreatePresaleDto } from "./dto/create-presale.dto";
import { UpdatePresaleDto } from "./dto/update-presale.dto";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { AuthUser } from "src/auth/decorators/user.decorator";
import { PaginationDto } from "src/common/dto/pagination.dto";

@Controller("presale")
export class PresaleController {
  constructor(private readonly presaleService: PresaleService) {}

  @UseGuards(JwtAuthGuard)
  @Post("create")
  async create(@AuthUser() user, @Body() createPresaleDto: CreatePresaleDto) {
    const { _id } = user;
    return await this.presaleService.create({
      ...createPresaleDto,
      userId: _id,
    });
  }

  @Get("findAll")
  async findAll(@Query() paginationDto: PaginationDto) {
    return this.presaleService.findAll(paginationDto);
  }

  // @Get(":id")
  // findOne(@Param("id") id: string) {
  //   return this.presaleService.findOne(id);
  // }

  @Get(":userId")
  findByUser(@Param("userId") userId: string) {
    return this.presaleService.findByUserId(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(":id")
  async update(@Body() updatePresaleDto: UpdatePresaleDto, @AuthUser() user) {
    const id = user._id;
    const presale = await this.presaleService.findById(id);
    if (!presale) {
      throw new NotFoundException("Presale not found");
    }
    if (presale.userId.toString() !== user._id.toString()) {
      throw new ForbiddenException(
        "You do not have permission to update this presale"
      );
    }

    return this.presaleService.update(id, updatePresaleDto);
  }

  @Delete(":id")
  remove(@Param("id") id: string) {
    return this.presaleService.remove(+id);
  }
}
