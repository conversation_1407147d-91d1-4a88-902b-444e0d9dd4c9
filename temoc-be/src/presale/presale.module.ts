import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { PresaleService } from "./presale.service";
import { PresaleController } from "./presale.controller";
import { JwtService } from "@nestjs/jwt";
import { UsersService } from "src/users/users.service";
import { MongooseModule } from "@nestjs/mongoose";
import { Presale, PresaleSchema } from "./entities/presale.entity";
import { CloudinaryModule } from "src/cloudinary/cloudinary.module";
import { UsersModule } from "src/users/users.module";
import { TokenService } from "src/tokens/tokens.service";
import { Token, TokenSchema } from "src/tokens/entities/token.entity";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Presale.name, schema: PresaleSchema },
      { name: Token.name, schema: TokenSchema },
    ]),
    CloudinaryModule,
    UsersModule,
  ],
  controllers: [PresaleController],
  providers: [PresaleService, JwtService, TokenService],
})
export class PresaleModule {}
