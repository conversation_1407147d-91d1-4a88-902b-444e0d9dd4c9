import { Injectable, NotFoundException } from "@nestjs/common";
import { CreatePresaleDto } from "./dto/create-presale.dto";
import { UpdatePresaleDto } from "./dto/update-presale.dto";
import { InjectModel } from "@nestjs/mongoose";
import { Presale } from "./entities/presale.entity";
import { Model, Types } from "mongoose";
import { Type } from "class-transformer";
import { TokenService } from "src/tokens/tokens.service";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { paginate } from "src/utils/pagination.util";

@Injectable()
export class PresaleService {
  constructor(
    @InjectModel(Presale.name) private readonly presaleModel: Model<Presale>,
    private readonly tokenService: TokenService
  ) {}

  async create(data: any) {
    const presale = await this.presaleModel.create(data);

    // ✅ Update token status to "presale"
    if (data.tokenAddress) {
      await this.tokenService.updateStatus(
        data.tokenAddress.toLowerCase(),
        "presale"
      );
    }

    return presale;
  }

  async findAll(paginationDto: PaginationDto) {
    return paginate(this.presaleModel, {}, paginationDto);
  }

  async update(id: string, updatePresaleDto: UpdatePresaleDto) {
    const updatedPresale = await this.presaleModel.findByIdAndUpdate(
      id,
      updatePresaleDto,
      { new: true } // return the updated doc
    );

    if (!updatedPresale) {
      throw new NotFoundException(`Presale with id ${id} not found`);
    }

    return updatedPresale;
  }

  async findById(id: string) {
    return this.presaleModel.findById(id).exec();
  }

  async findByUserId(userId: string) {
    const id = new Types.ObjectId(userId);
    return this.presaleModel.findOne({ userId: id }).exec();
  }

  async findOne(id: string) {
    const presale = await this.presaleModel.findById(id).exec();
    if (!presale) {
      throw new NotFoundException(`Presale with id ${id} not found`);
    }
    return presale;
  }

  remove(id: number) {
    return `This action removes a #${id} presale`;
  }
}
