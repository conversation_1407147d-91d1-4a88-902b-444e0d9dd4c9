// src/token/dto/create-token.dto.ts
import { IsString, IsNotEmpty, IsNumber, IsOptional } from "class-validator";

export class CreateTokenDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  symbol: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsString()
  @IsNotEmpty()
  walletAddress: string;

  @IsNumber()
  decimals: number;

  @IsString()
  @IsNotEmpty()
  totalSupply: string;

  @IsOptional()
  @IsString()
  network?: string;

  @IsOptional()
  @IsString()
  logoUrl?: string;
}
