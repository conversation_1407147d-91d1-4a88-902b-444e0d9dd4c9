// src/token/schemas/token.schema.ts
import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

export type TokenDocument = Token & Document;

@Schema({ timestamps: true })
export class Token extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  symbol: string;

  @Prop({ required: true, lowercase: true })
  address: string;

  @Prop({ required: true, lowercase: true })
  walletAddress: string;

  @Prop({ required: true, default: 18 })
  decimals: number;

  @Prop({ required: true })
  totalSupply: string;

  @Prop({ type: Types.ObjectId, ref: "User", required: true })
  user: Types.ObjectId;

  @Prop()
  network: string;

  @Prop()
  logoUrl: string;

  @Prop({
    required: true,
    enum: ["pending", "presale", "launched", "inactive"],
    default: "pending",
  })
  status: string;
}

export const TokenSchema = SchemaFactory.createForClass(Token);
