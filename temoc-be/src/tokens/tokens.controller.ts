// src/token/token.controller.ts
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  Param,
  Query,
} from "@nestjs/common";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { TokenService } from "./tokens.service";
import { CreateTokenDto } from "./dto/create.token.dto";
import { AuthUser } from "src/auth/decorators/user.decorator";
import { FileInterceptor } from "@nestjs/platform-express";
import { memoryStorage } from "multer";
import { Types } from "mongoose";
import { PaginationDto } from "src/common/dto/pagination.dto";

@Controller("token")
export class TokenController {
  constructor(private readonly tokenService: TokenService) {}

  @UseGuards(JwtAuthGuard)
  @Post("create")
  @UseInterceptors(FileInterceptor("logo", { storage: memoryStorage() }))
  async create(
    @AuthUser() user,
    @Body() createTokenDto: CreateTokenDto,
    @UploadedFile() logo?: Express.Multer.File
  ) {
    if (logo && !logo.mimetype.startsWith("image/")) {
      throw new BadRequestException("Logo must be an image");
    }
    return this.tokenService.create(
      { ...createTokenDto, user: user._id },
      logo
    );
  }

  @UseGuards(JwtAuthGuard)
  @Get("my-tokens")
  async findMyTokens(@AuthUser() user) {
    return this.tokenService.findByUser(user._id);
  }

  @UseGuards(JwtAuthGuard)
  @Get("artist-tokens/:userId")
  async findTokensbyArtistid(@Param("userId") userId: string) {
    return this.tokenService.findByUser(userId);
  }

  @Get("findAll")
  async findAll(@Query() paginationDto: PaginationDto) {
    return this.tokenService.findAll(paginationDto);
  }

  @Get("launched")
  async findLaunched(@Query() paginationDto: PaginationDto) {
    return this.tokenService.findLaunchedTokens(paginationDto);
  }
}
