import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TokenController } from "./tokens.controller";
import { TokenService } from "./tokens.service";
import { Token, TokenSchema } from "./entities/token.entity";
import { CloudinaryModule } from "src/cloudinary/cloudinary.module";
import { UsersModule } from "src/users/users.module";
import { MongooseModule } from "@nestjs/mongoose";
import { AuthModule } from "src/auth/auth.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Token.name, schema: TokenSchema }]),
    CloudinaryModule,
    UsersModule,
    AuthModule, // Import AuthModule instead of JwtService directly
  ],
  controllers: [TokenController],
  providers: [TokenService],
})
export class TokensModule {}
