// src/token/token.service.ts
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { Token } from "./entities/token.entity";
import { CreateTokenDto } from "./dto/create.token.dto";
import { CloudinaryService } from "src/cloudinary/cloudinary.service";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { paginate } from "src/utils/pagination.util";

@Injectable()
export class TokenService {
  constructor(
    private cloudinaryService: CloudinaryService,
    @InjectModel(Token.name) private tokenModel: Model<Token>
  ) {}
  // https://eth-sepolia.g.alchemy.com/v2/3-_rEWyUc-m9QgySIlzI22-HBVZJfzLn
  async create(
    createTokenDto: CreateTokenDto & { user: string },
    logo?: Express.Multer.File
  ) {
    const tokenData: any = {
      ...createTokenDto,
    };

    // Upload logo to Cloudinary
    if (logo) {
      try {
        const result = await this.cloudinaryService.uploadImage(
          logo,
          "temoc-tokens"
        );
        tokenData.logoUrl = result.secure_url;
      } catch (error) {
        console.error(`Failed to upload logo: ${error.message}`);
        throw new Error(`Failed to upload logo: ${error.message}`);
      }
    }

    const createdToken = new this.tokenModel(tokenData);
    return createdToken.save();
  }

  async findByUser(userId: string) {
    const id = new Types.ObjectId(userId);
    return this.tokenModel.find({ user: id });
  }

  async updateStatus(address: string, status: string) {
    console.log(address, status);

    return this.tokenModel.findOneAndUpdate(
      { address: address.toLowerCase() }, // if you store addresses in lowercase
      { status },
      { new: true }
    );
  }

  async findAll(paginationDto: PaginationDto, filter: any = {}) {
    return paginate(this.tokenModel, filter, paginationDto);
  }

  async findLaunchedTokens(paginationDto: PaginationDto) {
    const filter = { status: "launched" };
    return paginate(this.tokenModel, filter, paginationDto);
  }
}
