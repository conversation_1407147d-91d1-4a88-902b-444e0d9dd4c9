import { <PERSON>p, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

@Schema({ timestamps: true })
export class UserAlbumAccess extends Document {
  @Prop({ type: Types.ObjectId, ref: "User", required: true })
  userId: string;

  @Prop({ type: Types.ObjectId, ref: "Album", required: true })
  albumId: string;

  @Prop({ default: false })
  hasAccess: boolean;

  @Prop()
  tokensSpent: number;
}

export type UserAlbumAccessDocument = UserAlbumAccess & Document;
export const UserAlbumAccessSchema =
  SchemaFactory.createForClass(UserAlbumAccess);
