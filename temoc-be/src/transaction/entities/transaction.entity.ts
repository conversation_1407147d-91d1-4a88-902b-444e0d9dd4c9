import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Types } from "mongoose";

export enum PresaleTxType {
  BLOCKCHAIN = "blockchain",
  WERT = "wert",
}
@Schema({ timestamps: true })
export class Transaction extends Document {
  @Prop({ type: Types.ObjectId, ref: "User", required: true })
  userId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: "User", required: true })
  artistId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: "Presale" })
  presaleId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: "Album" })
  albumId: Types.ObjectId;

  @Prop()
  txHash: string;

  @Prop({ lowercase: true })
  contract: string;

  @Prop({ lowercase: true })
  address: string;

  @Prop({})
  tokenAddress: string;

  @Prop({})
  currencyAddress: string;

  @Prop({ enum: PresaleTxType, default: PresaleTxType.BLOCKCHAIN })
  type: PresaleTxType;

  @Prop()
  amount: number;

  @Prop()
  email?: string;

  @Prop()
  usdAmount: number;

  @Prop()
  chainId: number;

  @Prop()
  stage: number;

  @Prop()
  tokens: number;

  @Prop()
  timestamp: number;

  @Prop()
  quote: string;
}

export type TransactionDocument = Transaction & Document;
export const TransactionSchema = SchemaFactory.createForClass(Transaction);
