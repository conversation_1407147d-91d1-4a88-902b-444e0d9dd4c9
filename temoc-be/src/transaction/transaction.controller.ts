import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import { TransactionsService } from "./transaction.service";
import { TransactionDocument } from "./entities/transaction.entity";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { JwtAuthGuard } from "src/auth/guards/jwt-auth.guard";
import { AuthUser } from "src/auth/decorators/user.decorator";
import { Album } from "src/content/schemas/album.schema";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";

@Controller("transactions")
export class TransactionsController {
  constructor(
    private readonly transactionsService: TransactionsService,
    @InjectModel(Album.name)
    public albumModel: Model<Album>
  ) {}

  @UseGuards(JwtAuthGuard)
  @Get("by-fan")
  async getFanTransactions(
    @AuthUser() user,
    @Query() paginationDto: PaginationDto
  ): Promise<any> {
    const userId = user._id;
    if (!userId) {
      throw new Error("userId is required");
    }
    return this.transactionsService.getFanTransactions(userId, paginationDto);
  }

  // 🔹 Get all transactions received by an artist
  @UseGuards(JwtAuthGuard)
  @Get("by-artist")
  async getArtistTransactions(
    @AuthUser() user,
    @Query() paginationDto: PaginationDto
  ): Promise<any> {
    const artistId = user._id;
    if (!artistId) {
      throw new Error("artistId is required");
    }
    return this.transactionsService.getArtistTransactions(
      artistId,
      paginationDto
    );
  }

  @Post("create/transaction")
  async create(@Body() createDto: any): Promise<TransactionDocument> {
    return this.transactionsService.createTransaction(createDto);
  }

  @Post()
  async createTransaction(@Body() createTransactionDto: any) {
    try {
      return await this.transactionsService.verifyAndCreate(
        createTransactionDto
      );
    } catch (error) {
      console.log(error);
    }
  }

  @Get("total-token-sales")
  getTotalTokenSales() {
    return this.transactionsService.getTotalTokenSales();
  }

  @Get("total-user-tokens")
  async getUserTotalTokens(
    @Query("identifier") identifier: string
  ): Promise<{ identifier: string; totalTokens: number }> {
    if (!identifier) {
      throw new Error("Query param 'identifier' is required");
    }

    const totalTokens =
      await this.transactionsService.getUserTotalTokens(identifier);

    return {
      identifier,
      totalTokens,
    };
  }

  @Get("fan/:userId/total-tokens")
  async getFanTotalTokens(
    @Param("userId") userId: string,
    @Query("presaleId") presaleId: string
  ) {
    const totalTokens = await this.transactionsService.getFanTotalTokens(
      userId,
      presaleId
    );
    return { userId, totalTokens };
  }

  @Get("artist/:artistId/total-tokens")
  async getArtistTotalTokens(@Param("artistId") artistId: string) {
    const totalTokens =
      await this.transactionsService.getArtistTotalTokens(artistId);
    return { artistId, totalTokens };
  }

  @UseGuards(JwtAuthGuard)
  @Get("albumAccess/:albumId")
  async getUserAccess(@AuthUser() req, @Param("albumId") albumId: string) {
    const userId = req._id;

    if (!userId || !albumId) {
      throw new BadRequestException("userId and albumId are required.");
    }

    // Fetch album and access records
    const [album, access] = await Promise.all([
      this.albumModel.findById(new Types.ObjectId(albumId)),
      this.transactionsService.getAccess(userId.toString(), albumId),
    ]);

    if (!album) {
      throw new NotFoundException("Album not found");
    }

    const tokensSpent = access?.tokensSpent ?? 0;
    const hasAccess =
      album.freeToListen || tokensSpent >= album.requiredTokenAmount;

    return {
      hasAccess,
      tokensSpent,
    };
  }
}
