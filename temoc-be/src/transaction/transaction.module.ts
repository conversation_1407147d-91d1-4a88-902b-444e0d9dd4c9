import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TransactionsController } from "./transaction.controller";
import { TransactionsService } from "./transaction.service";
import { MongooseModule } from "@nestjs/mongoose";
import { CloudinaryModule } from "src/cloudinary/cloudinary.module";
import { Transaction, TransactionSchema } from "./entities/transaction.entity";
import { UsersModule } from "src/users/users.module";
import { JwtService } from "@nestjs/jwt";
import {
  UserAlbumAccess,
  UserAlbumAccessSchema,
} from "./entities/UserAlbumAccess.entity";
import { Album, AlbumSchema } from "src/content/schemas/album.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Transaction.name, schema: TransactionSchema },
      { name: UserAlbumAccess.name, schema: UserAlbumAccessSchema },
      { name: Album.name, schema: AlbumSchema },
    ]),
    CloudinaryModule,
    UsersModule,
  ],
  controllers: [TransactionsController],
  providers: [TransactionsService, JwtService],
})
export class TransactionModule {}
