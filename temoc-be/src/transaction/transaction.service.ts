import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, PipelineStage, Types } from "mongoose";
import { ethers } from "ethers";
import {
  Transaction,
  TransactionDocument,
} from "./entities/transaction.entity";
import { getRpcUrlByChainId } from "src/utils/rpcUrl";
import { erc20Abi } from "./abi/erc20Abi";
import { PaginationDto } from "src/common/dto/pagination.dto";
import { paginate } from "src/utils/pagination.util";
import {
  UserAlbumAccess,
  UserAlbumAccessDocument,
} from "./entities/UserAlbumAccess.entity";

@Injectable()
export class TransactionsService {
  constructor(
    @InjectModel(Transaction.name)
    public transactionModel: Model<TransactionDocument>,
    @InjectModel(UserAlbumAccess.name)
    public userAlbumAccessModel: Model<UserAlbumAccessDocument>
  ) {}

  async createTransaction(dto: any): Promise<TransactionDocument> {
    const data = {
      ...dto,
      userId: new Types.ObjectId(dto.userId),
      artistId: new Types.ObjectId(dto.artistId),
      presaleId: new Types.ObjectId(dto.presaleId),
      albumId: new Types.ObjectId(dto.albumId),
    };

    const transaction = new this.transactionModel(data);
    await transaction.save();

    // ✅ Grant access to the album for the user
    await this.userAlbumAccessModel.findOneAndUpdate(
      { userId: data.userId, albumId: data.albumId },
      {
        userId: new Types.ObjectId(data.userId),
        albumId: new Types.ObjectId(data.albumId),
        hasAccess: true,
        tokensSpent: dto.tokensSpent, // optional
      },
      { upsert: true, new: true }
    );

    return transaction;
  }

  async verifyAndCreate(data: any): Promise<TransactionDocument> {
    const { txHash, address, amount, chainId, currencyPrice } = data;
    console.log(txHash, address, amount, chainId);

    // 🔵 Dynamically get RPC URL based on chainId
    const rpcUrl = getRpcUrlByChainId(chainId);
    console.log(rpcUrl, "rpcUrl");

    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const receipt2 = await provider.waitForTransaction(txHash, 1);

    // Check if the transaction succeeded
    if (receipt2.status !== 1) {
      throw new Error("Transaction failed.");
    }

    // Continue with your logic for verification
    console.log("Transaction confirmed:", receipt2);

    // Retrieve the transaction
    const tx = await provider.getTransaction(txHash);
    if (!tx) {
      throw new Error("Transaction not found.");
    }

    // 📍 If Native Token (like ETH / Base ETH)
    const isNativeToken = !data.tokenAddress;

    if (isNativeToken) {
      const valueInEth = parseFloat(ethers.formatEther(tx.value)); // Example: 0.0035 ETH

      const valueInUsd = valueInEth * currencyPrice; // ➡️ Convert to USD
      console.log(`Sent: ${valueInUsd} USD | Expected: ${amount} USD`);

      if (Math.abs(valueInUsd - amount) > 0.5) {
        // Allow small $0.5 fluctuation
        throw new Error("Amount mismatch.");
      }
    } else {
      // 📍 ERC20 Transfer
      const iface = new ethers.Interface(erc20Abi);
      const decoded = iface.parseTransaction({ data: tx.data });

      if (decoded.name !== "transfer") {
        throw new Error("Not a transfer transaction.");
      }

      const [to, value] = decoded.args;
      const valueInEth = parseFloat(ethers.formatEther(tx.value)); // Example: 0.0035 ETH

      const valueInUsd = valueInEth * currencyPrice; // ➡️ Convert to USD
      console.log(`Sent: ${valueInUsd} USD | Expected: ${amount} USD`);

      if (Math.abs(valueInUsd - amount) > 0.5) {
        // Allow small $0.5 fluctuation
        throw new Error("Amount mismatch.");
      }
    }

    const receipt = await provider.waitForTransaction(txHash, 1);

    if (receipt.status !== 1) {
      throw new Error("Transaction failed.");
    }

    // ✅ All checks passed, save transaction
    const savedTransaction = await this.transactionModel.create(data);

    // Log success message
    console.log("Transaction verified and successfully created!");

    return savedTransaction;
  }

  async getTotalTokenSales(): Promise<number> {
    const result = await this.transactionModel.aggregate([
      {
        $group: {
          _id: null,
          totalTokens: { $sum: "$tokens" },
        },
      },
    ]);

    return result[0]?.totalTokens || 0;
  }

  async getUserTotalTokens(identifier: string): Promise<number> {
    const pipeline: PipelineStage[] = [
      {
        $lookup: {
          from: "users",
          localField: "address",
          foreignField: "walletAddress",
          as: "userInfo",
        },
      },
      { $unwind: { path: "$userInfo", preserveNullAndEmptyArrays: true } },
      {
        $match: {
          $or: [
            { address: { $regex: identifier, $options: "i" } },
            { "userInfo.email": { $regex: identifier, $options: "i" } },
          ],
        },
      },
      {
        $group: {
          _id: null,
          totalTokens: { $sum: "$tokens" },
        },
      },
    ];

    const result = await this.transactionModel.aggregate(pipeline);
    return result[0]?.totalTokens || 0;
  }

  async getFanTotalTokens(userId: string, presaleId: string): Promise<number> {
    const result = await this.transactionModel.aggregate([
      {
        $match: {
          userId: new Types.ObjectId(userId),
          presaleId: new Types.ObjectId(presaleId),
        },
      },
      {
        $group: {
          _id: null,
          totalTokens: { $sum: "$tokens" },
        },
      },
    ]);

    return result[0]?.totalTokens || 0;
  }

  async getArtistTotalTokens(artistId: string): Promise<number> {
    const result = await this.transactionModel.aggregate([
      {
        $match: {
          artistId: new Types.ObjectId(artistId),
        },
      },
      {
        $group: {
          _id: null,
          totalTokens: { $sum: "$tokens" },
        },
      },
    ]);

    return result[0]?.totalTokens || 0;
  }

  async getFanTransactions(userId: string, paginationDto: PaginationDto) {
    const id = new Types.ObjectId(userId);

    const baseQuery: any = { userId: id };

    if (paginationDto.search) {
      baseQuery.$or = [
        { address: { $regex: paginationDto.search, $options: "i" } },
        { email: { $regex: paginationDto.search, $options: "i" } },
        { txHash: { $regex: paginationDto.search, $options: "i" } },
      ];
    }

    return paginate(this.transactionModel, baseQuery, paginationDto);
  }

  async getArtistTransactions(artistId: string, paginationDto: PaginationDto) {
    const id = new Types.ObjectId(artistId);

    const baseQuery: any = { artistId: id };

    if (paginationDto.search) {
      baseQuery.$or = [
        { address: { $regex: paginationDto.search, $options: "i" } },
        { email: { $regex: paginationDto.search, $options: "i" } },
        { txHash: { $regex: paginationDto.search, $options: "i" } },
      ];
    }

    return paginate(this.transactionModel, baseQuery, paginationDto);
  }

  async getAccess(userId: string, albumId: string) {
    return this.userAlbumAccessModel.findOne({
      userId: new Types.ObjectId(userId),
      albumId: new Types.ObjectId(albumId),
    });
  }
}
