import {
  Controller,
  Get,
  Query,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe,
  ParseBoolPipe,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthUser } from "../auth/decorators/user.decorator";
import { DiscoveryService, DiscoveryFilters } from "./discovery.service";

@ApiTags("Discovery")
@Controller("discovery")
export class DiscoveryController {
  constructor(private readonly discoveryService: DiscoveryService) {}

  @Get("artists")
  @ApiOperation({ summary: "Discover artists with filters and search" })
  @ApiResponse({ status: 200, description: "Artists retrieved successfully" })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page",
  })
  @ApiQuery({
    name: "genre",
    required: false,
    type: [String],
    description: "Filter by genres",
  })
  @ApiQuery({
    name: "isVerified",
    required: false,
    type: Boolean,
    description: "Filter by verification status",
  })
  @ApiQuery({
    name: "hasToken",
    required: false,
    type: Boolean,
    description: "Filter by token status",
  })
  @ApiQuery({
    name: "location",
    required: false,
    type: String,
    description: "Filter by location",
  })
  @ApiQuery({
    name: "search",
    required: false,
    type: String,
    description: "Search query",
  })
  @ApiQuery({
    name: "sortBy",
    required: false,
    enum: ["popular", "newest", "alphabetical", "tokenPrice", "mostActive"],
    description: "Sort criteria",
  })
  @ApiQuery({
    name: "sortOrder",
    required: false,
    enum: ["asc", "desc"],
    description: "Sort order",
  })
  async discoverArtists(
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query("genre") genre?: string | string[],
    @Query("isVerified", new ParseBoolPipe({ optional: true }))
    isVerified?: boolean,
    @Query("hasToken", new ParseBoolPipe({ optional: true }))
    hasToken?: boolean,
    @Query("location") location?: string,
    @Query("search") search?: string,
    @Query("sortBy")
    sortBy?:
      | "popular"
      | "newest"
      | "alphabetical"
      | "tokenPrice"
      | "mostActive",
    @Query("sortOrder") sortOrder?: "asc" | "desc",
    @AuthUser() user?: any
  ) {
    const filters: DiscoveryFilters = {
      genre: Array.isArray(genre) ? genre : genre ? [genre] : undefined,
      isVerified,
      hasToken,
      location,
      search,
      sortBy: sortBy || "popular",
      sortOrder: sortOrder || "desc",
    };

    return this.discoveryService.discoverArtists(
      filters,
      page,
      limit,
      user?.sub
    );
  }

  @Get("trending")
  @ApiOperation({ summary: "Get trending artists" })
  @ApiResponse({
    status: 200,
    description: "Trending artists retrieved successfully",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of artists to return",
  })
  async getTrendingArtists(
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number
  ) {
    return this.discoveryService.getTrendingArtists(limit);
  }

  @Get("new-verified")
  @ApiOperation({ summary: "Get newly verified artists" })
  @ApiResponse({
    status: 200,
    description: "New verified artists retrieved successfully",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of artists to return",
  })
  async getNewVerifiedArtists(
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number
  ) {
    return this.discoveryService.getNewVerifiedArtists(limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("popular")
  @ApiOperation({ summary: "Get popular artists" })
  @ApiResponse({
    status: 200,
    description: "Popular artists retrieved successfully",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of artists to return",
  })
  async getPopularArtists(
    @AuthUser() user: any,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number
  ) {
    const userId = user._id;
    return this.discoveryService.getPopularArtists(limit, userId);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth("JWT-auth")
  @Get("recommended")
  @ApiOperation({ summary: "Get recommended artists for current user" })
  @ApiResponse({
    status: 200,
    description: "Recommended artists retrieved successfully",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Number of artists to return",
  })
  async getRecommendedArtists(
    @AuthUser() user: any,
    @Query("limit", new DefaultValuePipe(10), ParseIntPipe) limit: number
  ) {
    return this.discoveryService.getRecommendedArtists(user.sub, limit);
  }

  @Get("search")
  @ApiOperation({ summary: "Search artists with advanced options" })
  @ApiResponse({
    status: 200,
    description: "Search results retrieved successfully",
  })
  @ApiQuery({
    name: "q",
    required: true,
    type: String,
    description: "Search query",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page",
  })
  @ApiQuery({
    name: "genre",
    required: false,
    type: [String],
    description: "Filter by genres",
  })
  @ApiQuery({
    name: "isVerified",
    required: false,
    type: Boolean,
    description: "Filter by verification status",
  })
  async searchArtists(
    @Query("q") query: string,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query("genre") genre?: string | string[],
    @Query("isVerified", new ParseBoolPipe({ optional: true }))
    isVerified?: boolean,
    @AuthUser() user?: any
  ) {
    const filters: DiscoveryFilters = {
      search: query,
      genre: Array.isArray(genre) ? genre : genre ? [genre] : undefined,
      isVerified,
      sortBy: "popular",
      sortOrder: "desc",
    };

    return this.discoveryService.discoverArtists(
      filters,
      page,
      limit,
      user?.sub
    );
  }

  @Get("genres")
  @ApiOperation({ summary: "Get available genres" })
  @ApiResponse({ status: 200, description: "Genres retrieved successfully" })
  async getGenres() {
    // This could be made dynamic by aggregating from user profiles
    const genres = [
      "Electronic",
      "Hip-Hop",
      "Pop",
      "Rock",
      "Jazz",
      "Classical",
      "Indie",
      "R&B",
      "Country",
      "Folk",
      "Reggae",
      "Blues",
      "Funk",
      "Soul",
      "Alternative",
      "Punk",
      "Metal",
      "House",
      "Techno",
      "Ambient",
    ];

    return { genres };
  }
}
