import { IsNotEmpty, Is<PERSON><PERSON>al, IsString, IsArray, ValidateNested, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';

export class SocialLinkDto {
  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsNotEmpty()
  @IsUrl()
  url: string;
}

export class BasicInfoDto {
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  bio?: string;
}

export class UsernameDto {
  @IsNotEmpty()
  @IsString()
  username: string;
}

export class SocialLinksDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SocialLinkDto)
  socialLinks: SocialLinkDto[];
}

export class DemoTracksDto {
  @IsArray()
  @IsString({ each: true })
  demoTracks: string[];
}

export class SubmitApplicationDto {
  @IsNotEmpty()
  @IsString()
  userId: string;
}