
import { IsE<PERSON>, <PERSON>Enum, IsS<PERSON>, IsOptional, IsBoolean, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { UserRole, AuthProvider } from '../schemas/user.schema';

class WalletDto {
  @IsString()
  address: string;

  @IsString()
  chainId: number;
  @IsBoolean()
  @IsOptional()
  tokenCreated?: boolean;
  @IsString()
  provider: string;
}

class SocialLinkDto {
  @IsString()
  platform: string;

  @IsString()
  url: string;
}

class KycDto {
  @IsBoolean()
  @IsOptional()
  submitted?: boolean;

  @IsBoolean()
  @IsOptional()
  verified?: boolean;

  @IsOptional()
  verificationDate?: Date;

  @IsString()
  @IsOptional()
  rejectionReason?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsArray()
  @IsOptional()
  documents?: string[];
}

class ArtistProfileDto {
  @IsString()
  @IsOptional()
  bio?: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SocialLinkDto)
  socialLinks?: SocialLinkDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => KycDto)
  kyc?: KycDto;

  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;

  @IsArray()
  @IsOptional()
  tokens?: string[];

  @IsString()
  @IsOptional()
  genre?: string;
}

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @IsOptional()
  password?: string;

  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => WalletDto)
  wallets?: WalletDto[];

  @IsString()
  @IsOptional()
  dynamicUserId?: string;

  @IsString()
  @IsOptional()
  displayName?: string;

  @IsString()
  @IsOptional()
  avatarUrl?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => ArtistProfileDto)
  artistProfile?: ArtistProfileDto;

  @IsEnum(AuthProvider)
  @IsOptional()
  authProvider?: AuthProvider;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsBoolean()
  @IsOptional()
  isAdmin?: boolean;
}