import {
  Controller,
  Post,
  Get,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  ParseIntPipe,
  DefaultValuePipe,
  ForbiddenException
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthUser } from "../auth/decorators/user.decorator";
import { ReportService, CreateReportDto } from "./report.service";
import { ReportStatus, ReportCategory, ReportUrgency } from "./schemas/report.schema";
import { memoryStorage } from "multer";

@ApiTags("Reports")
@ApiBearerAuth("JWT-auth")
@Controller("reports")
export class ReportController {
  constructor(private readonly reportService: ReportService) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  @UseInterceptors(FilesInterceptor("evidence", 5, { storage: memoryStorage() }))
  @ApiOperation({ summary: "Create a new report" })
  @ApiConsumes("multipart/form-data")
  @ApiResponse({ status: 201, description: "Report created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Reported user/content not found" })
  async createReport(
    @AuthUser() user: any,
    @Body() createReportDto: CreateReportDto,
    @UploadedFiles() evidenceFiles?: Express.Multer.File[]
  ) {
    console.log('Create report - User ID:', user._id);
    return this.reportService.createReport(user._id, createReportDto, evidenceFiles);
  }

  @UseGuards(JwtAuthGuard)
  @Get("my-reports")
  @ApiOperation({ summary: "Get current user's reports" })
  @ApiResponse({ status: 200, description: "Reports retrieved successfully" })
  async getMyReports(
    @AuthUser() user: any,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number
  ) {
    return this.reportService.getUserReports(user._id, page, limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("ticket/:ticketNumber")
  @ApiOperation({ summary: "Get report by ticket number" })
  @ApiResponse({ status: 200, description: "Report retrieved successfully" })
  @ApiResponse({ status: 404, description: "Report not found" })
  async getReportByTicket(@Param("ticketNumber") ticketNumber: string) {
    return this.reportService.getReportByTicketNumber(ticketNumber);
  }

  @UseGuards(JwtAuthGuard)
  @Get(":reportId")
  @ApiOperation({ summary: "Get report by ID" })
  @ApiResponse({ status: 200, description: "Report retrieved successfully" })
  @ApiResponse({ status: 404, description: "Report not found" })
  async getReportById(@Param("reportId") reportId: string) {
    return this.reportService.getReportById(reportId);
  }

  // Admin endpoints (will be moved to admin module later)
  @UseGuards(JwtAuthGuard)
  @Get()
  @ApiOperation({ summary: "Get all reports (Admin only)" })
  @ApiResponse({ status: 200, description: "Reports retrieved successfully" })
  async getAllReports(
    @AuthUser() user: any,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query("status") status?: ReportStatus,
    @Query("category") category?: ReportCategory,
    @Query("urgency") urgency?: ReportUrgency,
    @Query("assignedModerator") assignedModerator?: string
  ) {
    // Check if user is admin
    if (!user.isAdmin) {
      throw new ForbiddenException('Admin access required');
    }

    const filters = {
      status,
      category,
      urgency,
      assignedModerator
    };

    return this.reportService.getReports(page, limit, filters);
  }

  @UseGuards(JwtAuthGuard)
  @Put(":reportId/status")
  @ApiOperation({ summary: "Update report status (Admin only)" })
  @ApiResponse({ status: 200, description: "Report status updated successfully" })
  @ApiResponse({ status: 404, description: "Report not found" })
  async updateReportStatus(
    @Param("reportId") reportId: string,
    @Body() updateData: {
      status: ReportStatus;
      moderatorNotes?: string;
      resolutionAction?: string;
    },
    @AuthUser() user: any
  ) {
    // Check if user is admin
    if (!user.isAdmin) {
      throw new ForbiddenException('Admin access required');
    }

    return this.reportService.updateReportStatus(
      reportId,
      updateData.status,
      user._id,
      updateData.moderatorNotes,
      updateData.resolutionAction
    );
  }

  @UseGuards(JwtAuthGuard)
  @Put(":reportId/assign")
  @ApiOperation({ summary: "Assign moderator to report (Admin only)" })
  @ApiResponse({ status: 200, description: "Moderator assigned successfully" })
  @ApiResponse({ status: 404, description: "Report not found" })
  async assignModerator(
    @Param("reportId") reportId: string,
    @Body() assignData: { moderatorId: string },
    @AuthUser() user: any
  ) {
    // Check if user is admin
    if (!user.isAdmin) {
      throw new ForbiddenException('Admin access required');
    }

    return this.reportService.assignModerator(reportId, assignData.moderatorId);
  }

  @UseGuards(JwtAuthGuard)
  @Get("admin/stats")
  @ApiOperation({ summary: "Get report statistics (Admin only)" })
  @ApiResponse({ status: 200, description: "Statistics retrieved successfully" })
  async getReportStats(@AuthUser() user: any) {
    // Check if user is admin
    if (!user.isAdmin) {
      throw new ForbiddenException('Admin access required');
    }

    return this.reportService.getReportStats();
  }
}
