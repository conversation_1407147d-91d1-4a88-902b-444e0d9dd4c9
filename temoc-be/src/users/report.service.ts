import { Injectable, NotFoundException, BadRequestException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { Report, ReportCategory, ReportSubcategory, ReportStatus, ReportUrgency } from "./schemas/report.schema";
import { User } from "./schemas/user.schema";
import { CloudinaryService } from "../cloudinary/cloudinary.service";

export interface CreateReportDto {
  reportedUserId?: string;
  reportedContentId?: string;
  category: ReportCategory;
  subcategory: ReportSubcategory;
  description: string;
  urgency?: ReportUrgency;
  isAnonymous?: boolean;
  metadata?: Record<string, any>;
}

@Injectable()
export class ReportService {
  constructor(
    @InjectModel(Report.name) private reportModel: Model<Report>,
    @InjectModel(User.name) private userModel: Model<User>,
    private cloudinaryService: CloudinaryService
  ) {}

  async createReport(
    reporterId: string,
    createReportDto: CreateReportDto,
    evidenceFiles?: Express.Multer.File[]
  ) {
    console.log('Create report - Reporter ID:', reporterId);
    console.log('Create report - Reporter ID type:', typeof reporterId);
    console.log('Create report - Reporter ID length:', reporterId?.length);

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(reporterId)) {
      console.log('Invalid ObjectId format for reporter:', reporterId);
      throw new BadRequestException('Invalid reporter ID format');
    }

    // Validate reporter exists
    const reporter = await this.userModel.findById(reporterId);
    console.log('Reporter found:', !!reporter);
    if (!reporter) {
      throw new NotFoundException('Reporter not found');
    }

    // Validate reported user if provided
    if (createReportDto.reportedUserId) {
      const reportedUser = await this.userModel.findById(createReportDto.reportedUserId);
      if (!reportedUser) {
        throw new NotFoundException('Reported user not found');
      }

      if (reporterId === createReportDto.reportedUserId) {
        throw new BadRequestException('Cannot report yourself');
      }
    }

    // Generate unique ticket number
    const ticketNumber = await this.generateTicketNumber();

    // Upload evidence files if provided
    const evidenceUrls: string[] = [];
    if (evidenceFiles && evidenceFiles.length > 0) {
      for (const file of evidenceFiles) {
        try {
          const result = await this.cloudinaryService.uploadImage(file, "temoc-reports");
          evidenceUrls.push(result.secure_url);
        } catch (error) {
          console.error(`Failed to upload evidence: ${error.message}`);
        }
      }
    }

    const report = new this.reportModel({
      reporter: new Types.ObjectId(reporterId),
      reportedUser: createReportDto.reportedUserId ? new Types.ObjectId(createReportDto.reportedUserId) : undefined,
      reportedContent: createReportDto.reportedContentId ? new Types.ObjectId(createReportDto.reportedContentId) : undefined,
      category: createReportDto.category,
      subcategory: createReportDto.subcategory,
      description: createReportDto.description,
      urgency: createReportDto.urgency || ReportUrgency.MEDIUM,
      isAnonymous: createReportDto.isAnonymous || false,
      metadata: createReportDto.metadata || {},
      evidenceUrls,
      ticketNumber
    });

    await report.save();

    return {
      success: true,
      ticketNumber,
      reportId: report._id,
      message: 'Report submitted successfully'
    };
  }

  async getReports(
    page: number = 1,
    limit: number = 20,
    filters?: {
      status?: ReportStatus;
      category?: ReportCategory;
      urgency?: ReportUrgency;
      assignedModerator?: string;
    }
  ) {
    const skip = (page - 1) * limit;
    const query: any = {};

    if (filters?.status) {
      query.status = filters.status;
    }

    if (filters?.category) {
      query.category = filters.category;
    }

    if (filters?.urgency) {
      query.urgency = filters.urgency;
    }

    if (filters?.assignedModerator) {
      query.assignedModerator = new Types.ObjectId(filters.assignedModerator);
    }

    const [reports, total] = await Promise.all([
      this.reportModel
        .find(query)
        .populate('reporter', 'username displayName email')
        .populate('reportedUser', 'username displayName email')
        .populate('assignedModerator', 'username displayName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      this.reportModel.countDocuments(query)
    ]);

    return {
      reports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getReportById(reportId: string) {
    const report = await this.reportModel
      .findById(reportId)
      .populate('reporter', 'username displayName email')
      .populate('reportedUser', 'username displayName email')
      .populate('assignedModerator', 'username displayName');

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async getReportByTicketNumber(ticketNumber: string) {
    const report = await this.reportModel
      .findOne({ ticketNumber })
      .populate('reporter', 'username displayName email')
      .populate('reportedUser', 'username displayName email')
      .populate('assignedModerator', 'username displayName');

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async getUserReports(userId: string, page: number = 1, limit: number = 20) {
    const skip = (page - 1) * limit;

    const [reports, total] = await Promise.all([
      this.reportModel
        .find({ reporter: new Types.ObjectId(userId) })
        .populate('reportedUser', 'username displayName')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      this.reportModel.countDocuments({ reporter: new Types.ObjectId(userId) })
    ]);

    return {
      reports,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async updateReportStatus(
    reportId: string,
    status: ReportStatus,
    moderatorId?: string,
    moderatorNotes?: string,
    resolutionAction?: string
  ) {
    const updateData: any = { status };

    if (moderatorId) {
      updateData.assignedModerator = new Types.ObjectId(moderatorId);
    }

    if (moderatorNotes) {
      updateData.moderatorNotes = moderatorNotes;
    }

    if (resolutionAction) {
      updateData.resolutionAction = resolutionAction;
    }

    if (status === ReportStatus.RESOLVED || status === ReportStatus.DISMISSED) {
      updateData.resolutionDate = new Date();
    }

    const report = await this.reportModel.findByIdAndUpdate(
      reportId,
      updateData,
      { new: true }
    );

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async assignModerator(reportId: string, moderatorId: string) {
    const moderator = await this.userModel.findById(moderatorId);
    if (!moderator || !moderator.isAdmin) {
      throw new BadRequestException('Invalid moderator');
    }

    const report = await this.reportModel.findByIdAndUpdate(
      reportId,
      {
        assignedModerator: new Types.ObjectId(moderatorId),
        status: ReportStatus.UNDER_REVIEW
      },
      { new: true }
    );

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async getReportStats() {
    const stats = await this.reportModel.aggregate([
      {
        $group: {
          _id: null,
          totalReports: { $sum: 1 },
          pendingReports: {
            $sum: { $cond: [{ $eq: ['$status', ReportStatus.PENDING] }, 1, 0] }
          },
          underReviewReports: {
            $sum: { $cond: [{ $eq: ['$status', ReportStatus.UNDER_REVIEW] }, 1, 0] }
          },
          resolvedReports: {
            $sum: { $cond: [{ $eq: ['$status', ReportStatus.RESOLVED] }, 1, 0] }
          },
          dismissedReports: {
            $sum: { $cond: [{ $eq: ['$status', ReportStatus.DISMISSED] }, 1, 0] }
          }
        }
      }
    ]);

    const categoryStats = await this.reportModel.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      }
    ]);

    return {
      overview: stats[0] || {
        totalReports: 0,
        pendingReports: 0,
        underReviewReports: 0,
        resolvedReports: 0,
        dismissedReports: 0
      },
      byCategory: categoryStats
    };
  }

  private async generateTicketNumber(): Promise<string> {
    const prefix = 'TMC';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 6).toUpperCase();

    let ticketNumber = `${prefix}-${timestamp}-${random}`;

    // Ensure uniqueness
    const existing = await this.reportModel.findOne({ ticketNumber });
    if (existing) {
      return this.generateTicketNumber(); // Recursive call if collision
    }

    return ticketNumber;
  }
}
