import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

@Schema({ timestamps: true })
export class Follow extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: true })
  follower: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: true })
  following: MongooseSchema.Types.ObjectId;

  @Prop({ default: Date.now })
  followedAt: Date;

  @Prop({ default: true })
  isActive: boolean;
}

export const FollowSchema = SchemaFactory.createForClass(Follow);

// Create compound index for efficient queries
FollowSchema.index({ follower: 1, following: 1 }, { unique: true });
FollowSchema.index({ follower: 1 });
FollowSchema.index({ following: 1 });
