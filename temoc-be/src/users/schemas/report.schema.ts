import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

export enum ReportCategory {
  PROFILE = "profile",
  CONTENT = "content", 
  BEHAVIOR = "behavior",
  TECHNICAL = "technical"
}

export enum ReportSubcategory {
  // Profile subcategories
  FAKE_IMPERSONATION = "fake_impersonation",
  INAPPROPRIATE_CONTENT = "inappropriate_content",
  HARASSMENT_ABUSE = "harassment_abuse",
  SPAM_SCAM = "spam_scam",
  
  // Content subcategories
  COPYRIGHT_INFRINGEMENT = "copyright_infringement",
  INAPPROPRIATE_AUDIO = "inappropriate_audio",
  QUALITY_ISSUES = "quality_issues",
  MISLEADING_INFORMATION = "misleading_information",
  
  // Behavior subcategories
  SPAM_BEHAVIOR = "spam_behavior",
  HARASSMENT = "harassment",
  COMMUNITY_VIOLATIONS = "community_violations",
  
  // Technical subcategories
  PLATFORM_BUGS = "platform_bugs",
  PAYMENT_TOKEN_ISSUES = "payment_token_issues",
  ACCESSIBILITY_ISSUES = "accessibility_issues"
}

export enum ReportStatus {
  PENDING = "pending",
  UNDER_REVIEW = "under_review",
  RESOLVED = "resolved",
  DISMISSED = "dismissed",
  ESCALATED = "escalated"
}

export enum ReportUrgency {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical"
}

@Schema({ timestamps: true })
export class Report extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: true })
  reporter: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User" })
  reportedUser?: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId })
  reportedContent?: MongooseSchema.Types.ObjectId; // Can reference Track, Album, or Library

  @Prop({ required: true, enum: Object.values(ReportCategory) })
  category: ReportCategory;

  @Prop({ required: true, enum: Object.values(ReportSubcategory) })
  subcategory: ReportSubcategory;

  @Prop({ required: true })
  description: string;

  @Prop({ type: [String], default: [] })
  evidenceUrls: string[]; // Screenshots or other evidence

  @Prop({ enum: Object.values(ReportUrgency), default: ReportUrgency.MEDIUM })
  urgency: ReportUrgency;

  @Prop({ enum: Object.values(ReportStatus), default: ReportStatus.PENDING })
  status: ReportStatus;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User" })
  assignedModerator?: MongooseSchema.Types.ObjectId;

  @Prop()
  moderatorNotes?: string;

  @Prop()
  resolutionDate?: Date;

  @Prop()
  resolutionAction?: string;

  @Prop({ type: Object })
  metadata?: Record<string, any>;

  @Prop({ default: false })
  isAnonymous: boolean;

  @Prop()
  ticketNumber: string; // Auto-generated unique ticket number
}

export const ReportSchema = SchemaFactory.createForClass(Report);

// Create indexes for efficient queries
ReportSchema.index({ reporter: 1 });
ReportSchema.index({ reportedUser: 1 });
ReportSchema.index({ status: 1 });
ReportSchema.index({ category: 1 });
ReportSchema.index({ urgency: 1 });
ReportSchema.index({ ticketNumber: 1 }, { unique: true });
