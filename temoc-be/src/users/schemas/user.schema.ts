import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema, Types } from "mongoose";

export enum UserRole {
  FAN = "fan",
  ARTIST = "artist",
  ADMIN = "admin",
}

export enum AuthProvider {
  EMAIL = "email",
  GOOGLE = "google",
  WALLET = "wallet",
}

@Schema({ _id: true }) // Ensure _id is generated for each subdocument
export class SocialLink {
  @Prop()
  _id?: string;

  @Prop({ required: true })
  platform: string;

  @Prop({ required: true })
  url: string;
}

export const SocialLinkSchema = SchemaFactory.createForClass(SocialLink);

@Schema()
export class KYC {
  @Prop({ default: false })
  submitted: boolean;

  @Prop({ default: false })
  verified: boolean;

  // @Prop()
  // verificationDate: Date;

  // @Prop()
  // rejectionReason: string;

  @Prop({ default: "pending", enum: ["pending", "approved", "rejected"] })
  status: string;

  @Prop({ type: [String] })
  documents: string[];
}

@Schema()
export class DemoTrackMetadata {
  @Prop()
  url: string;

  @Prop()
  publicId: string;

  @Prop()
  uploadedAt: Date;

  @Prop()
  title?: string;

  @Prop()
  description?: string;
}

export const DemoTrackMetadataSchema = SchemaFactory.createForClass(DemoTrackMetadata);

@Schema()
export class ArtistProfile {
  @Prop()
  bio: string;

  @Prop({ type: [SocialLinkSchema], default: [] })
  socialLinks: SocialLink[];

  @Prop({ type: Object })
  kyc: KYC;

  @Prop({ default: false })
  isVerified: boolean;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: "Token" }] })
  tokens: MongooseSchema.Types.ObjectId[];

  @Prop()
  genre: string;

  @Prop()
  coverPhoto: string;

  @Prop()
  profilePic: string;

  // Demo tracks URLs
  @Prop({ type: [String], default: [] })
  demoTracks: string[];

  // Demo tracks metadata
  @Prop({ type: [DemoTrackMetadataSchema], default: [] })
  demoTrackMetadata: DemoTrackMetadata[];

  @Prop({ default: false })
  applicationSubmitted: boolean;

  @Prop({ enum: ["pending", "approved", "rejected"], default: "pending" })
  status: string;

  @Prop()
  applicationSubmittedAt: Date;

  @Prop()
  applicationReviewedBy: string; // Admin ID or username

  @Prop()
  applicationReviewDate: Date;

  @Prop()
  rejectionReason: string;
}

export const ArtistProfileSchema = SchemaFactory.createForClass(ArtistProfile);

@Schema()
export class Wallet {
  @Prop({ required: true })
  address: string;

  @Prop({ required: true })
  chainId: number;

  @Prop({ required: true })
  provider: string;
}

@Schema({ timestamps: true })
export class User extends Document {
  @Prop({ required: true })
  email: string;
  @Prop({ required: true })
  firstName: string;
  @Prop({ required: true })
  lastName: string;
  @Prop({ required: true, unique: true })
  username: string;

  @Prop({ type: String })
  address?: string;

  @Prop()
  password: string; // Hashed password (if using email/password)
  @Prop({ default: true })
  tokenCreated: boolean;
  @Prop({
    required: true,
    enum: Object.values(UserRole),
    default: UserRole.FAN,
  })
  role: UserRole;

  @Prop({ type: [{ type: Object }] })
  wallets: Wallet[];

  @Prop({ unique: true, sparse: true })
  dynamicUserId: string;

  @Prop()
  displayName: string;
  @Prop()
  description: string;
  @Prop()
  avatarUrl: string;

  @Prop()
  coverPicture: string;

  @Prop({ type: ArtistProfileSchema })
  artistProfile: ArtistProfile;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: "Token" }] })
  ownedTokens: MongooseSchema.Types.ObjectId[];

  @Prop({ enum: Object.values(AuthProvider) })
  authProvider: AuthProvider;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: false })
  isAdmin: boolean;

  @Prop()
  lastLogin: Date;

  @Prop({ type: Object })
  metadata: Record<string, any>; // For any additional user data

  // New fields for artist application process
  @Prop({ default: false })
  isArtistApplicationInProgress: boolean;

  @Prop({ default: 1 }) // To resume where left off
  currentArtistFormStep: number;

  // Social features - Phase 1
  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: "User" }], default: [] })
  following: MongooseSchema.Types.ObjectId[];

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: "User" }], default: [] })
  followers: MongooseSchema.Types.ObjectId[];

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: "User" }], default: [] })
  blockedUsers: MongooseSchema.Types.ObjectId[];

  @Prop({ default: Date.now })
  lastActiveAt: Date;

  @Prop({ default: true })
  isProfilePublic: boolean;

  @Prop()
  location: string;

  @Prop({ default: 0 })
  profileViews: number;
}

export const UserSchema = SchemaFactory.createForClass(User);
