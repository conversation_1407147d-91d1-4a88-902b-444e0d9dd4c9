import {
  Controller,
  Post,
  Delete,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  DefaultValuePipe
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { AuthUser } from "../auth/decorators/user.decorator";
import { SocialService } from "./social.service";

@ApiTags("Social")
@ApiBearerAuth("JWT-auth")
@Controller("social")
export class SocialController {
  constructor(private readonly socialService: SocialService) {}

  @UseGuards(JwtAuthGuard)
  @Post("follow/:userId")
  @ApiOperation({ summary: "Follow a user" })
  @ApiResponse({ status: 200, description: "Successfully followed user" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User not found" })
  @ApiResponse({ status: 409, description: "Already following user" })
  async followUser(
    @AuthUser() user: any,
    @Param("userId") userId: string
  ) {
    console.log(`Follow API called - Current user: ${user._id}, Target user: ${userId}`);
    console.log(`User object:`, user);
    return this.socialService.followUser(user._id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete("follow/:userId")
  @ApiOperation({ summary: "Unfollow a user" })
  @ApiResponse({ status: 200, description: "Successfully unfollowed user" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User not found" })
  async unfollowUser(
    @AuthUser() user: any,
    @Param("userId") userId: string
  ) {
    return this.socialService.unfollowUser(user._id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get("followers/:userId")
  @ApiOperation({ summary: "Get user's followers" })
  @ApiResponse({ status: 200, description: "Followers retrieved successfully" })
  async getFollowers(
    @Param("userId") userId: string,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number
  ) {
    return this.socialService.getFollowers(userId, page, limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("following/:userId")
  @ApiOperation({ summary: "Get users that a user is following" })
  @ApiResponse({ status: 200, description: "Following list retrieved successfully" })
  async getFollowing(
    @Param("userId") userId: string,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number
  ) {
    return this.socialService.getFollowing(userId, page, limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("is-following/:userId")
  @ApiOperation({ summary: "Check if current user is following another user" })
  @ApiResponse({ status: 200, description: "Follow status retrieved" })
  async isFollowing(
    @AuthUser() user: any,
    @Param("userId") userId: string
  ) {
    console.log(`Is following check - Current user: ${user._id}, Target user: ${userId}`);
    const isFollowing = await this.socialService.isFollowing(user._id, userId);
    console.log(`Is following result: ${isFollowing}`);
    return { isFollowing };
  }

  @UseGuards(JwtAuthGuard)
  @Post("block/:userId")
  @ApiOperation({ summary: "Block a user" })
  @ApiResponse({ status: 200, description: "User blocked successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "User not found" })
  async blockUser(
    @AuthUser() user: any,
    @Param("userId") userId: string
  ) {
    return this.socialService.blockUser(user._id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete("block/:userId")
  @ApiOperation({ summary: "Unblock a user" })
  @ApiResponse({ status: 200, description: "User unblocked successfully" })
  async unblockUser(
    @AuthUser() user: any,
    @Param("userId") userId: string
  ) {
    return this.socialService.unblockUser(user._id, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get("stats/:userId")
  @ApiOperation({ summary: "Get follow statistics for a user" })
  @ApiResponse({ status: 200, description: "Statistics retrieved successfully" })
  async getFollowStats(@Param("userId") userId: string) {
    return this.socialService.getFollowStats(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get("my-followers")
  @ApiOperation({ summary: "Get current user's followers" })
  @ApiResponse({ status: 200, description: "Followers retrieved successfully" })
  async getMyFollowers(
    @AuthUser() user: any,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number
  ) {
    return this.socialService.getFollowers(user._id, page, limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("my-following")
  @ApiOperation({ summary: "Get users that current user is following" })
  @ApiResponse({ status: 200, description: "Following list retrieved successfully" })
  async getMyFollowing(
    @AuthUser() user: any,
    @Query("page", new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query("limit", new DefaultValuePipe(20), ParseIntPipe) limit: number
  ) {
    return this.socialService.getFollowing(user._id, page, limit);
  }

  @UseGuards(JwtAuthGuard)
  @Get("my-stats")
  @ApiOperation({ summary: "Get current user's follow statistics" })
  @ApiResponse({ status: 200, description: "Statistics retrieved successfully" })
  async getMyStats(@AuthUser() user: any) {
    return this.socialService.getFollowStats(user._id);
  }

  // Test endpoint without authentication for debugging
  @Post("test-follow/:followerId/:followingId")
  @ApiOperation({ summary: "Test follow functionality without auth (for debugging)" })
  @ApiResponse({ status: 200, description: "Successfully followed user" })
  async testFollowUser(
    @Param("followerId") followerId: string,
    @Param("followingId") followingId: string
  ) {
    return this.socialService.followUser(followerId, followingId);
  }

  @Delete("test-follow/:followerId/:followingId")
  @ApiOperation({ summary: "Test unfollow functionality without auth (for debugging)" })
  @ApiResponse({ status: 200, description: "Successfully unfollowed user" })
  async testUnfollowUser(
    @Param("followerId") followerId: string,
    @Param("followingId") followingId: string
  ) {
    return this.socialService.unfollowUser(followerId, followingId);
  }

  @Get("debug/user/:userId")
  @ApiOperation({ summary: "Debug user existence" })
  async debugUser(@Param("userId") userId: string) {
    return this.socialService.debugUser(userId);
  }
}
