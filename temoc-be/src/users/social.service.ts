import { Injectable, NotFoundException, BadRequestException, ConflictException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { User } from "./schemas/user.schema";
import { Follow } from "./schemas/follow.schema";

@Injectable()
export class SocialService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(Follow.name) private followModel: Model<Follow>
  ) {}

  async followUser(followerId: string, followingId: string) {
    console.log(`Follow request: ${followerId} -> ${followingId}`);

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(followerId) || !Types.ObjectId.isValid(followingId)) {
      console.log(`Invalid ObjectId format - Follower: ${followerId}, Following: ${followingId}`);
      throw new BadRequestException('Invalid user ID format');
    }

    // Validate users exist
    const [follower, following] = await Promise.all([
      this.userModel.findById(followerId),
      this.userModel.findById(followingId)
    ]);

    console.log(`Follower found: ${!!follower}, Following found: ${!!following}`);

    if (!follower || !following) {
      console.log(`User not found - Follower: ${followerId}, Following: ${followingId}`);
      throw new NotFoundException('User not found');
    }

    if (followerId === followingId) {
      throw new BadRequestException('Cannot follow yourself');
    }

    // Check if already following
    const existingFollow = await this.followModel.findOne({
      follower: new Types.ObjectId(followerId),
      following: new Types.ObjectId(followingId),
      isActive: true
    });

    if (existingFollow) {
      console.log('User is already following this person');
      return { success: true, message: 'Already following this user', alreadyFollowing: true };
    }

    // Check if user is blocked
    const isBlocked = following.blockedUsers?.some(
      blockedId => blockedId.toString() === followerId
    );

    if (isBlocked) {
      throw new BadRequestException('Cannot follow this user');
    }

    try {
      // Create follow relationship
      const follow = new this.followModel({
        follower: new Types.ObjectId(followerId),
        following: new Types.ObjectId(followingId)
      });

      const savedFollow = await follow.save();
      console.log(`Follow relationship saved: ${savedFollow._id}`);

      // Update user documents
      const updateResults = await Promise.all([
        this.userModel.findByIdAndUpdate(followerId, {
          $addToSet: { following: new Types.ObjectId(followingId) }
        }),
        this.userModel.findByIdAndUpdate(followingId, {
          $addToSet: { followers: new Types.ObjectId(followerId) }
        })
      ]);

      console.log(`User documents updated - Follower: ${!!updateResults[0]}, Following: ${!!updateResults[1]}`);

      // Verify the follow was saved
      const verifyFollow = await this.followModel.findOne({
        follower: new Types.ObjectId(followerId),
        following: new Types.ObjectId(followingId),
        isActive: true
      });
      console.log(`Follow verification: ${!!verifyFollow}`);

      return { success: true, message: 'Successfully followed user' };
    } catch (error) {
      // Handle duplicate key error (user already following)
      if (error.code === 11000) {
        console.log('User is already following this person (caught duplicate key error)');
        return { success: true, message: 'Already following this user', alreadyFollowing: true };
      }
      throw error;
    }
  }

  async unfollowUser(followerId: string, followingId: string) {
    console.log(`Unfollow request: ${followerId} -> ${followingId}`);

    // Validate ObjectId format
    if (!Types.ObjectId.isValid(followerId) || !Types.ObjectId.isValid(followingId)) {
      console.log(`Invalid ObjectId format - Follower: ${followerId}, Following: ${followingId}`);
      throw new BadRequestException('Invalid user ID format');
    }

    // Validate users exist
    const [follower, following] = await Promise.all([
      this.userModel.findById(followerId),
      this.userModel.findById(followingId)
    ]);

    if (!follower || !following) {
      throw new NotFoundException('User not found');
    }

    // Check if following
    const existingFollow = await this.followModel.findOne({
      follower: new Types.ObjectId(followerId),
      following: new Types.ObjectId(followingId),
      isActive: true
    });

    if (!existingFollow) {
      console.log('User is not following this person');
      return { success: true, message: 'Not following this user', notFollowing: true };
    }

    // Remove follow relationship
    await this.followModel.findByIdAndUpdate(existingFollow._id, {
      isActive: false
    });

    // Update user documents
    await Promise.all([
      this.userModel.findByIdAndUpdate(followerId, {
        $pull: { following: new Types.ObjectId(followingId) }
      }),
      this.userModel.findByIdAndUpdate(followingId, {
        $pull: { followers: new Types.ObjectId(followerId) }
      })
    ]);

    return { success: true, message: 'Successfully unfollowed user' };
  }

  async getFollowers(userId: string, page: number = 1, limit: number = 20) {
    const skip = (page - 1) * limit;

    const followers = await this.followModel
      .find({ following: new Types.ObjectId(userId), isActive: true })
      .populate('follower', 'username displayName avatarUrl role')
      .sort({ followedAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await this.followModel.countDocuments({
      following: new Types.ObjectId(userId),
      isActive: true
    });

    return {
      followers: followers.map(f => f.follower),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async getFollowing(userId: string, page: number = 1, limit: number = 20) {
    const skip = (page - 1) * limit;

    const following = await this.followModel
      .find({ follower: new Types.ObjectId(userId), isActive: true })
      .populate('following', 'username displayName avatarUrl role')
      .sort({ followedAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await this.followModel.countDocuments({
      follower: new Types.ObjectId(userId),
      isActive: true
    });

    return {
      following: following.map(f => f.following),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async isFollowing(followerId: string, followingId: string): Promise<boolean> {
    console.log(`Is following check - Current user: ${followerId}, Target user: ${followingId}`);

    const follow = await this.followModel.findOne({
      follower: new Types.ObjectId(followerId),
      following: new Types.ObjectId(followingId),
      isActive: true
    });

    console.log(`Is following result: ${!!follow}`);
    return !!follow;
  }

  async blockUser(userId: string, blockedUserId: string) {
    if (userId === blockedUserId) {
      throw new BadRequestException('Cannot block yourself');
    }

    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Add to blocked users
    await this.userModel.findByIdAndUpdate(userId, {
      $addToSet: { blockedUsers: new Types.ObjectId(blockedUserId) }
    });

    // Remove any existing follow relationships
    await Promise.all([
      this.unfollowUser(userId, blockedUserId).catch(() => {}),
      this.unfollowUser(blockedUserId, userId).catch(() => {})
    ]);

    return { success: true, message: 'User blocked successfully' };
  }

  async unblockUser(userId: string, blockedUserId: string) {
    await this.userModel.findByIdAndUpdate(userId, {
      $pull: { blockedUsers: new Types.ObjectId(blockedUserId) }
    });

    return { success: true, message: 'User unblocked successfully' };
  }

  async getFollowStats(userId: string) {
    const [followersCount, followingCount] = await Promise.all([
      this.followModel.countDocuments({
        following: new Types.ObjectId(userId),
        isActive: true
      }),
      this.followModel.countDocuments({
        follower: new Types.ObjectId(userId),
        isActive: true
      })
    ]);

    return {
      followersCount,
      followingCount
    };
  }

  async debugUser(userId: string) {
    console.log(`Debug user: ${userId}`);
    const user = await this.userModel.findById(userId);
    console.log(`User found: ${!!user}`);
    if (user) {
      console.log(`User details: ${user.username}, ${user.displayName}`);
    }
    return {
      userId,
      exists: !!user,
      user: user ? {
        id: user._id,
        username: user.username,
        displayName: user.displayName,
        role: user.role
      } : null
    };
  }
}
