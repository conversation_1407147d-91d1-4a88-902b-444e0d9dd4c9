import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
  Logger,
  UploadedFiles,
} from "@nestjs/common";
import {
  FileFieldsInterceptor,
  FileInterceptor,
} from "@nestjs/platform-express";
import { UsersService } from "./users.service";
import { SocialService } from "./social.service";
import { User, UserRole } from "./schemas/user.schema";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { RolesGuard } from "../auth/guards/roles.guard";
import { Roles } from "../auth/decorators/roles.decorator";
import { BecomeArtistDto } from "./dto/become-artist.dto";
import { UpdateArtistProfileDto } from "./dto/update-artist-profile.dto";
import { CloudinaryService } from "../cloudinary/cloudinary.service";
import { memoryStorage } from "multer";
import { UpdateSocialLinkDto } from "./dto/update-social-link.dto";
import { AddSocialLinkDto } from "./dto/add-social-link.dto";
import { BasicInfoDto, DemoTracksDto, SocialLinkDto, SocialLinksDto, UsernameDto } from "./dto/artist-application.dto";
// import { BasicInfoDto } from "./dto/basic-info.dto";
// import { UsernameDto } from "./dto/username.dto";
// import { SocialLinksDto } from "./dto/social-links.dto";
// import { DemoTracksDto } from "./dto/demo-tracks.dto";

@Controller("users")
export class UsersController {
  private readonly logger = new Logger(UsersController.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly cloudinaryService: CloudinaryService,
    private readonly socialService: SocialService
  ) {}

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get()
  async findAll(
    @Request() req,
    @Param("role") role?: UserRole
  ): Promise<User[]> {
    if (role) {
      return this.usersService.findByRole(role);
    }
    return this.usersService.findAll();
  }

  @UseGuards(JwtAuthGuard)
  @Get("profile")
  async getProfile(@Request() req) {
    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    return user;
  }

  @Get("profile/:username")
  async getPublicProfile(@Param("username") username: string, @Request() req) {
    const currentUserId = req.user?._id;
    const user = await this.usersService.findByUsername(username);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.isActive) {
      throw new NotFoundException("User not found");
    }

    // Check if profile is public or if user is viewing their own profile
    if (!user.isProfilePublic && currentUserId !== user._id.toString()) {
      throw new ForbiddenException("This profile is private");
    }

    // Increment profile views if not viewing own profile
    if (currentUserId && currentUserId !== user._id.toString()) {
      await this.usersService.incrementProfileViews(user._id.toString());
    }

    // Get follow status if user is logged in and not viewing own profile
    let isFollowing = false;
    if (currentUserId && currentUserId !== user._id.toString()) {
      isFollowing = await this.socialService.isFollowing(currentUserId, user._id.toString());
    }

    // Return public profile data
    const publicProfile = {
      _id: user._id,
      username: user.username,
      displayName: user.displayName,
      firstName: user.firstName,
      lastName: user.lastName,
      avatarUrl: user.avatarUrl,
      coverPicture: user.coverPicture,
      description: user.description,
      location: user.location,
      role: user.role,
      profileViews: user.profileViews,
      lastActiveAt: user.lastActiveAt,
      createdAt: (user as any).createdAt,
      isFollowing, // Add follow status to profile response
      artistProfile: user.role === UserRole.ARTIST ? {
        bio: user.artistProfile?.bio,
        genre: user.artistProfile?.genre,
        isVerified: user.artistProfile?.isVerified,
        socialLinks: user.artistProfile?.socialLinks || [],
        coverPhoto: user.artistProfile?.coverPhoto,
        profilePic: user.artistProfile?.profilePic,
        status: user.artistProfile?.status,
        applicationSubmitted: user.artistProfile?.applicationSubmitted,
        applicationSubmittedAt: user.artistProfile?.applicationSubmittedAt,
        tokens: user.artistProfile?.tokens || []
      } : undefined
    };

    return publicProfile;
  }

  @Get("profile/:username/stats")
  async getPublicProfileStats(@Param("username") username: string) {
    const user = await this.usersService.findByUsername(username);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (!user.isActive) {
      throw new NotFoundException("User not found");
    }

    // Get user statistics
    const stats = await this.usersService.getUserStats(user._id.toString());

    return {
      success: true,
      stats
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put("profile")
  async updateProfile(@Request() req, @Body() updateUserDto: any) {
    const userId = req.user._id;

    // Don't allow changing role or admin status through this endpoint
    delete updateUserDto.role;
    delete updateUserDto.isAdmin;

    return this.usersService.update(userId, updateUserDto);
  }

  // ===== NEW ARTIST METHODS =====
  @UseGuards(JwtAuthGuard)
  @Get("artist-profile")
  async getArtistProfile(@Request() req) {
    const userId = req.user._id;
    return this.usersService.getArtistProfile(userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post("become-artist")
  async becomeArtist(@Request() req, @Body() becomeArtistDto: BecomeArtistDto) {
    this.logger.log(`User ${req.user._id} requesting to become an artist`);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role === UserRole.ARTIST) {
      throw new BadRequestException("User is already an artist");
    }

    const updatedUser = await this.usersService.becomeArtist(
      userId,
      becomeArtistDto.reason
    );

    return {
      success: true,
      message: "Successfully became an artist",
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        displayName: `${updatedUser.firstName} ${updatedUser.lastName}`,
      },
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put("update-artist-profile")
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: "profilePicture", maxCount: 1 },
        { name: "coverPhoto", maxCount: 1 },
      ],
      {
        storage: memoryStorage(), // Use memory storage instead of disk
      }
    )
  )
  async updateArtistProfile(
    @Request() req,
    @Body() updateArtistProfileDto: UpdateArtistProfileDto,
    @UploadedFiles()
    files: {
      profilePicture?: Express.Multer.File[];
      coverPhoto?: Express.Multer.File[];
    }
  ) {
    this.logger.log(`Updating artist profile for user ${req.user._id}`);
    if (
      files?.profilePicture?.[0] &&
      (!files.profilePicture[0].buffer ||
        files.profilePicture[0].buffer.length === 0)
    ) {
      throw new BadRequestException("Profile picture file is empty or invalid");
    }

    if (
      files?.coverPhoto?.[0] &&
      (!files.coverPhoto[0].buffer || files.coverPhoto[0].buffer.length === 0)
    ) {
      throw new BadRequestException("Cover photo file is empty or invalid");
    }

    console.log("Files received:", files);
    if (files?.profilePicture?.[0]) {
      console.log("Profile pic details:", {
        filename: files.profilePicture[0].originalname,
        size: files.profilePicture[0].size,
        mimetype: files.profilePicture[0].mimetype,
        buffer: files.profilePicture[0].buffer ? "Buffer exists" : "No buffer",
        bufferLength: files.profilePicture[0].buffer
          ? files.profilePicture[0].buffer.length
          : 0,
      });
    }
    if (
      files?.profilePicture?.[0] &&
      (!files.profilePicture[0].buffer ||
        files.profilePicture[0].buffer.length === 0)
    ) {
      throw new BadRequestException("Profile picture file is empty or invalid");
    }
    //   const userId = '682b1d6e598c77cb693ea3fc';
    const userId = req.user._id;

    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can update artist profiles");
    }

    // Validate file types if needed
    if (files?.profilePicture?.[0]) {
      const file = files.profilePicture[0];
      if (!file.mimetype.startsWith("image/")) {
        throw new BadRequestException("Profile picture must be an image");
      }
    }

    if (files?.coverPhoto?.[0]) {
      const file = files.coverPhoto[0];
      if (!file.mimetype.startsWith("image/")) {
        throw new BadRequestException("Cover photo must be an image");
      }
    }
    console.log(updateArtistProfileDto, files, "updateArtistProfileDto");

    return this.usersService.updateArtistProfileComprehensive(
      userId,
      updateArtistProfileDto,
      files
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post("upload-profile-picture")
  @UseInterceptors(
    FileFieldsInterceptor(
      [
        { name: "profilePicture", maxCount: 1 },
        { name: "coverPhoto", maxCount: 1 },
      ],
      {
        storage: memoryStorage(), // Use memory storage instead of disk
      }
    )
  )
  async uploadProfilePicture(
    @Request() req,
    @UploadedFiles()
    files: {
      profilePicture?: Express.Multer.File[];
      coverPhoto?: Express.Multer.File[];
    }
  ) {
    // Get the profile picture file
    const profilePictureFile = files?.profilePicture?.[0];

    if (!profilePictureFile) {
      throw new BadRequestException("No profile picture uploaded");
    }

    this.logger.log(
      `Uploading profile picture for user ${req.user._id || req.user.sub}`
    );

    // Get user ID from JWT token
    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    try {
      // Verify buffer exists and has content
      if (
        !profilePictureFile.buffer ||
        profilePictureFile.buffer.length === 0
      ) {
        throw new BadRequestException("Empty file");
      }

      // Upload to Cloudinary
      const result = await this.cloudinaryService.uploadImage(
        profilePictureFile,
        "temoc-profile-pictures"
      );

      // Update user avatar URL
      await this.usersService.update(userId, {
        avatarUrl: result.secure_url,
        metadata: {
          ...user.metadata,
          profileImage: {
            publicId: result.public_id,
            url: result.secure_url,
            uploadedAt: new Date(),
          },
        },
      });

      return {
        success: true,
        message: "Profile picture uploaded successfully",
        imageUrl: result.secure_url,
      };
    } catch (error) {
      this.logger.error(`Error uploading profile picture: ${error.message}`);
      throw new BadRequestException(`Failed to upload image: ${error.message}`);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post("upload-cover-photo")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: memoryStorage(),
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
      fileFilter: (req, file, cb) => {
        if (!file.mimetype.match(/^image\/(jpeg|png|gif|jpg)$/)) {
          return cb(
            new BadRequestException("Only image files are allowed"),
            false
          );
        }
        cb(null, true);
      },
    })
  )
  async uploadCoverPhoto(
    @Request() req,
    @UploadedFile() file: Express.Multer.File
  ) {
    if (!file) {
      throw new BadRequestException("No file uploaded");
    }

    this.logger.log(`Uploading cover photo for user ${req.user._id}`);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    try {
      // Verify buffer exists and has content
      if (!file.buffer || file.buffer.length === 0) {
        throw new BadRequestException("Empty file");
      }

      // Upload to Cloudinary
      const result = await this.cloudinaryService.uploadImage(
        file,
        "temoc-cover-photos"
      );

      // Update user with cover photo URL
      await this.usersService.update(userId, {
        coverPicture: result.secure_url, // Store directly on user
        metadata: {
          ...user.metadata,
          coverPhoto: {
            publicId: result.public_id,
            url: result.secure_url,
            uploadedAt: new Date(),
          },
        },
      });

      return {
        success: true,
        message: "Cover photo uploaded successfully",
        imageUrl: result.secure_url,
      };
    } catch (error) {
      this.logger.error(`Error uploading cover photo: ${error.message}`);
      throw new BadRequestException(`Failed to upload image: ${error.message}`);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Post("switch-view")
  async switchView(@Request() req) {
    this.logger.log(`User ${req.user._id} switching view mode`);

    const userId = req.user._id;
    const viewModeResult = await this.usersService.switchViewMode(userId);

    return {
      success: true,
      message: `Switched to ${viewModeResult.viewMode} view`,
      role: viewModeResult.role,
      viewMode: viewModeResult.viewMode,
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get("current-view")
  async getCurrentView(@Request() req) {
    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    // Determine viewMode with proper defaults
    let viewMode = user.metadata?.viewMode;

    // If no viewMode is set, default based on role
    if (!viewMode) {
      viewMode = user.role.toLowerCase(); // 'fan' or 'artist'

      // Update user metadata to include the default viewMode for future requests
      await this.usersService.update(userId, {
        metadata: {
          ...user.metadata,
          viewMode: viewMode,
        },
      });
    }

    return {
      role: user.role,
      viewMode: viewMode,
      canSwitch: user.role === UserRole.ARTIST,
    };
  }

  // ===== SOCIAL MEDIA MANAGEMENT ENDPOINTS =====

  @UseGuards(JwtAuthGuard)
  @Post("artist/social-links")
  async addSocialLink(
    @Request() req,
    @Body() addSocialLinkDto: AddSocialLinkDto
  ) {
    this.logger.log(`Adding social link for user ${req.user._id}`);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can manage social links");
    }

    return this.usersService.addSocialLink(userId, addSocialLinkDto);
  }

  @UseGuards(JwtAuthGuard)
  @Put("artist/social-links/:id") // use _id instead of index
  async updateSocialLink(
    @Request() req,
    @Param("id") id: string,
    @Body() updateSocialLinkDto: UpdateSocialLinkDto
  ) {
    this.logger.log(`Updating social link ${id} for user ${req.user._id}`);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can manage social links");
    }

    return this.usersService.updateSocialLinkById(
      userId,
      id,
      updateSocialLinkDto
    );
  }

  @UseGuards(JwtAuthGuard)
  @Delete("artist/social-links/:id") // 'id' = _id of social link
  async deleteSocialLink(@Request() req, @Param("id") id: string) {
    this.logger.log(
      `Deleting social link with _id ${id} for user ${req.user._id}`
    );

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can manage social links");
    }

    return this.usersService.deleteSocialLinkById(userId, id);
  }

  @UseGuards(JwtAuthGuard)
  @Get("artist/social-links")
  async getSocialLinks(@Request() req) {
    this.logger.log(`Getting social links for user ${req.user._id}`);

    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException("User not found");
    }

    if (user.role !== UserRole.ARTIST) {
      throw new ForbiddenException("Only artists can view their social links");
    }

    return {
      socialLinks: user.artistProfile?.socialLinks || [],
      count: user.artistProfile?.socialLinks?.length || 0,
    };
  }

  // ===== ARTIST APPLICATION ENDPOINTS =====

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/step-1')
  async updateArtistApplicationStep1(@Request() req, @Body() basicInfoDto: BasicInfoDto) {
    const userId = req.user._id;
    const updatedUser = await this.usersService.updateArtistApplicationStep1(userId, basicInfoDto);

    return {
      success: true,
      message: 'Basic info updated successfully',
      currentStep: updatedUser.currentArtistFormStep
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/step-2')
  async updateArtistApplicationStep2(@Request() req, @Body() usernameDto: UsernameDto) {
    const userId = req.user._id;
    const updatedUser = await this.usersService.updateArtistApplicationStep2(userId, usernameDto);

    return {
      success: true,
      message: 'Username updated successfully',
      currentStep: updatedUser.currentArtistFormStep
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/step-3/links')
  async addArtistApplicationSocialLink(@Request() req, @Body() socialLinkDto: SocialLinkDto) {
    const userId = req.user._id;
    const updatedUser = await this.usersService.addArtistApplicationSocialLink(userId, socialLinkDto);

    return {
      success: true,
      message: 'Social link added successfully',
      socialLinks: updatedUser.artistProfile.socialLinks
    };
  }

  @UseGuards(JwtAuthGuard)
  @Put('artist-application/step-3/links/:id')
  async updateArtistApplicationSocialLink(
    @Request() req,
    @Param('id') id: string,
    @Body() socialLinkDto: SocialLinkDto
  ) {
    const userId = req.user._id;
    const updatedUser = await this.usersService.updateArtistApplicationSocialLink(userId, id, socialLinkDto);

    return {
      success: true,
      message: 'Social link updated successfully',
      socialLinks: updatedUser.artistProfile.socialLinks
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete('artist-application/step-3/links/:id')
  async deleteArtistApplicationSocialLink(@Request() req, @Param('id') id: string) {
    const userId = req.user._id;
    const updatedUser = await this.usersService.deleteArtistApplicationSocialLink(userId, id);

    return {
      success: true,
      message: 'Social link deleted successfully',
      socialLinks: updatedUser.artistProfile.socialLinks
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('artist-application/step-3/links')
  async getArtistApplicationSocialLinks(@Request() req) {
    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return {
      success: true,
      socialLinks: user.artistProfile?.socialLinks || []
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/step-3/complete')
  async completeArtistApplicationStep3(@Request() req) {
    const userId = req.user._id;
    const user = await this.usersService.findById(userId);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Move to next step without changing social links
    const updatedUser = await this.usersService.update(userId, {
      currentArtistFormStep: 4
    });

    return {
      success: true,
      message: 'Social links step completed',
      currentStep: updatedUser.currentArtistFormStep
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/step-4')
  @UseInterceptors(FileInterceptor('file', {
    storage: memoryStorage(),
    limits: {
      fileSize: 50 * 1024 * 1024, // 50MB limit
    },
    fileFilter: (req, file, cb) => {
      // Check if file is an audio/video file
      if (!file.mimetype.match(/^audio\/(mp3|wav|mpeg|ogg)$/) &&
          !file.mimetype.match(/^video\/(mp4|quicktime|x-msvideo)$/)) {
        return cb(new BadRequestException('Only audio/video files are allowed'), false);
      }
      cb(null, true);
    },
  }))
  async updateArtistApplicationStep4(@Request() req, @UploadedFile() file: Express.Multer.File) {
    const userId = req.user._id;

    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      // Upload to Cloudinary
      const result = await this.cloudinaryService.uploadAudio(file, 'temoc-demo-tracks');

      // Add the URL to the user's demo tracks and update step
      const updatedUser = await this.usersService.addDemoTrackAndUpdateStep(userId, result.secure_url, result.public_id);

      return {
        success: true,
        message: 'Demo track uploaded successfully',
        trackUrl: result.secure_url,
        publicId: result.public_id,
        currentStep: updatedUser.currentArtistFormStep
      };
    } catch (error) {
      throw new BadRequestException(`Failed to upload demo track: ${error.message}`);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('artist-application/demo-tracks')
  async getDemoTracks(@Request() req) {
    const userId = req.user._id;
    const tracks = await this.usersService.getDemoTracks(userId);

    return {
      success: true,
      tracks
    };
  }

  @UseGuards(JwtAuthGuard)
  @Delete('artist-application/demo-tracks/:trackId')
  async removeDemoTrack(@Request() req, @Param('trackId') trackId: string) {
    const userId = req.user._id;

    await this.usersService.removeDemoTrackById(userId, trackId);

    return {
      success: true,
      message: 'Demo track removed successfully'
    };
  }

  @UseGuards(JwtAuthGuard)
  @Post('artist-application/submit')
  async submitArtistApplication(@Request() req) {
    const userId = req.user._id;

    await this.usersService.submitArtistApplication(userId);

    return {
      success: true,
      message: 'Artist application submitted successfully'
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('artist-application/progress')
  async getArtistApplicationProgress(@Request() req) {
    const userId = req.user._id;

    const progress = await this.usersService.getArtistApplicationProgress(userId);

    return {
      success: true,
      progress
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('artist-application/current-step')
  async getCurrentArtistApplicationStep(@Request() req) {
    const userId = req.user._id;
    const progress = await this.usersService.getArtistApplicationProgress(userId);

    return {
      success: true,
      currentStep: progress.currentStep,
      isComplete: progress.currentStep >= 5,
      isSubmitted: progress.isSubmitted,
      progress
    };
  }
}
