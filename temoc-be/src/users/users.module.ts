import { Module, forwardRef } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { MulterModule } from "@nestjs/platform-express";
import { User, UserSchema } from "./schemas/user.schema";
import { Follow, FollowSchema } from "./schemas/follow.schema";
import { Report, ReportSchema } from "./schemas/report.schema";
import { Token, TokenSchema } from "../tokens/entities/token.entity";
import { Track, TrackSchema } from "../content/schemas/track.schema";
import { Album, AlbumSchema } from "../content/schemas/album.schema";
import { Library, LibrarySchema } from "../content/schemas/library.schema";
import { UsersService } from "./users.service";
import { UsersController } from "./users.controller";
import { SocialService } from "./social.service";
import { SocialController } from "./social.controller";
import { DiscoveryService } from "./discovery.service";
import { DiscoveryController } from "./discovery.controller";
import { ReportService } from "./report.service";
import { ReportController } from "./report.controller";
import { CloudinaryModule } from "../cloudinary/cloudinary.module";
import { DynamicModule } from "../dynamic/dynamic.module";
import { JwtService } from "@nestjs/jwt";
import { EmailModule } from "../email/email.module";
import { AuthModule } from "../auth/auth.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Follow.name, schema: FollowSchema },
      { name: Report.name, schema: ReportSchema },
      { name: Token.name, schema: TokenSchema },
      { name: Track.name, schema: TrackSchema },
      { name: Album.name, schema: AlbumSchema },
      { name: Library.name, schema: LibrarySchema }
    ]),
    forwardRef(() => AuthModule),
    EmailModule,
    MulterModule.register({
      dest: "./uploads", // Temporary storage before Cloudinary
    }),
    CloudinaryModule,
    DynamicModule,
  ],
  controllers: [
    UsersController,
    SocialController,
    DiscoveryController,
    ReportController
  ],
  providers: [
    UsersService,
    SocialService,
    DiscoveryService,
    ReportService,
    JwtService
  ],
  exports: [
    UsersService,
    SocialService,
    DiscoveryService,
    ReportService
  ],
})
export class UsersModule {}
