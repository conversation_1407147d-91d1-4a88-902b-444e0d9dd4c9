export function getRpcUrlByChainId(chainId: number): string {
  switch (chainId) {
    case 1: // Ethereum Mainnet
      return "https://mainnet.infura.io/v3/********************************";
    case 8453: // Base Mainnet
      return "https://base-mainnet.g.alchemy.com/v2/3-_rEWyUc-m9QgySIlzI22-HBVZJfzLn";
    case 84532: // Base Sepolia Testnet
      return "https://base-sepolia.infura.io/v3/********************************";
    default:
      throw new Error(`Unsupported chainId: ${chainId}`);
  }
}
