{"name": "my-next-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:strict": "next lint '*/**/*.{js,jsx,ts,tsx}'", "prettier": "prettier --write */**/*.{js,jsx,json,ts,tsx,scss,css,md}", "prepare": "husky install"}, "dependencies": {"@dynamic-labs/ethereum": "^4.12.4", "@dynamic-labs/sdk-react-core": "^4.12.4", "@dynamic-labs/wagmi-connector": "^4.12.4", "@greatsumini/react-facebook-login": "^3.4.0", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.4.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-tabs": "^1.1.12", "@rainbow-me/rainbowkit": "^2.1.3", "@react-oauth/google": "^0.12.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tanstack/query-core": "^5.50.1", "@tanstack/react-query": "^5.50.1", "@tanstack/react-query-devtools": "^5.36.2", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@uniswap/v2-periphery": "^1.1.0-beta.0", "@uniswap/v3-periphery": "^1.4.4", "@uniswap/v3-sdk": "^3.25.2", "@wert-io/widget-initializer": "^6.8.0", "@wert-io/widget-sc-signer": "^2.0.1", "aos": "^2.3.4", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "emoji-picker-react": "^4.12.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "ethers": "^6.12.2", "husky": "^9.1.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lint-staged": "^15.2.10", "lucide-react": "^0.378.0", "my-next-app": "file:", "next": "14.0.3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "react": "^18", "react-apple-login": "^1.1.6", "react-apple-signin-auth": "^1.1.1", "react-countdown": "^2.3.5", "react-dom": "^18", "react-hook-form": "^7.51.4", "react-icons": "^5.0.1", "react-loading-skeleton": "^3.5.0", "react-pin-field": "^4.0.2", "react-spinners": "^0.17.0", "react-toastify": "^10.0.5", "recharts": "^2.15.3", "swiper": "^11.2.6", "tailwind-merge": "^2.3.0", "uuid": "^9.0.1", "viem": "^2.27.2", "wagmi": "^2.14.16", "yup": "^1.4.0"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "eslint": "^8.57.1", "eslint-config-next": "^14.0.3", "postcss": "^8", "postcss-import": "^16.1.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "lint-staged": {"*.js": "eslint --fix", "*.ts": "eslint --fix", "*.css": "stylelint --fix", "*.md": "prettier --write"}}