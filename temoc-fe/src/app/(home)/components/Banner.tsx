'use client';
import { Button, Container } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import React, { forwardRef, useEffect } from 'react';
import AOS from 'aos';
// import Link from 'next/link';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';

const Banner = forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>(
  (props, ref) => {
    useEffect(() => {
      AOS.init({
        duration: 1000,
        easing: 'ease-in-out',
      });
    }, []);

    const { setShowAuthFlow, primaryWallet } = useDynamicContext();
    const router = useRouter();
    const queryClient = useQueryClient();
    return (
      <div id="main" ref={ref} {...props}>
        <Container className="pt-12 sm:pt-16 xl:pt-[100px]">
          <div className="relative text-center">
            <img
              src="/assets/images/home/<USER>"
              alt="Built for the Modern Musician"
              className="mx-auto hidden sm:block"
            />
            <img
              src="/assets/images/home/<USER>"
              alt="Built for the Modern Musician"
              className="mx-auto block sm:hidden"
            />
            {/* <img
              src="/assets/images/home/<USER>"
              alt="Built for the Modern Musician"
              className="absolute top-0 z-50 block w-[97.3%] sm:hidden"
            /> */}
            <h1 className="h1 absolute top-[20%] z-50 block w-[97.3%] font-DreamAvenue text-white sm:hidden">
              Own Your Favorite Artist&#39;s Token
            </h1>
            <div className="relative top-0 flex h-full w-full flex-col items-center justify-between sm:absolute">
              <div
                data-aos="fade-up"
                className="cursor-text font-DreamAvenue text-[38px] font-normal text-[#292D32] sm:mt-20 md:text-[3rem] lg:text-[5rem]"
              >
                <img
                  src="/assets/images/home/<USER>"
                  alt="Built for the Modern Musician"
                  className="ml-4 hidden w-[97.3%] sm:block"
                />
              </div>

              <div className="mb-5 flex flex-col items-center gap-5">
                <p className="mt-5 w-[448px] text-sm font-normal !leading-5 tracking-tighter text-[#292D32] sm:text-lg lg:mt-0 xs:w-full">
                  TEMOC connects artists and fans through tokenized content.
                  Support creators directly and unlock exclusive experiences.
                </p>
                <div className="flex justify-center">
                  {/* <Link href="/app/explore" className="block"> */}
                  <Button
                    onClick={() => {
                      if (!primaryWallet?.isConnected) {
                        setShowAuthFlow(true);
                        return;
                      }
                      queryClient.refetchQueries({
                        queryKey: ['auth-user'],
                      });
                      router.replace('/app/explore');
                    }}
                    className="!h-[51px] !w-[220px] !text-lg !font-medium xs:!h-[50px]"
                  >
                    EXPLORE ARTISTS
                  </Button>
                  {/* </Link> */}
                  {/* <Link
                    href="/artist/initial-dashboard"
                    className="block xs:!w-[70%]"
                  >
                    <Button
                      variant="outline"
                      className="!h-[51px] !w-[220px] !text-lg !font-medium xs:!h-[50px] xs:!w-full"
                    >
                      BECOME AN ARTIST
                    </Button>
                  </Link> */}
                </div>
                {/* <img
                  src="/assets/images/home/<USER>/mb.png"
                  alt=""
                  className="mx-auto block sm:hidden bd"
                /> */}
                <ImageComponent
                  src="/assets/images/home/<USER>"
                  figClassName="sm:block hidden"
                  className="mt-8 animate-bounce"
                  height={117}
                  width={108}
                  alt="More"
                />
                <ImageComponent
                  src="/assets/images/home/<USER>"
                  figClassName="sm:hidden block mt-5"
                  className="animate-bounce xs:ml-4"
                  height={97}
                  width={88}
                  alt="More"
                />
              </div>
            </div>
          </div>
        </Container>
      </div>
    );
  },
);

Banner.displayName = 'Banner';

export default Banner;
