'use client';
import { Container } from '@/components/common';
import React, { useState } from 'react';
import { IoMdMusicalNote } from 'react-icons/io';
import { MdColorLens } from 'react-icons/md';
import { PiVideoFill } from 'react-icons/pi';
import { MdOutlineNoteAlt } from 'react-icons/md';
import { BsFillCameraFill } from 'react-icons/bs';

const data = [
  {
    icon: <IoMdMusicalNote />,
    title: 'Music',
    para: '100 Artists',
  },
  {
    icon: <MdColorLens />,
    title: 'Visual Arts',
    para: '75 Artists',
  },
  {
    icon: <PiVideoFill />,
    title: 'Video',
    para: '120 Artists',
  },
  {
    icon: <MdOutlineNoteAlt />,
    title: 'Writing',
    para: '170 Artists',
  },
  {
    icon: <BsFillCameraFill />,
    title: 'Photography',
    para: '53 Artists',
  },
];

const Explore = () => {
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0);
  return (
    <div className="pb-10 pt-10 sm:pb-32 sm:pt-20">
      <Container className="relative">
        <div
          className="absolute -left-8 -top-28 flex w-full justify-end"
          data-aos="fade-up"
        >
          <h2 className="hidden font-DreamAvenue !text-[277.8px] text-[#292D32] opacity-[6%] md:block">
            03
          </h2>
        </div>
        <h2 className="h2 text-center">Explore Categories</h2>
        <div className="z-50 mt-10 flex grid-cols-2 flex-wrap justify-center gap-5 xs:grid">
          {data.map((item, index) => {
            const isSelected = index === selectedIndex;
            return (
              <div
                key={index}
                onClick={() => setSelectedIndex(index)}
                className={`group relative z-20 flex w-full max-w-[205px] cursor-pointer flex-col items-center gap-2 rounded-[10px] border border-[#FF8000] bg-white py-5 text-black transition-all duration-200 hover:bg-[#FF8000] hover:!text-white ${index == data.length - 1 && 'xs:col-span-2 xs:mx-auto'} ${
                  isSelected ? '' : ''
                }`}
              >
                <span
                  className={`text-3xl ${isSelected ? '' : ''} text-[#FF8000] group-hover:text-white`}
                >
                  {item.icon}
                </span>
                <p
                  className={`text-sm font-medium ${isSelected ? '' : ''} text-black group-hover:text-white`}
                >
                  {item.title}
                </p>
                <p
                  className={`text-xs ${isSelected ? '' : ''} text-[#FF8000] group-hover:text-white`}
                >
                  {item.para}
                </p>
              </div>
            );
          })}
        </div>
      </Container>
    </div>
  );
};

export default Explore;
