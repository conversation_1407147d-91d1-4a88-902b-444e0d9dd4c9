'use client';
import { But<PERSON>, Container } from '@/components/common';
import React, { useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.css';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Pagination, Autoplay } from 'swiper/modules';
import SliderCard from '@/components/ui/SliderCard/SliderCard';
import CardSkeleton from '@/components/ui/Skelton/CardSkelton';

const data = [
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '36 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON> <PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '15 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '13 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '27 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'R Kelly',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '19 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Amelia',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '42 songs',
  },
];

function Featured() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setMounted(true);
    }, 200); // Slight delay to avoid flicker
    return () => clearTimeout(timeout);
  }, []);

  return (
    <div className="bg-[#F6F6F6] py-10 sm:py-20">
      <Container className="relative">
        <div
          className="absolute -left-8 -top-48 flex w-full justify-start"
          data-aos="fade-up"
        >
          <h2 className="hidden font-DreamAvenue !text-[277.8px] text-[#292D32] opacity-[6%] md:block">
            02
          </h2>
        </div>
        <div className="flex justify-center sm:justify-between">
          <h2 className="h2 text-center">Featured Artists</h2>
          <Button className="hidden !h-[51px] !w-[119px] sm:block">
            View All
          </Button>
        </div>

        <div className="relative mt-5 sm:mt-12">
          {!mounted ? (
            // SKELETON LOADER
            <div className="hideScrollbar flex gap-3 overflow-auto px-2">
              {[...Array(7)].map(() => (
                <>
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                  <CardSkeleton />
                </>
              ))}
            </div>
          ) : (
            // SWIPER SLIDER
            <Swiper
              modules={[Pagination, Autoplay]}
              style={{ position: 'unset' }}
              className="AtSocialSlider1 h-full"
              pagination={{
                clickable: true,
                type: 'bullets',
                renderBullet(index: any, className: string) {
                  return '<span class="' + className + '"></span>';
                },
              }}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              }}
              spaceBetween={0}
              slidesPerView={3.8}
              centeredSlides={true}
              loop={true}
              direction="horizontal"
              breakpoints={{
                320: {
                  slidesPerView: 1,
                  spaceBetween: 10,
                },
                520: {
                  slidesPerView: 1.5,
                  spaceBetween: 10,
                },
                768: {
                  slidesPerView: 3,
                  spaceBetween: 20,
                },
                1024: {
                  slidesPerView: 4.5,
                  spaceBetween: 20,
                },
              }}
            >
              {data.map((item, index) => (
                <SwiperSlide key={index}>
                  <SliderCard
                    imageSrc={item.src}
                    name={item.name}
                    followers={item.followers}
                    pricePerToken={item.price}
                    itemCount={item.count}
                  />
                </SwiperSlide>
              ))}
            </Swiper>
          )}

          <div className="w-full">
            <Button className="block !h-[51px] !w-full sm:hidden">
              View All
            </Button>
          </div>
        </div>
      </Container>
    </div>
  );
}

export default Featured;
