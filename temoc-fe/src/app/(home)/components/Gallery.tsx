import React, { forwardRef } from 'react';
import { Container } from '@/components/common';
import Image from 'next/image';

const Gallery = forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>(
  (props, ref) => {
    return (
      <div className="relative z-20" id="about" ref={ref} {...props}>
        <Container className="relative">
          <div
            className="absolute -left-8 -top-48 -z-50 flex w-full justify-start"
            data-aos="fade-up"
          >
            <h2 className="hidden font-DreamAvenue !text-[277.8px] text-[#292D32] opacity-[6%] md:block">
              03
            </h2>
          </div>
          <div className="relative m-auto max-w-[1279px]" data-aos="fade-up">
            <h2 className="h2 text-center">
              Explore the next generation of music and art on Temoc.
            </h2>
            <p className="mt-6 text-center capitalize" data-aos="fade-up">
              Stay ahead of the curve with exclusive access to up-and-coming
              independent artists. TEMOC highlights fresh talent, providing fans
              with an opportunity to support and engage with artists before they
              reach mainstream success.
            </p>
          </div>
          <div className="relative">
            <div className="relative mt-10 hidden h-[330px] sm:block md:h-[420px] lg:h-[544px]">
              {/* {images.map((image, index) => ( */}
              <Image
                // key={index}
                src="/assets/images/home/<USER>"
                alt={'Explore the next generation of music on Temoc.'}
                width={1259}
                height={544}
                className={`object-cover`}
                // className={`tooltip-image  absolute object-cover top-0  ${
                //   currentImageIndex === index ? "show" : ""
                // }`}
              />
              {/* ))} */}
            </div>
            <div className="relative mt-10 flex h-[440px] items-center justify-center sm:hidden xs1:h-[420px]">
              {/* {mobileimage.map((image, index) => ( */}
              <Image
                // key={index}
                src="/assets/images/gallery/mblgallery1.png"
                width={350}
                height={419}
                alt={'Explore the next generation of music on Temoc'}
                className={`object-cover`}
                // className={`tooltip-image absolute object-cover top-0  ${
                //   currentImageIndex === index ? "show" : ""
                // }`}
              />
              {/* ))} */}
            </div>
          </div>
        </Container>
      </div>
    );
  },
);
Gallery.displayName = 'Vision';

export default Gallery;
