'use client';
import Button from '@/components/common/Button';
import Container from '@/components/common/Container';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import React from 'react';

const Ready = () => {
  const { setShowAuthFlow } = useDynamicContext();
  return (
    <>
      <Container className="relative pb-10 pt-10 sm:pb-[69px] sm:pt-[92px]">
        <div className="relative text-center">
          <img
            src="/assets/images/home/<USER>"
            alt="Be Part of the Movement Early"
            className="mx-auto hidden xl:block"
          />

          <div className="relative top-0 flex h-full w-full flex-col items-center justify-between xl:absolute">
            <div className="mb-5 mt-0 flex flex-col items-center gap-3 xl:gap-6">
              <h2
                className="h2 m-auto max-w-[637px] text-center text-[60px] sm:text-[80px]"
                data-aos="fade-up"
              >
                Ready to Launch Your Token?
              </h2>
              <p
                className="m-auto text-center text-xs capitalize text-[#292D32] sm:max-w-[580px] sm:text-lg xs:w-full"
                data-aos="fade-up"
              >
                Join thousands of creators monetizing their content and building
                their community with TEMOC.
              </p>
              <div
                className="flex justify-center gap-5 xs:flex-col xs:gap-3.5"
                data-aos="fade-up"
              >
                <Button
                  onClick={() => setShowAuthFlow(true)}
                  className="!h-[53px] !w-[231px] !text-lg !font-bold xs:!w-[247px]"
                >
                  APPLY AS AN ARTIST
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </>
  );
};

export default Ready;
