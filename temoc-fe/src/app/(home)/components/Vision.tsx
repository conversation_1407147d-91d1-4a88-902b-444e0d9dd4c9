'use client';
import { Container } from '@/components/common';
import Artist from '@/components/common/Icons/Artist';
import Fans from '@/components/common/Icons/Fans';
import Unlock from '@/components/common/Icons/Unlock';
import ImageComponent from '@/components/common/ImageComponent';
import React, { forwardRef } from 'react';

const indexClasses: Record<number, string> = {
  1: 'sm:right-5 right-2.5',
  2: 'sm:right-7  right-2.5',
  3: 'sm:right-10  right-2.5',
};

const Vision = forwardRef<HTMLDivElement, React.HTMLProps<HTMLDivElement>>(
  (props, ref) => {
    const data = [
      {
        icon: <Artist />,
        quarter: 'Artists Launch Tokens',
        description:
          ' Creators deploy their own tokens on popular blockchains with just a few clicks.',
        number: '01',
      },
      {
        icon: <Fans />,
        quarter: 'Fans Acquire Tokens',
        description:
          'Supporters purchase tokens with credit card or crypto to access exclusive content.',
        number: '02',
      },
      {
        icon: <Unlock />,
        quarter: 'Unlock Experiences',
        description:
          'Token holders access gated content, receive rewards, and interact directly with creators.',
        number: '03',
      },
    ];

    return (
      <div id="discover" ref={ref} {...props}>
        <Container className="relative py-[40px] sm:py-[80px] lg:py-[125px]">
          <div
            className="absolute top-0 flex w-full justify-end"
            data-aos="fade-up"
          >
            <h2 className="hidden font-DreamAvenue !text-[277.8px] text-[#292D32] opacity-[6%] md:block">
              01
            </h2>
          </div>
          <h2
            className="h2 text-center"
            data-aos="fade-up"
            id="roadmap"
            ref={ref}
            {...props}
          >
            How TEMOC Works
          </h2>
          <p
            className="mt-2 text-center text-lg sm:!text-xl sm:!font-medium"
            data-aos="fade-up"
          >
            Understand the Power Behind the Platform and How TEMOC Fuels the
            Ecosystem.
          </p>

          <div className="mt-10 flex flex-col gap-5 sm:mt-14 lg:flex-row">
            <div className="block lg:hidden">
              <ImageComponent
                src="/assets/images/home/<USER>"
                height={503}
                width={466}
                className="h-[500px] w-full object-cover"
                alt="Temoc’s Vision"
              />
            </div>
            <div className="w-full space-y-5 sm:space-y-2.5">
              {data.map((item, index) => {
                return (
                  <div
                    className="group relative space-y-3 overflow-hidden bg-[#F6F6F6] px-5 py-[30px] pr-24 duration-300 sm:space-y-5 sm:px-7 md:py-[59.5px]"
                    key={index}
                  >
                    {item.icon}
                    <h2 className="font-DreamAvenue !text-2xl font-normal duration-500 sm:!text-[40px] group-hover:sm:!text-[46px]">
                      {item.quarter}
                    </h2>

                    <p className="relative z-20 w-full max-w-[365px] !font-normal text-[#212428] sm:!text-xs">
                      {item.description}
                    </p>

                    <p
                      className={` ${indexClasses[index] || 'right-2.5'} } absolute -right-[7%] font-DreamAvenue text-[120px] font-normal text-[#FF760E] duration-500 sm:right-2.5 sm:top-[76%] sm:text-[140px] sm:text-[160px] md:text-[200px] group-hover:lg:-translate-y-[300%]`}
                    >
                      {item.number}
                    </p>
                  </div>
                );
              })}
            </div>
            <div className="hidden lg:block">
              <ImageComponent
                src="/assets/images/home/<USER>"
                // height={1003}
                // width={466}
                figClassName="h-full w-[466px]"
                fill
                className=""
                alt="Temoc’s Vision"
              />
            </div>
          </div>
        </Container>
      </div>
    );
  },
);
Vision.displayName = 'Vision';

export default Vision;
