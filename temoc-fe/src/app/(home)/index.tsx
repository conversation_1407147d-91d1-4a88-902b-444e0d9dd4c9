'use client';
import Header from '@/components/ui/Header';
import Gallery from './components/Gallery';
import { useEffect, useRef, useState } from 'react';
import Banner from './components/Banner';
import Vision from './components/Vision';
import Ready from './components/Ready';
import Footer from '@/components/ui/Footer';
import Featured from './components/Featured';
// import Explore from './components/Explore';

const Home = () => {
  const [ActiveTab, setActiveTab] = useState(0);
  const mainRef = useRef(null);
  const discoverRef = useRef(null);
  const aboutRef = useRef(null);
  const Navigation = [
    {
      name: 'Home',
      href: `/#main`,
      id: 'main',
      ref: mainRef,
    },
    {
      name: 'Discover',
      href: `/#discover`,

      id: 'discover',
      ref: discoverRef,
    },
    {
      name: 'About',
      href: `/#about`,
      id: 'about',
      ref: aboutRef,
    },
  ];

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5, // Adjust this value as needed
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = Navigation.findIndex(
            (nav) => nav.ref.current === entry.target,
          );
          if (index !== -1) {
            setActiveTab(index);
          }
        }
      });
    }, options);

    // Observe each section
    Navigation.forEach((nav) => {
      if (nav.ref.current) {
        observer.observe(nav.ref.current);
      }
    });

    return () => {
      // Cleanup the observer on unmount
      observer.disconnect();
    };
  }, []);

  return (
    <>
      <meta name="robots" content="index" />
      <meta
        name="title"
        content="Own a Slice of Creativity Invest in Artists Tokens | TEMOC"
      />
      <title>Own a Slice of Creativity Invest in Artists Tokens | TEMOC</title>
      <meta
        name="description"
        content="Imagine owning a piece of your favorite artist's rise. TEMOC makes it real. Buy artist crytpo tokens to gain unparalleled access to behind-the-scenes content, private events, and help shape their journey. Redefining patronage for the digital age."
      />
      <meta
        name="keywords"
        content="artist tokenization, support independent artists, exclusive fan content, blockchain music platform, creator tokens, invest in artists, fan engagement, TEMOC token, music tokenization, digital art tokens, web3 artists, artist funding, Crypto music Tokens, Crypto Tokens"
      />
      <link rel="canonical" href="https://www.temoc.io/" />
      <meta property="og:locale" content="en_US" />
      <meta
        property="og:title"
        content="Own a Slice of Creativity Invest in Artists Tokens | TEMOC"
      />
      <meta
        property="og:description"
        content="Imagine owning a piece of your favorite artist's rise. TEMOC makes it real. Buy artist crytpo tokens to gain unparalleled access to behind-the-scenes content, private events, and help shape their journey. Redefining patronage for the digital age."
      />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://www.temoc.io/" />
      <meta
        property="og:image"
        content="https://www.temoc.io/assets/images/link-preview.png"
      />
      <meta name="twitter:url" content="https://www.temoc.io/" />
      <meta
        name="twitter:title"
        content="Own a Slice of Creativity Invest in Artists Tokens | TEMOC"
      />
      <meta
        name="twitter:description"
        content="Imagine owning a piece of your favorite artist's rise. TEMOC makes it real. Buy artist crytpo tokens to gain unparalleled access to behind-the-scenes content, private events, and help shape their journey. Redefining patronage for the digital age. "
      />
      <meta
        name="twitter:image"
        content="https://www.temoc.io/assets/images/link-preview.png"
      />
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:image:alt" content="TEMOC" />
      <Header activeTab={ActiveTab} setActiveTab={setActiveTab} />
      <Banner ref={mainRef} />
      <Vision ref={discoverRef} />
      <Featured />
      {/* <Explore /> */}
      <div className="pb-10 pt-10 sm:pb-32 sm:pt-20">
        <Gallery ref={aboutRef} />
      </div>

      <Ready />
      <Footer />
    </>
  );
};

export default Home;
