import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic'; // Ensure this route is always dynamically served

export async function GET(req: NextRequest) {
  try {
    // Extract the symbol from the query parameters
    const { searchParams } = new URL(req.url);
    const symbol = searchParams.get('symbol') || 'RFC';

    // Mock data for RFC token (uncomment if needed)
    // if (symbol === "RFC") {
    //     const data = {
    //         data: {
    //             [symbol]: {
    //                 quote: {
    //                     USD: {
    //                         price: 0.01646,
    //                     },
    //                 },
    //             },
    //         },
    //     };
    //     return NextResponse.json(data, { status: 200 });
    // }

    // Fetch data from CoinMarketCap API
    const response = await axios.get(
      'https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest',
      {
        headers: {
          'X-CMC_PRO_API_KEY': process.env.COINMARKETCAP_API_KEY || '',
        },
        params: { symbol },
      },
    );

    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching cryptocurrency data:', error.message);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error.message },
      { status: 500 },
    );
  }
}
