import React, { useEffect, useRef } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Input } from '@/components/common/Forms/Input';
import { userService } from '@/services/user.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FaRegEdit } from 'react-icons/fa';
import InputError from '@/components/common/Forms/InputError';
import { ProfileSchema } from '@/utils/schema';
import { UserRes } from '@/types/user.interface';
import { Textarea } from '@/components/common/Forms/TextArea';
import { toast } from 'react-toastify';
import { Button } from '@/components/common';
import { AboutSkeleton } from '@/components/ui/Skelton/AboutSkelton';
import SocialMedia from '@/components/ui/UpdateSocialMedia/SocialMedia';
// import SocialMedia fro../../../components/ui/UpdateSocialMedia/SocialMediadia';

// Define your form data type
type FormData = {
  firstName: string;
  lastName: string;
  username: string;
  // walletAddress: string;
  email: string;
  description?: string;
};

const About = () => {
  const { data, isLoading } = useQuery<UserRes>({
    queryKey: ['profile-fan'],
    queryFn: () => userService?.getUsersProfile(),
  });

  const AboutData = data?.data;
  const queryClient = useQueryClient();

  // Initialize react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    resolver: yupResolver(ProfileSchema),
    mode: 'all',
  });

  // Store initial values for comparison
  const initialValuesRef = useRef<FormData | null>(null);

  // console.log(AboutData?.artistProfile?.bio, 'AboutData.artistProfile.bio');

  useEffect(() => {
    if (AboutData) {
      const initialValues = {
        firstName: AboutData.firstName || '',
        lastName: AboutData.lastName || '',
        username: AboutData.username || '',
        email: AboutData.email || '',
        description:
          AboutData?.role == 'artist'
            ? AboutData?.artistProfile.bio || ''
            : AboutData?.description || '',
      };
      initialValuesRef.current = initialValues;

      setValue('firstName', initialValues.firstName);
      setValue('lastName', initialValues.lastName);
      setValue('username', initialValues.username);
      setValue('email', initialValues.email);
      setValue('description', initialValues.description);
    }
  }, [AboutData]);

  // Watch current form values
  const watchedValues = useWatch({ control });

  // Compare initial and current values
  const isFormChanged = () => {
    if (!initialValuesRef.current) return false;
    const initial = initialValuesRef.current;
    return (
      initial.firstName !== watchedValues.firstName ||
      initial.lastName !== watchedValues.lastName ||
      initial.username !== watchedValues.username ||
      initial.email !== watchedValues.email ||
      initial.description !== watchedValues.description
    );
  };

  const disableSaveButton = !isFormChanged();

  const updateMutation = useMutation({
    mutationFn: userService.updateUserProfile,
    onSuccess: () => {
      toast.success('Updated successfuly!');
      queryClient.invalidateQueries({ queryKey: ['profile-fan'] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  // Submit handler
  const onSubmit = async (formData: FormData) => {
    try {
      await updateMutation.mutateAsync(formData);
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  return (
    <div>
      <h3 className="text-center text-xl font-normal sm:text-left sm:text-[22px]">
        ABOUT
      </h3>
      {isLoading ? (
        <AboutSkeleton />
      ) : (
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="mx-auto mt-7 w-full max-w-[1040px] rounded-[20px] bg-white p-5 shadow-[0_4px_10px_rgba(0,0,0,0.08)] sm:p-10"
          noValidate
        >
          {AboutData?.role == 'artist' ? (
            <div className="">
              <div className="grid grid-cols-1 gap-4 border-[#CECECE] pb-4 sm:grid-cols-2 sm:gap-12 sm:border-b sm:pb-5">
                <div className="relative">
                  <p className="font-display text-base text-[#333333]">
                    First Name
                  </p>
                  <p className="mt-1 font-display text-sm text-[#666666]">
                    {AboutData?.firstName}
                  </p>
                  <div className="absolute -right-4 top-1/2 hidden h-9 w-[2px] -translate-y-1/2 bg-[#CECECE] sm:block"></div>
                </div>
                <div className="">
                  <p className="font-display text-base text-[#333333]">
                    Last Name
                  </p>
                  <p className="mt-1 font-display text-sm text-[#666666]">
                    {AboutData?.lastName}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4 border-[#CECECE] pb-4 sm:my-5 sm:grid-cols-2 sm:gap-12 sm:border-b sm:pb-5">
                <div className="relative">
                  <p className="font-display text-base text-[#333333]">
                    Username
                  </p>
                  <p className="mt-1 font-display text-sm text-[#666666]">
                    {AboutData?.username}
                  </p>
                  <div className="absolute -right-4 top-1/2 hidden h-9 w-[2px] -translate-y-1/2 bg-[#CECECE] sm:block"></div>
                </div>
                <div className="">
                  <p className="font-display text-base text-[#333333]">Email</p>
                  <p className="mt-1 font-display text-sm text-[#666666]">
                    {AboutData?.email}
                  </p>
                </div>
              </div>
              {(AboutData?.artistProfile.bio || AboutData?.description) && (
                <div className="">
                  <p className="font-display text-base text-[#333333]">
                    Description
                  </p>
                  <p className="mt-1 font-display text-sm text-[#666666]">
                    {AboutData?.role == 'artist'
                      ? AboutData?.artistProfile.bio
                      : AboutData?.description}
                  </p>
                </div>
              )}
              <SocialMedia />
            </div>
          ) : (
            <div className="">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-12">
                <div className="relative flex flex-col">
                  <div className="flex items-center justify-between px-1">
                    <p className="font-bold">First Name</p>
                    <label htmlFor="F_Name">
                      {' '}
                      <FaRegEdit className="cursor-pointer text-base" />
                    </label>
                  </div>
                  <Input
                    placeholder="First Name"
                    name="firstName"
                    id="F_Name"
                    register={register}
                    className="mt-1 w-full !rounded-md !pl-3 !font-normal"
                  />
                  <InputError error={errors.firstName?.message} />
                </div>

                <div className="flex flex-col">
                  <div className="flex w-full items-center justify-between">
                    <p className="font-bold">Last Name</p>
                    <label htmlFor="L_Name">
                      <FaRegEdit className="mt-1 block cursor-pointer text-base" />
                    </label>
                  </div>
                  <Input
                    placeholder="Username"
                    name="lastName"
                    id="L_Name"
                    register={register}
                    className="mt-1 w-full !rounded-md !pl-3 !font-normal"
                  />

                  <InputError error={errors.lastName?.message} />
                </div>
              </div>
              <div className="my-4 grid grid-cols-1 gap-4 sm:my-5 sm:gap-12 md:grid-cols-2">
                <div className="relative flex flex-col">
                  <div className="flex items-center justify-between px-2">
                    <p className="font-bold">Username</p>
                    <label htmlFor="User_Name">
                      <FaRegEdit className="mt-1 block cursor-pointer text-base" />
                    </label>
                  </div>
                  <Input
                    placeholder="Username"
                    name="username"
                    id="User_Name"
                    register={register}
                    className="mt-1 w-full !rounded-md !pl-3 !font-normal"
                  />
                  <InputError error={errors.username?.message} />
                </div>
                <div className="flex flex-col">
                  <div className="flex w-full items-center justify-between px-1">
                    <p className="font-bold">Email</p>
                  </div>
                  <Input
                    placeholder="Email"
                    name="email"
                    readOnly
                    register={register}
                    className="mt-1 w-full !rounded-md !pl-3 !font-normal focus:!border-[#CECECE]"
                  />
                  <InputError error={errors.email?.message} />
                </div>
              </div>
              <p className="mb-1 font-bold">Description</p>
              <Textarea
                placeholder="Description"
                name="description"
                register={register}
                className="!rounded-md !p-3 !font-normal"
              />

              <div className="mt-5 flex justify-center sm:mt-8 sm:justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting || disableSaveButton}
                  className=""
                  // className="mt-8 rounded bg-blue-600 px-6 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </div>
          )}
        </form>
      )}

      <p className="mt-12 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </div>
  );
};

export default About;
