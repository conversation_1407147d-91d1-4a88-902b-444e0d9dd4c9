import { But<PERSON> } from '@/components/common';
import Card from '@/components/ui/Card/Card';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { discoveryApi, RecommendedArtist } from '@/services/discovery';
import Link from 'next/link';

interface StaticArtistData {
  src: string;
  name: string;
  followers: string;
}

const staticData: StaticArtistData[] = [
  {
    src: '/assets/images/artist/popular1.svg',
    name: '<PERSON>',
    followers: '150.91 million',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    followers: '126.15 million',
  },
  {
    src: '/assets/images/artist/popular3.svg',
    name: '<PERSON>',
    followers: '124.24 million',
  },
  {
    src: '/assets/images/artist/popular4.svg',
    name: '<PERSON>',
    followers: '107.89 million',
  },
  {
    src: '/assets/images/artist/popular5.svg',
    name: '<PERSON><PERSON>',
    followers: '103.63 million',
  },
  {
    src: '/assets/images/artist/popular3.svg',
    name: '<PERSON> <PERSON>',
    followers: '124.24 million',
  },
  {
    src: '/assets/images/artist/popular4.svg',
    name: '<PERSON> Eilish',
    followers: '107.89 million',
  },
  {
    src: '/assets/images/artist/popular5.svg',
    name: 'Kendrick Lamar',
    followers: '103.63 million',
  },
  {
    src: '/assets/images/artist/popular1.svg',
    name: 'Bruno Mars',
    followers: '150.91 million',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    followers: '126.15 million',
  },
  {
    src: '/assets/images/artist/popular4.svg',
    name: 'Billie Eilish',
    followers: '107.89 million',
  },
  {
    src: '/assets/images/artist/popular3.svg',
    name: 'Lady Gaga',
    followers: '124.24 million',
  },
];

const OtherArtist = () => {
  // Fetch recommended artists from API
  const { data: recommendedArtists, isLoading } = useQuery({
    queryKey: ['discovery', 'recommended'],
    queryFn: () => discoveryApi.getRecommendedArtists(12),
  });

  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold sm:text-xl">Other Artists</h3>
        <Link href="/app/discovery">
          <Button className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]">
            <u> View All</u> <FaLongArrowAltRight className="text-black" />
          </Button>
        </Link>
      </div>
      <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-6 xl:grid-cols-6">
        {isLoading
          ? // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="h-48 w-full rounded-lg bg-gray-200"></div>
              </div>
            ))
          : recommendedArtists
            ? // Render API data
              recommendedArtists.map(
                (artist: RecommendedArtist, index: number) => {
                  const artistData = {
                    src:
                      artist.artistProfile?.profilePic ||
                      artist.avatarUrl ||
                      '/assets/images/artist/popular1.svg',
                    name: artist.displayName || artist.username,
                    followers: `${artist.mutualConnections || 0} Mutual Connections`,
                    username: artist.username,
                  };

                  return (
                    <Link
                      href={`/app/discovery/${artistData.username}`}
                      key={index}
                    >
                      <Card
                        imageSrc={artistData.src}
                        name={artistData.name}
                        followers={artistData.followers}
                      />
                    </Link>
                  );
                },
              )
            : // Render static fallback data
              staticData
                .slice(0, 6)
                .map((item: StaticArtistData, index: number) => (
                  <Card
                    key={index}
                    imageSrc={item.src}
                    name={item.name}
                    followers={item.followers}
                  />
                ))}
      </div>
    </div>
  );
};

export default OtherArtist;
