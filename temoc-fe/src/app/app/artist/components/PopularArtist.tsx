'use client';
import { <PERSON><PERSON> } from '@/components/common';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';
import Link from 'next/link';
import SliderCard from '@/components/ui/SliderCard/SliderCard';
import CommonSlider from '@/components/common/CommonSlider';
import { useQuery } from '@tanstack/react-query';
import {
  discoveryApi,
  type PopularArtist as PopularArtistType,
} from '@/services/discovery';

interface StaticArtistData {
  src: string;
  name: string;
  followers: string;
  price: string;
  count: string;
}

const staticData: StaticArtistData[] = [
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '36 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '15 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '13 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: '<PERSON>',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '27 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'R Kelly',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '19 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Amelia',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '42 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Emily Rivera',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '36 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Lil Wayne',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '15 Songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Bruno Mars',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '13 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Emily Rivera',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '27 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'R Kelly',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '19 songs',
  },
  {
    src: '/assets/images/home/<USER>',
    name: 'Amelia',
    followers: '10K Followers',
    price: '$2.50 per token',
    count: '42 songs',
  },
];

const PopularArtist = () => {
  // Fetch popular artists from API
  const { data: popularArtists, isLoading } = useQuery({
    queryKey: ['discovery', 'popular'],
    queryFn: () => discoveryApi.getPopularArtists(12),
  });

  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold sm:text-xl">POPULAR ARTISTS</h3>
        <Link href="/app/discovery">
          <Button className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]">
            <u> View All</u> <FaLongArrowAltRight className="text-black" />
          </Button>
        </Link>
      </div>
      <div className="mt-2">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
          </div>
        ) : (
          <CommonSlider>
            {popularArtists
              ? // Render API data
                popularArtists.map(
                  (artist: PopularArtistType, index: number) => {
                    const artistData = {
                      src:
                        artist.artistProfile?.profilePic ||
                        artist.avatarUrl ||
                        '/assets/images/placeholder.avif',
                      name: artist.displayName || artist.username,
                      followers: `${artist.followersCount || 0} Followers`,
                      price: '$2.50 per token', // This would come from token data in real implementation
                      count: '0 Songs', // This would come from tracks data in real implementation
                      username: artist.username,
                    };

                    return (
                      <div className="mb-3" key={index}>
                        <Link href={`/app/discovery/${artistData.username}`}>
                          <SliderCard
                            imageSrc={artistData.src}
                            name={artistData.name}
                            followers={artistData.followers}
                            pricePerToken={artistData.price}
                            itemCount={artistData.count}
                          />
                        </Link>
                      </div>
                    );
                  },
                )
              : // Render static fallback data
                staticData.map((item: StaticArtistData, index: number) => (
                  <div className="mb-3" key={index}>
                    <Link href="/app/trading">
                      <SliderCard
                        imageSrc={item.src}
                        name={item.name}
                        followers={item.followers}
                        pricePerToken={item.price}
                        itemCount={item.count}
                      />
                    </Link>
                  </div>
                ))}
          </CommonSlider>
        )}
      </div>
    </div>
  );
};

export default PopularArtist;
