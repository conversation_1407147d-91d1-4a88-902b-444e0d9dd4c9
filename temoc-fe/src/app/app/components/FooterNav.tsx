'use client';
// import { Gears, Home, Market, ShowDown, Teams } from '@/components/common/Icons';
import Link from 'next/link';
import React from 'react';
import { usePathname } from 'next/navigation';
import Explore from '@/components/common/Icons/Explore';
import PManagement from '@/components/common/Icons/PManagement';
import CreateProject from '@/components/common/Icons/CreateProject';
// import PublishEvents from '@/components/common/Icons/PublishEvents';

const FooterCards = [
  {
    icon: <Explore className="h-[24px] w-[24px]" />,
    dec: 'Explore',
    href: '/app/explore',
  },

  {
    icon: <PManagement className="h-[24px] w-[24px]" />,
    dec: 'Library',
    href: '/app/library',
  },
  {
    icon: <Explore className="h-[24px] w-[24px]" />,
    dec: 'Discovery',
    href: '/app/discovery',
  },
  {
    dec: 'Profile',
    href: '/app/profile',
    icon: <CreateProject className="h-[24px] w-[24px]" />,
  },
  // {
  //   dec: 'Trading',
  //   href: '/app/trading',
  //   icon: <PublishEvents className="h-[24px] w-[24px]" />,
  // },
];

const FooterNav = () => {
  const pathname = usePathname();

  return (
    <div className="sticky bottom-0 z-[200] block w-full md:hidden">
      <div className="flex justify-between rounded-t-[16px] bg-white px-7 py-4 shadow">
        {FooterCards.map((item, index) => {
          const isSelected = pathname === item.href;
          const isLastItem = index === FooterCards.length - 1;

          return (
            <React.Fragment key={item.href}>
              <Link href={item.href} className="flex items-center gap-6">
                <div
                  className={`flex cursor-pointer flex-col items-center justify-center gap-2 ${isSelected ? '' : ''}`}
                >
                  <div
                    className={` ${isSelected ? '!text-primary' : '!text-[#999999]'} flex h-[26px] w-[26px] items-center justify-center`}
                  >
                    {item.icon}
                  </div>
                  <p
                    className={`text-sm font-normal ${isSelected ? '!text-primary' : '!text-[#333333]'}`}
                  >
                    {item.dec}
                  </p>
                </div>
                {!isLastItem && (
                  <div className="h-[20px] w-[1.5px] bg-[#E3E3E3]"></div>
                )}
              </Link>
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default FooterNav;
