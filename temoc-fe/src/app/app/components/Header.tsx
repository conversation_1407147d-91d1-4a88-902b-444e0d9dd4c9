import React, { useEffect } from 'react';
import { Bars3Icon } from '@heroicons/react/24/outline';
import ProfileDropdown from '@/components/ui/ProfileDropdown';
import Button from '@/components/common/Button';
import ImageComponent from '@/components/common/ImageComponent';
import Link from 'next/link';
// import Search from '@/components/common/Forms/Search';
import { MdArrowOutward } from 'react-icons/md';
import { userService } from '@/services/user.service';
import { toast } from 'react-toastify';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { CurrentViewRes } from '@/types/user.interface';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

const Header = ({ setSidebarOpen, artist }: any) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  // const artistMutation = useMutation({
  //   mutationFn: userService.becomeArtist,
  //   onSuccess: () => {
  //     router.push('/artist/initial-dashboard');
  //     queryClient.invalidateQueries({ queryKey: ['auth-user'] });
  //   },
  //   onError: (error: any) => {
  //     toast.error(error.response.data.message);
  //   },
  // });

  const { data: currentData, isLoading: currentLoading } =
    useQuery<CurrentViewRes>({
      queryKey: ['current-view'],
      queryFn: () => userService?.getCurrentView(),
      enabled: !!user, // Only fetch when user data is available
      staleTime: 1 * 1000, // 1 second - very fast updates for immediate header response
      refetchOnMount: true, // Always refetch on mount for fresh data
      refetchOnWindowFocus: false,
    });
  const currentViewData = currentData?.data;
  const SwitchViewMode = currentViewData?.viewMode;

  // Listen for auth success event and force refetch for immediate UI update
  useEffect(() => {
    const handleAuthSuccess = () => {
      console.log('Auth success event received, forcing query refetch...');
      // Longer delay to ensure token is fully set and available
      setTimeout(() => {
        console.log('Starting forced refetch of user data...');
        // Force refetch to immediately update header UI
        queryClient.invalidateQueries({ queryKey: ['auth-user'] });
        queryClient.refetchQueries({ queryKey: ['auth-user'] });

        // Wait a bit more for user data, then fetch current view
        setTimeout(() => {
          console.log('Starting forced refetch of current view...');
          queryClient.invalidateQueries({ queryKey: ['current-view'] });
          queryClient.refetchQueries({ queryKey: ['current-view'] });
        }, 300);
      }, 500); // 500ms delay to ensure token is properly set
    };

    window.addEventListener('authSuccess', handleAuthSuccess);
    return () => window.removeEventListener('authSuccess', handleAuthSuccess);
  }, [queryClient]);

  // Watch for user becoming available and refetch current view
  useEffect(() => {
    if (user && !currentData) {
      console.log('User became available, refetching current view...');
      queryClient.refetchQueries({ queryKey: ['current-view'] });
    }
  }, [user, currentData, queryClient]);

  // Remove automatic redirect logic to prevent conflicts with manual switching
  // The ExploreHandle component handles initial navigation based on viewMode

  const switchMutation = useMutation({
    mutationFn: userService?.switchView,
    onSuccess: (response) => {
      console.log('Switch view response:', response);
      // Backend returns data directly, not nested in data.data
      const newViewMode = response?.data?.viewMode;
      console.log('New view mode:', newViewMode);

      // Fast invalidation for immediate UI updates
      queryClient.invalidateQueries({ queryKey: ['current-view'] });
      queryClient.invalidateQueries({ queryKey: ['auth-user'] });

      // Small delay to ensure queries are invalidated before navigation
      setTimeout(() => {
        // Fast navigation based on new view mode with user feedback
        if (newViewMode === 'artist') {
          console.log('Switching to artist view - redirecting to dashboard');
          toast.success('Switched to Artist');
          router.replace('/artist/initial-dashboard?tab=Home');
        } else if (newViewMode === 'fan') {
          console.log('Switching to fan view - redirecting to explore');
          toast.success('Switched to Fan');
          router.replace('/app/explore');
        }
      }, 100); // 100ms delay
    },
    onError: (error: any) => {
      console.error('Switch view error:', error);
      toast.error(error.response?.data?.message || 'Failed to switch view');
    },
  });

  const handleSwitch = async () => {
    // Prevent multiple clicks while switching
    if (switchMutation.isPending) {
      console.log('Switch already in progress, ignoring click');
      return;
    }

    try {
      console.log('Starting view switch...');
      await switchMutation.mutateAsync();
    } catch (error) {
      console.error('Switch view error in handleSwitch:', error);
    }
  };

  return (
    <div className="sticky top-0 z-50">
      <div className="relative top-0 flex min-h-[4.5rem] w-full shrink-0 items-center gap-x-4 bg-white px-4 py-4 sm:gap-x-6 sm:px-6 lg:px-8 xs:py-2">
        <button
          onClick={() => setSidebarOpen(true)}
          className={`-m-2.5 block p-2.5 text-white lg:hidden xs:-m-0 xs:p-0 ${artist && '!hidden'}`}
        >
          <span className="sr-only">Open sidebar</span>
          <Bars3Icon aria-hidden="true" className="h-6 w-6 text-black" />
        </button>

        <div
          aria-hidden="true"
          className={`${artist && '!hidden'} block h-6 w-px bg-gray-900/10 lg:hidden`}
        />

        <div className="relative z-50 flex w-full items-center justify-between gap-x-4 self-stretch lg:flex-1 lg:gap-x-6 xs:gap-x-4">
          <Link
            href="/"
            className="relative z-20 flex h-[4.5rem] shrink-0 items-center bg-transparent py-4 pl-4 xs:pl-0"
          >
            <ImageComponent
              src="/assets/images/temoc-logo.svg"
              figClassName="h-[40px] w-[101px] hidden lg:block"
              fill
              alt="logo"
              priority
              className="cursor-pointer object-contain"
            />
            <img
              src="https://www.temoc.io/assets/images/logo.svg "
              alt="logo"
              className="block lg:hidden"
            />
          </Link>
          <div className="mt-2 flex items-center gap-3 md:hidden">
            <div>
              {user ? (
                user?.role === 'fan' ? (
                  <Link href="/kyc">
                    <Button className="group !h-[40px] !text-xs font-semibold uppercase">
                      Become An Artist
                      <MdArrowOutward className="text-sm text-primary" />
                    </Button>
                  </Link>
                ) : user?.role === 'artist' && SwitchViewMode ? (
                  <Button
                    onClick={handleSwitch}
                    isLoading={currentLoading || switchMutation?.isPending}
                    className="group !h-[40px] w-[160px] !text-xs font-semibold uppercase"
                  >
                    {SwitchViewMode === 'fan'
                      ? 'Switch To Artist'
                      : 'Switch To Fan'}
                    <MdArrowOutward className="text-sm text-primary" />
                  </Button>
                ) : user?.role === 'artist' && currentLoading ? (
                  <Button
                    isLoading={true}
                    className="group !h-[40px] w-[160px] !text-xs font-semibold uppercase"
                  >
                    Loading...
                  </Button>
                ) : null
              ) : (
                // Show loading state when user is not yet loaded
                <Button
                  isLoading={true}
                  className="group !h-[40px] w-[160px] !text-xs font-semibold uppercase"
                >
                  Loading...
                </Button>
              )}
            </div>
            {user && <ProfileDropdown />}
          </div>
          {/* <div className="hidden md:block lg:w-[22%]">
            <Search />
          </div> */}
          <div className="hidden gap-3 md:flex">
            {user ? (
              user?.role === 'fan' ? (
                <Link href="/kyc">
                  <Button className="group !h-[40px] !text-xs font-semibold uppercase lg:!w-[160px]">
                    Become An Artist
                    <MdArrowOutward
                      className="flex-shrink-0 text-sm !text-white"
                      size={18}
                    />
                  </Button>
                </Link>
              ) : user?.role === 'artist' && SwitchViewMode ? (
                <Button
                  onClick={handleSwitch}
                  isLoading={currentLoading || switchMutation?.isPending}
                  className="group !h-[40px] !w-[160px] text-center !text-xs font-semibold uppercase lg:!w-[160px]"
                >
                  {SwitchViewMode === 'fan'
                    ? 'Switch To Artist'
                    : 'Switch To Fan'}

                  <MdArrowOutward
                    className={`${(currentLoading || switchMutation?.isPending) && 'hidden'} flex-shrink-0 text-sm !text-white`}
                    size={18}
                  />
                </Button>
              ) : user?.role === 'artist' && currentLoading ? (
                <Button
                  isLoading={true}
                  className="group !h-[40px] !w-[160px] text-center !text-xs font-semibold uppercase lg:!w-[160px]"
                >
                  Loading...
                </Button>
              ) : null
            ) : (
              // Show loading state when user is not yet loaded
              <Button
                isLoading={true}
                className="group !h-[40px] !w-[160px] text-center !text-xs font-semibold uppercase lg:!w-[160px]"
              >
                Loading...
              </Button>
            )}
            {user && <ProfileDropdown />}
          </div>
        </div>
      </div>
      {/* <div className="mb-4 flex justify-center gap-2 px-2 md:hidden">
        <div className="w-full">
          <Search />
        </div>
      </div> */}
    </div>
  );
};

export default Header;
