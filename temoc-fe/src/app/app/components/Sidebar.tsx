import React from 'react';
import clsx from 'clsx';
import <PERSON> from 'next/link';
import { usePathname } from 'next/navigation';
import {
  // Dashboard,
  PManagement,
  CreateProject,
  PublishEvents,
  Explore,
  Help,
  // Artitest,
  // Managenemt,
} from '@/components/common/Icons';

const navigation = [
  // {
  //   name: 'Dashboard',
  //   href: '/app/initial-dashboard',
  //   icon: <Dashboard />,
  //   current: false,
  // },
  {
    name: 'Explore',
    href: '/app/explore',
    icon: <Explore />,
    current: true,
  },
  {
    name: 'Discover Artist',
    href: '/app/discovery',
    icon: <Explore />,
    current: false,
  },
  // { name: 'Artist', href: '/app/artist', icon: <Artitest />, current: true },
  {
    name: 'Library',
    href: '/app/library',
    icon: <PManagement />,
    current: false,
  },
  {
    name: 'Profile',
    href: '/app/profile',
    icon: <CreateProject />,
    current: false,
  },
  // {
  //   name: 'Management',
  //   href: '/app/management',
  //   icon: <Managenemt />,
  //   current: false,
  // },

  {
    name: 'Trading',
    href: '/app/trading',
    icon: <PublishEvents />,
    current: false,
  },

  {
    name: 'Transactions',
    href: '/app/transaction',
    icon: <PublishEvents />,
    current: false,
  },
  {
    name: 'Help',
    href: '/app/help',
    icon: <Help />,
    current: false,
  },
];

const footerData = [
  {
    name: 'Legal',
    href: '#',
    current: false,
  },
  { name: 'Terms & Conditions', href: '/terms-and-conditions', current: true },
  { name: 'Privacy Policy', href: '/privacy', current: true },
  { name: 'Cookies', href: '#', current: true },
  { name: 'About Ads', href: '#', current: true },
];

const Sidebar = () => {
  const pathname = usePathname();
  return (
    <div className="flex h-full flex-col justify-between">
      <div className="hideScrollbar relative flex h-screen grow flex-col overflow-y-auto bg-[#F2F2F2] lg:mt-24 lg:max-h-[600px] lg:rounded-[20px]">
        <nav className="flex flex-1 flex-col gap-5 p-4">
          <ul role="list" className="space-y-2.5">
            {navigation.map((item) => {
              const isSelected =
                pathname === item.href ||
                (item.href === '/app/initial-dashboard' &&
                  pathname?.includes('initial-dashboard')) ||
                (item.href === '/app/discovery' &&
                  pathname?.includes('discovery'));
              return (
                <li key={item.name}>
                  <Link href={item.href}>
                    <div
                      className={clsx(
                        item.name == 'Explore' ? '' : 'blu',
                        'group flex items-center justify-between gap-x-2.5 rounded-xl px-3 py-2 text-sm leading-6',
                      )}
                    >
                      <div
                        className={clsx(
                          isSelected ? 'text-primary' : 'text-[#999999]',
                          'flex items-center gap-x-2.5',
                        )}
                      >
                        <i
                          className={
                            'flex shrink-0 items-center justify-center'
                          }
                          aria-hidden="true"
                        >
                          {item.icon}
                        </i>
                        <span
                          className={
                            isSelected ? 'text-primary' : 'text-[#333333]'
                          }
                        >
                          {' '}
                          {item.name}
                        </span>
                      </div>
                    </div>
                  </Link>
                </li>
              );
            })}
          </ul>
          <div className="absolute bottom-3 w-full pr-8">
            <div className="w-full border-t border-[#999999] py-2">
              <Link href="/" className="flex items-center justify-center gap-2">
                <img
                  src="/assets/images/logo.svg"
                  alt="Temoc Logo"
                  className="h-[18px] cursor-pointer"
                />
                <p className="text-sm text-[#333333]">Temoc Token</p>
              </Link>
            </div>
          </div>
        </nav>
      </div>
      <div className="flex flex-wrap justify-center gap-4 bg-white p-4 font-display">
        {footerData.map((item, i) => (
          <Link
            href={item.href}
            className="text-[11px] font-medium text-[#A7A7A7] hover:text-black"
            key={i}
          >
            {item.name}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
