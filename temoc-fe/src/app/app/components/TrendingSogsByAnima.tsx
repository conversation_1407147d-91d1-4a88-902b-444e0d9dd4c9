import React from 'react';

export const TrendingSogsByAnima = () => {
  // Album data for mapping
  const albums = [
    {
      id: 1,
      title: 'Ordinary',
      artist: '<PERSON>',
      image: '/image.png',
      comingSoon: true,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
    {
      id: 2,
      title: 'Back to friends',
      artist: 'Sombrr',
      image: '/image-1.png',
      comingSoon: false,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
    {
      id: 3,
      title: 'APT',
      artist: '<PERSON>',
      image: '/image-2.png',
      comingSoon: false,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
    {
      id: 4,
      title: '<PERSON><PERSON>',
      artist: '<PERSON>',
      image: '/image-3.png',
      comingSoon: false,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
    {
      id: 5,
      title: 'Die with a smile',
      artist: '<PERSON> <PERSON>',
      image: '/image-4.png',
      comingSoon: false,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
    {
      id: 6,
      title: 'No one notice',
      artist: 'The Marías',
      image: '/image-5.png',
      comingSoon: false,
      bgColor: 'bg-white',
      cardBgColor: 'bg-[#e3e3e3]',
    },
  ];

  return (
    <div className="flex w-full flex-col items-start gap-[21px]">
      <div className="flex w-full items-start justify-between gap-4">
        {albums.map((album) => (
          <div key={album.id} className="relative w-[212px]">
            <div className="relative">
              <div
                className={`mx-auto mt-[178px] h-[100px] w-[187px] rounded-[10px] ${album.cardBgColor}`}
              />

              <div
                className={`absolute left-0 top-0 p-4 ${album.bgColor} rounded-[10px] border-0 shadow-none`}
              >
                <div className="p-0">
                  <div className="flex flex-col items-start gap-4">
                    <img
                      className={`${album.id === 6 ? 'w-[26px]' : 'w-[180px]'} h-[180px] object-cover`}
                      alt={album.title}
                      src={album.image}
                    />

                    <div className="flex flex-col items-start gap-2.5">
                      <div className="z-[1] mt-[-1.00px] text-sm font-medium leading-[25.6px] text-[#333333]">
                        {album.title}
                      </div>

                      <div className="z-0 flex items-center gap-[5px]">
                        <div className="h-5 w-5">
                          <img
                            className="h-full w-full object-cover"
                            alt="Artist avatar"
                            src="/ellipse-372-5.png"
                          />
                        </div>

                        <div className="text-xs font-normal leading-[13.2px] text-[#666666]">
                          Artist • {album.artist}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {album.comingSoon && (
                <div className="absolute left-[35px] top-[122px] rounded-[400px] border border-solid bg-[#ff6e00] px-6 py-3 text-xs font-medium leading-3 tracking-[0.72px] text-white">
                  COMING SOON
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
