'use client';

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { api } from '@/services/api';
import { contentApi } from '@/services/content';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { ReportModal } from '@/components/reports/ReportModal';
import { useAuth } from '@/hooks/useAuth';
import { useFollowSync } from '@/hooks/useFollowSync';
import ArtistProfileHeader from '@/components/profile/ArtistProfileHeader';
import ArtistProfileContent from '@/components/profile/ArtistProfileContent';
import Modal from '@/components/common/Modal';

interface UserProfile {
  _id: string;
  username: string;
  displayName: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  coverPicture?: string;
  description?: string;
  location?: string;
  role: 'fan' | 'artist';
  profileViews: number;
  lastActiveAt: string;
  createdAt: string;
  isFollowing: boolean; // Add follow status to profile interface
  artistProfile?: {
    bio?: string;
    genre?: string;
    isVerified: boolean;
    socialLinks: {
      platform: string;
      url: string;
    }[];
  };
}

export default function ProfilePage() {
  const params = useParams();
  const username = params.username as string;
  const [showReportModal, setShowReportModal] = useState(false);
  const { user: currentUser } = useAuth();

  // Get user profile
  const {
    data: profile,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['profile', username],
    queryFn: async (): Promise<UserProfile> => {
      const response = await api.get(`/users/profile/${username}`);
      return response.data;
    },
  });

  // Get artist content (tracks, albums, libraries)
  const { data: artistContent, isLoading: contentLoading } = useQuery({
    queryKey: ['artist-content', profile?._id],
    queryFn: async () => {
      if (!profile?._id) return null;

      const [tracksRes, albumsRes, librariesRes] = await Promise.all([
        contentApi.getTracksByUser(profile._id),
        contentApi.getAlbumsByUser(profile._id),
        contentApi.getLibrariesByUser(profile._id),
      ]);

      return {
        tracks: tracksRes?.tracks || [],
        albums: albumsRes?.albums || [],
        libraries: librariesRes?.libraries || [],
      };
    },
    enabled: !!profile?._id,
  });

  // Use follow sync hook for real-time synchronization
  const {
    isFollowing,
    isLoading: isFollowLoading,
    handleFollowToggle,
    followStats,
    loadingAction,
  } = useFollowSync({
    userId: profile?._id || '',
    displayName: profile?.displayName || profile?.username,
    initialFollowStatus: profile?.isFollowing,
  });

  // Check if current user is viewing their own profile
  const isOwnProfile =
    currentUser && profile && currentUser.username === profile.username;

  if (isLoading || contentLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-bold text-gray-900">
            Profile not found
          </h2>
          <p className="text-gray-600">
            The user you&apos;re looking for doesn&apos;t exist or has been
            removed.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Artist Profile Header - Billie Eilish Style */}
      <ArtistProfileHeader
        profile={profile}
        followStats={followStats}
        isFollowing={isFollowing}
        isFollowLoading={isFollowLoading}
        handleFollowToggle={handleFollowToggle}
        loadingAction={loadingAction}
        // @ts-expect-error
        isOwnProfile={isOwnProfile}
        onReportClick={() => setShowReportModal(true)}
      />

      {/* Artist Profile Content - Billie Eilish Style */}
      <ArtistProfileContent
        profile={profile}
        artistContent={artistContent}
        followStats={followStats}
      />

      {/* Report Modal */}
      {showReportModal && (
        <Modal
          className="hideScrollbar !h-max !max-w-[650px] !px-5 !pb-6 md:pb-0"
          show={showReportModal}
          hide={setShowReportModal}
        >
          <ReportModal
            reportedUserId={profile._id}
            onClose={() => setShowReportModal(false)}
          />
        </Modal>
      )}
    </div>
  );
}
