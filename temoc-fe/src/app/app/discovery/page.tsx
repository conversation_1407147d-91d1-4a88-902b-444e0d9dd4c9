'use client';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { discovery<PERSON><PERSON>, DiscoveryFilt<PERSON>, Artist } from '@/services/discovery';
import { ArtistCard } from '@/components/discovery/ArtistCard';
import { DiscoveryFilters as FiltersComponent } from '@/components/discovery/DiscoveryFilters';
import { SearchBar } from '@/components/discovery/SearchBar';
// import { TrendingSection } from '@/components/discovery/TrendingSection';
import { Pagination } from '@/components/ui/Pagination';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
// import { ArtistNewCard } from '@/components/discovery/ArtistNewCard';
import ResponsiveFilter from '@/components/discovery/ResponsiveFilter';
// import { Bars3Icon } from '@heroicons/react/24/outline';
import { LuFilter } from 'react-icons/lu';
export default function DiscoveryPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [filters, setFilters] = useState<DiscoveryFilters>({
    sortBy: 'popular',
    sortOrder: 'desc',
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');

  // Main discovery query
  const {
    data: discoveryData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['discovery', 'artists', filters, currentPage, searchQuery],
    queryFn: () => {
      if (searchQuery.trim()) {
        return discoveryApi.searchArtists(
          searchQuery,
          filters,
          currentPage,
          20,
        );
      }
      return discoveryApi.discoverArtists(filters, currentPage, 20);
    },
  });

  // Trending artists query
  // const { data: trendingArtists } = useQuery({
  //   queryKey: ['discovery', 'trending'],
  //   queryFn: () => discoveryApi.getTrendingArtists(10),
  // });

  // Popular artists query
  // const { data: popularArtists } = useQuery({
  //   queryKey: ['discovery', 'popular'],
  //   queryFn: () => discoveryApi.getPopularArtists(10),
  // });

  // New verified artists query
  // const { data: newVerifiedArtists } = useQuery({
  //   queryKey: ['discovery', 'new-verified'],
  //   queryFn: () => discoveryApi.getNewVerifiedArtists(10),
  // });

  const handleFiltersChange = (newFilters: DiscoveryFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="mb-2 text-2xl font-bold text-gray-900">
            Something went wrong
          </h2>
          <p className="text-gray-600">
            Failed to load artists. Please try again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen rounded-bl-lg rounded-tl-lg bg-[#F8F8F8] p-4 sm:p-7">
      <div className="">
        {/* Header */}
        <div className="mb-8 flex flex-col items-center justify-between gap-4 sm:flex-row">
          <p
            // onClick={() => setShowModal(true)}
            className="w-max whitespace-nowrap text-[22px] font-normal uppercase"
          >
            Discover Artists
          </p>
          <div className="w-full lg:w-[500px]">
            <SearchBar onSearch={handleSearch} />
          </div>
        </div>

        {/* Trending Section - Only show when not searching */}
        {/* {!searchQuery && (
          <TrendingSection
            trendingArtists={trendingArtists}
            popularArtists={popularArtists}
            newVerifiedArtists={newVerifiedArtists}
          />
        )} */}

        <div className="flex flex-col gap-5 sm:gap-8 lg:flex-row">
          <button
            className="flex items-center gap-2 p-2.5 lg:hidden xs:-m-0 xs:p-0"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <LuFilter className="h-6 w-6 text-black" />
            {/* <Bars3Icon aria-hidden="true"  /> */}
            <p className="!font-display text-base font-medium">Filters</p>
          </button>
          {/* </div> */}
          {/* Filters Sidebar */}

          <div className="hidden lg:block">
            <FiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </div>

          <ResponsiveFilter isOpen={sidebarOpen} onClose={setSidebarOpen}>
            <FiltersComponent
              filters={filters}
              onFiltersChange={handleFiltersChange}
            />
          </ResponsiveFilter>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="mb-6 flex items-center justify-between">
              <div>
                {searchQuery ? (
                  <h2 className="mb-4 !font-display !text-xl font-semibold text-gray-900 lg:!text-2xl">
                    Search results for &quot;{searchQuery}&quot;
                  </h2>
                ) : (
                  <h2 className="text-xl font-semibold text-gray-900"></h2>
                )}
                {discoveryData && (
                  <p className="text-xl font-semibold uppercase text-[#333333]">
                    {discoveryData.pagination.total} artists found
                  </p>
                )}
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            )}

            {/* Artists Grid */}
            {discoveryData && !isLoading && (
              <>
                {discoveryData.artists.length > 0 ? (
                  <>
                    <div className="mb-8 grid grid-cols-1 gap-y-5 sm:grid-cols-2 sm:gap-6 sm:gap-y-10 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
                      {discoveryData.artists.map((artist: Artist) => (
                        <ArtistCard key={artist._id} artist={artist} />
                      ))}
                    </div>

                    {/* Pagination */}
                    {discoveryData.pagination.pages > 1 && (
                      <Pagination
                        currentPage={currentPage}
                        totalPages={discoveryData.pagination.pages}
                        onPageChange={handlePageChange}
                      />
                    )}
                  </>
                ) : (
                  <div className="py-12 text-center">
                    <div className="mb-4 text-gray-400">
                      <svg
                        className="mx-auto h-12 w-12"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    </div>
                    <h3 className="mb-2 text-lg font-medium text-gray-900">
                      No artists found
                    </h3>
                    <p className="text-gray-600">
                      {searchQuery
                        ? 'Try adjusting your search terms or filters'
                        : 'No artists match your current filters'}
                    </p>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
