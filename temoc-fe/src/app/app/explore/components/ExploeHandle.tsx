'use client';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import SocialLinks from '../../trading/components/SocialLinks';
import TrendingSongs from './TrendingSongs';
import PopularArtist from '../../artist/components/PopularArtist';
import TopGeners from './TopGeners';
import Profile from '../../trading/components/Profile';
import Summary from './Summary';
import PopularVideos from './PopularVideos';
import ResponsiveTab from './ResponsiveTab';
import { userService } from '@/services/user.service';

import LoadingSkelton from './LoadingSkelton';
import TrendingThisWeek from './TrendingThisWeek';
// import Modal from '@/components/common/Modal';
// import Billie from '@/components/common/Billie/Billie';
// import { CurrentViewRes } from '@/types/user.interface';
// import { useQuery } from '@tanstack/react-query';

const ExploeHandle = () => {
  const [exploreDetail, setexploreDetail] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(true);
  const router = useRouter();
  // const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const checkViewMode = async () => {
      try {
        // Check if user is authenticated first
        const token = localStorage.getItem('dynamic_authentication_token');
        if (!token) {
          console.log('No token found, staying on explore page');
          setIsRedirecting(false);
          return;
        }

        // Get current view mode and role
        const response = await userService.getCurrentView();
        const viewMode = response?.data?.viewMode;
        const role = response?.data?.role;
        console.log('Current view mode in explore:', viewMode, 'role:', role);

        // Role-based navigation logic:
        // 1. Fans always stay on explore page
        // 2. Artists with viewMode 'artist' go to artist dashboard
        // 3. Artists with viewMode 'fan' stay on explore page
        if (role === 'artist' && viewMode === 'artist') {
          console.log('Artist in artist mode, redirecting to dashboard');
          router.replace('/artist/initial-dashboard?tab=Home');
        } else {
          console.log(
            'Staying on explore page - role:',
            role,
            'viewMode:',
            viewMode,
          );
          setIsRedirecting(false);
        }
      } catch (error) {
        console.error('Error checking view mode:', error);
        // If there's an error (like 401), just show the explore page
        setIsRedirecting(false);
      }
    };

    // Only run once on mount for fast loading
    checkViewMode();
  }, [router]); // Include router in dependencies

  if (isRedirecting) {
    return <LoadingSkelton />;
  }
  return (
    <div>
      {exploreDetail ? (
        <div>
          <Profile setexploreDetail={setexploreDetail} />
          <div className="hidden p-7 sm:block">
            <Summary />
            <SocialLinks explore />
            <PopularVideos />
          </div>
          <div className="block sm:hidden">
            <ResponsiveTab />
          </div>
        </div>
      ) : (
        <div className="p-3 sm:p-4 lg:p-7">
          <div className="">
            <p
              // onClick={() => setShowModal(true)}
              className="w-max text-[22px] font-normal"
            >
              EXPLORE
            </p>
          </div>
          {/* <Modal
            show={showModal}
            hide={() => setShowModal(false)}
            className="w-full max-w-[489px]"
          >
            <Billie />
          </Modal> */}
          <TrendingSongs setexploreDetail={setexploreDetail} />
          <TrendingThisWeek />
          <PopularArtist />
          <TopGeners />
        </div>
      )}
    </div>
  );
};

export default ExploeHandle;
