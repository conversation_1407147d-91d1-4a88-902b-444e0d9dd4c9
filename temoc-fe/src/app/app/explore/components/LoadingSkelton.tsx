import CardSkeleton from '@/components/ui/Skelton/CardSkelton';
import React from 'react';

const LoadingSkelton = () => {
  return (
    <>
      <h3 className="py-5 text-base font-semibold uppercase sm:text-xl">
        Trending Songs
      </h3>
      <div className="hideScrollbar flex gap-3 overflow-auto px-2">
        {[...Array(7)].map((_, index) => (
          <React.Fragment key={`trending-${index}`}>
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
          </React.Fragment>
        ))}
      </div>
      <h3 className="py-7 text-base font-semibold sm:text-xl">
        POPULAR ARTISTS
      </h3>
      <div className="hideScrollbar mt-5 flex gap-3 overflow-auto px-2">
        {[...Array(7)].map((_, index) => (
          <React.Fragment key={`popular-${index}`}>
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
            <CardSkeleton />
          </React.Fragment>
        ))}
      </div>
    </>
  );
};

export default LoadingSkelton;
