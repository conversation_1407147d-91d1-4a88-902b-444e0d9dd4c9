import VideoCard from '@/components/ui/VideoCard/VideoCard';
import React from 'react';

const Carddata = [
  {
    src: '/assets/images/artist/videocard.png',
    name: 'Losing My Religion',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard2.png',
    name: 'Wonderful Tonight',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard3.png',
    name: 'Out of My Mine',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard4.png',
    name: 'I Didnot Know',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard5.png',
    name: 'Some Feeling',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard6.png',
    name: 'Lemon Tree',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard2.png',
    name: 'Losing My Religion',
    type: 'Date and type',
  },
  {
    src: '/assets/images/artist/videocard4.png',
    name: 'Losing My Religion',
    type: 'Date and type',
  },
];

const PopularVideos = () => {
  return (
    <>
      <div className="mt-12">
        <h3 className="text-xl font-semibold uppercase">POPULAR VIDEOS</h3>
        <div className="mt-4 flex flex-wrap gap-6 blur-md">
          {Carddata.map((items, index) => {
            return (
              <div key={index}>
                <VideoCard
                  src={items.src}
                  songName={items.name}
                  songType={items.type}
                />
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default PopularVideos;
