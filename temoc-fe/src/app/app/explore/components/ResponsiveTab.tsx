'use client';
import React, { useState } from 'react';
import Summary from './Summary';
import TopFans from '../../trading/components/TopFans';
import SocialLinks from '../../trading/components/SocialLinks';
import PopularVideos from './PopularVideos';
import Comments from '../../trading/components/Comments';

const ResponsiveTab = () => {
  const [activeTab, setActiveTab] = useState('Description');

  return (
    <>
      <div className="mx-auto my-5 flex w-full max-w-[311px] items-center justify-between rounded-[10px] bg-white p-5 text-sm">
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Description' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Description')}
        >
          Description
        </p>
        <div className="h-4 w-0.5 border border-black bg-black"></div>
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Media' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Media')}
        >
          Media
        </p>
        <div className="h-4 w-0.5 border border-black bg-black"></div>
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Comments' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Comments')}
        >
          Comments
        </p>
      </div>
      {/* descriptiondiv */}

      {activeTab === 'Description' && (
        <div>
          <Summary />
          <TopFans />
        </div>
      )}

      {/* media div */}

      {activeTab === 'Media' && (
        <div>
          <SocialLinks />
          <PopularVideos />
        </div>
      )}

      {/* comments div */}
      {activeTab === 'Comments' && (
        <div>
          <Comments />
        </div>
      )}
    </>
  );
};

export default ResponsiveTab;
