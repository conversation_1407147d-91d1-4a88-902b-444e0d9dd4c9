import React, { useState, useEffect } from 'react';

const Summary = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640);
    };
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const fullText1 = `The presale offers an exclusive opportunity for fans and early
supporters to purchase the artist’s native token before it becomes
publicly tradable. Structured as a fair launch, the presale aims to
raise between a soft cap of $40,000 and a hard cap of $50,000, with
$45,000 already raised from over 765 contributors. By participating,
supporters not only back the artist’s journey but also gain early
access to a range of benefits, including premium content, gated
community features, and tipping functionality within the platform.`;

  const fullText2 = `All funds raised will be used to support the artist’s growth, content
production, and ongoing development of their digital ecosystem. To
ensure trust and transparency, the token is deployed via a verified
smart contract with automatic liquidity lock upon successful presale
completion.`;

  const shortText1 =
    'The presale offers an exclusive opportunity for fans and early supporters to purchase the artist’s native token before it becomes publicly tradable...';

  return (
    <div className="flex flex-col justify-between gap-5 sm:gap-[30px] xl:flex-row">
      <div className="w-full rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-6 xl:w-[70%]">
        <h3 className="text-sm font-normal sm:text-lg">Description</h3>
        <p className="mt-4 text-xs font-normal text-[#666666] sm:text-base">
          {isMobile && !isExpanded ? shortText1 : fullText1}
        </p>
        {isMobile && isExpanded && (
          <p className="mt-2 text-xs font-normal text-[#666666] sm:mt-4 sm:text-base">
            {fullText2}
          </p>
        )}
        {isMobile && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="mt-2 text-xs text-primary hover:underline focus:outline-none sm:text-base"
          >
            {isExpanded ? 'Read less' : 'Read more'}
          </button>
        )}
        {/* Desktop-only full content */}
        {!isMobile && (
          <p className="mt-2 text-xs font-normal text-[#666666] sm:mt-4 sm:text-base">
            {fullText2}
          </p>
        )}
      </div>

      <div className="w-full rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-7 xl:w-[30%]">
        <h3 className="text-sm font-normal sm:text-lg">Summary</h3>
        <div className="mt-2.5 flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5 sm:py-5">
          <p className="text-xs font-normal text-[#666666] sm:text-base">
            Launch date
          </p>
          <p className="text-xs font-normal text-primary sm:text-base">
            April 10, 2025
          </p>
        </div>
        <div className="flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5 sm:py-5">
          <p className="text-xs font-normal text-[#666666] sm:text-base">
            Submitted
          </p>
          <p className="text-xs font-normal text-primary sm:text-base">
            June 10, 2025
          </p>
        </div>
        <div className="flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5 sm:py-5">
          <p className="text-xs font-normal text-[#666666] sm:text-base">
            Softcap
          </p>
          <p className="text-xs font-normal text-primary sm:text-base">
            USDT 20B
          </p>
        </div>
        <div className="flex justify-between py-2.5 sm:py-5">
          <p className="text-xs font-normal text-[#666666] sm:text-base">
            Hardcap
          </p>
          <p className="text-xs font-normal text-primary sm:text-base">
            USDT 30B
          </p>
        </div>
      </div>
    </div>
  );
};

export default Summary;
