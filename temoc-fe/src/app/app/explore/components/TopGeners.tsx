import { Button } from '@/components/common';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';

const data = [
  {
    src: '/assets/images/artist/content1.svg',
    name: 'Lounge',
  },
  {
    src: '/assets/images/artist/content2.svg',
    name: 'Lounge',
  },
  {
    src: '/assets/images/artist/content3.svg',
    name: 'Lounge',
  },
  {
    src: '/assets/images/artist/content4.svg',
    name: 'Lounge',
  },
  {
    src: '/assets/images/artist/content5.svg',
    name: 'Lounge',
  },
  {
    src: '/assets/images/artist/content6.svg',
    name: 'Lounge',
  },
];

const TopGeners = () => {
  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          TOP GENRES
        </h3>
        <Button className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]">
          <u> View All</u> <FaLongArrowAltRight className="text-black" />
        </Button>
      </div>
      <div className="mt-2 grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8">
        {data.map((item, index) => {
          return (
            <div
              key={index}
              className="relative h-[135px] w-full overflow-hidden rounded-md"
            >
              <img
                src={item.src}
                className="absolute top-0 h-full w-full rounded-md object-cover"
              />
              <div className="relative px-4 py-3">
                <h3 className="text-lg font-bold text-white lg:text-xl">
                  Lounge
                </h3>
              </div>
            </div>
            // <Link href="/app/trading" key={index}>
            //   <Card
            //     imageSrc={item.src}
            //     name={item.name}
            //     profileSrc={item.profileSrc}
            //     artistName={item.artistName}
            //   />
            // </Link>
          );
        })}
      </div>
    </div>
  );
};

export default TopGeners;
