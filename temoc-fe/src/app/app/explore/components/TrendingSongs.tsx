import { But<PERSON> } from '@/components/common';
import Card from '@/components/ui/Card/Card';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';
import CommonSlider from '@/components/common/CommonSlider';

const data = [
  {
    src: '/assets/images/artist/popular1.svg',
    name: '<PERSON>',
    // followers: '150.91 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • <PERSON>',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    // followers: '126.15 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Sombrr',
  },
  {
    src: '/assets/images/artist/popular3.svg',
    name: '<PERSON>',
    // followers: '124.24 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • <PERSON>',
  },
  {
    src: '/assets/images/artist/popular4.svg',
    name: '<PERSON>',
    // followers: '107.89 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • <PERSON>',
  },
  {
    src: '/assets/images/artist/popular5.svg',
    name: '<PERSON>drick <PERSON>',
    // followers: '103.63 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Lady Gaga',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    // followers: '126.15 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Alex Warren',
  },
  {
    src: '/assets/images/artist/popular1.svg',
    name: 'Bruno Mars',
    // followers: '150.91 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Alex Warren',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    // followers: '126.15 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Sombrr',
  },
  {
    src: '/assets/images/artist/popular3.svg',
    name: 'Lady Gaga',
    // followers: '124.24 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Bruno Mars',
  },
  {
    src: '/assets/images/artist/popular4.svg',
    name: 'Billie Eilish',
    // followers: '107.89 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Ed Sheeran',
  },
  {
    src: '/assets/images/artist/popular5.svg',
    name: 'Kendrick Lamar',
    // followers: '103.63 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Lady Gaga',
  },
  {
    src: '/assets/images/artist/popular2.svg',
    name: 'The Weeknd',
    // followers: '126.15 million',
    profileSrc: '/assets/images/artist/artist.png',
    artistName: 'Artist • Alex Warren',
  },
];
interface Iprops {
  setexploreDetail?: any;
}
const TrendingSongs = ({ setexploreDetail }: Iprops) => {
  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          Trending Songs
        </h3>
        <Button className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]">
          <u> View All</u> <FaLongArrowAltRight className="text-black" />
        </Button>
      </div>
      <div className="mt-2">
        <CommonSlider delay={true}>
          {data.map((item, index) => (
            // <Link href="/app/trading" key={index}>
            <div
              onClick={() => {
                setexploreDetail(true);
              }}
              key={index}
              className="mb-3 cursor-pointer"
            >
              <Card
                imageSrc={item.src}
                name={item.name}
                profileSrc={item.profileSrc}
                artistName={item.artistName}
              />
            </div>
            // </Link>
          ))}
        </CommonSlider>
      </div>
    </div>
  );
};

export default TrendingSongs;
