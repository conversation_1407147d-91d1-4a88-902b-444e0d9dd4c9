import Card from '@/components/ui/Card/Card';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';
import CommonSlider from '@/components/common/CommonSlider';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { discoveryApi } from '@/services/discovery';

const TrendingThisWeek = () => {
  const { data: trendingArtists } = useQuery({
    queryKey: ['discovery', 'trending'],
    queryFn: () => discoveryApi.getTrendingArtists(10),
  });

  console.log(trendingArtists, 'trendingArtists');

  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          Trending This Week
        </h3>
        <Link
          href="/app/discovery?sortBy=popular"
          className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]"
        >
          <u> View All</u> <FaLongArrowAltRight className="text-black" />
        </Link>
      </div>
      <div className="mt-6">
        {trendingArtists && trendingArtists.length > 0 && (
          <CommonSlider delay={true}>
            {trendingArtists?.slice(0, 5).map((artist) => (
              <div className="mb-3" key={artist._id}>
                <Link
                  href={`/app/discovery/${artist.username}`}
                  className="cursor-pointer"
                >
                  <Card
                    imageSrc={
                      artist?.coverPicture ?? '/assets/images/women.avif'
                    }
                    name={artist.username}
                    profileSrc={
                      artist?.avatarUrl ?? '/assets/images/women.avif'
                    }
                    artistName={artist.displayName}
                  />
                </Link>
              </div>
            ))}
          </CommonSlider>
        )}
      </div>
    </div>
  );
};

export default TrendingThisWeek;
