'use client';

import React, { useState } from 'react';
import {
  ArrowLeft,
  CheckCircle,
  Search,
  Book,
  CreditCard,
  Shield,
  DollarSign,
  Users,
  Settings,
  FileText,
  MessageCircle,
  Phone,
  Mail,
  ChevronRight,
} from 'lucide-react';

const HelpPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentView, setCurrentView] = useState({
    category: null,
    slug: null,
  });
  const headingstyle = '!font-display !text-2xl sm:!text-3xl !font-semibold';
  const navigateToArticle = (categoryId: any, articleSlug: any) => {
    setCurrentView({ category: categoryId, slug: articleSlug });
  };

  const navigateBack = () => {
    setCurrentView({ category: null, slug: null });
  };

  const { category, slug } = currentView;

  // Help topics configuration
  const helpTopics = [
    {
      id: 'getting-started',
      icon: Book,
      title: 'Getting Started',
      description:
        'Learn the basics of setting up your artist profile on TEMOC',
      iconColor: 'text-orange-500',
      bgColor: 'bg-orange-500',
      articles: [
        { title: 'Introduction to TEMOC Platform', slug: 'introduction' },
        { title: 'Creating Your Artist Profile', slug: 'creating-profile' },
        { title: 'Launching Your Token', slug: 'launching-token' },
        { title: 'Dashboard Overview', slug: 'dashboard-overview' },
      ],
    },
    {
      id: 'managing-gated-content',
      icon: Shield,
      title: 'Managing Gated Content',
      description:
        'Control which fans get access to exclusive content based on token holdings',
      iconColor: 'text-red-500',
      bgColor: 'bg-red-500',
      articles: [
        { title: 'Creating Gated Content', slug: 'creating-gated-content' },
        { title: 'Updating or Deleting Content', slug: 'updating-content' },
        { title: 'Managing Membership Levels', slug: 'membership-levels' },
        {
          title: 'Troubleshooting Fan Access Issues',
          slug: 'troubleshooting-access',
        },
      ],
    },
    {
      id: 'buy-sell-tax-system',
      icon: CreditCard,
      title: 'Buy/Sell Tax System',
      description:
        'Understand how token buy/sell fees work and where the revenue goes',
      iconColor: 'text-orange-600',
      bgColor: 'bg-orange-600',
      articles: [
        { title: 'Current Tax Rates', slug: 'tax-rates' },
        { title: 'Revenue Distribution', slug: 'revenue-distribution' },
        { title: 'Tax Adjustment Requests', slug: 'tax-adjustments' },
      ],
    },
    {
      id: 'earnings-withdrawals',
      icon: DollarSign,
      title: 'Earnings & Withdrawals',
      description: 'How your monthly artist earnings work',
      iconColor: 'text-green-500',
      bgColor: 'bg-green-500',
      articles: [
        { title: 'Monthly Earnings Breakdown', slug: 'earnings-breakdown' },
        { title: 'Withdraw Process', slug: 'withdraw-process' },
        { title: 'Earnings Statements', slug: 'earnings-statements' },
      ],
    },
    {
      id: 'referral-program',
      icon: Users,
      title: 'Referral Program',
      description:
        'Invite fans & other artists to TEMOC and earn referral rewards',
      iconColor: 'text-blue-500',
      bgColor: 'bg-blue-500',
      articles: [
        { title: 'How Referrals Work', slug: 'how-referrals-work' },
        { title: 'Referral Bonus Payouts', slug: 'referral-payouts' },
        { title: 'Tracking Referral Stats', slug: 'referral-stats' },
      ],
    },
    {
      id: 'account-security',
      icon: Settings,
      title: 'Account & Security',
      description: 'Manage your account and protect your profile',
      iconColor: 'text-purple-500',
      bgColor: 'bg-purple-500',
      articles: [
        { title: 'Profile Settings', slug: 'profile-settings' },
        { title: '2FA Setup', slug: '2fa-setup' },
        { title: 'Lost Access Recovery', slug: 'lost-access' },
        { title: 'Account Deactivation', slug: 'account-deactivation' },
      ],
    },
    {
      id: 'policies-legal',
      icon: FileText,
      title: 'Policies & Legal',
      description: 'Platform rules, terms, and responsibilities',
      iconColor: 'text-gray-500',
      bgColor: 'bg-gray-500',
      articles: [
        { title: 'Terms of Service', slug: 'terms-of-service' },
        { title: 'Privacy Policy', slug: 'privacy-policy' },
        { title: 'Community Guidelines', slug: 'community-guidelines' },
        { title: 'Artist Token Compliance Rules', slug: 'compliance-rules' },
      ],
    },
  ];

  // Article content database
  type Article = {
    title: string;
    content: React.ReactNode;
  };

  type ArticleContent = {
    [category: string]: {
      [slug: string]: Article;
    };
  };

  const articleContent: ArticleContent = {
    'getting-started': {
      introduction: {
        title: 'Introduction to TEMOC Platform',
        content: (
          <div className="space-y-6">
            <div>
              <h2 className={`mb-4 ${headingstyle}`}>What is TEMOC?</h2>
              <p className="leading-relaxed text-gray-600">
                TEMOC is a revolutionary tokenized creator platform that
                empowers artists to launch their own custom tokens and build
                exclusive communities for their fans. Think of it as a hybrid
                between Patreon, OnlyFans, and a decentralized exchange - all
                powered by blockchain technology.
              </p>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>How It Works</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="mt-1 h-5 w-5 flex-shrink-0 text-green-500" />
                  <div>
                    <h3 className="font-medium">For Artists</h3>
                    <p className="text-gray-600">
                      Launch your own token, set up gated content, and monetize
                      your fanbase through exclusive access and community
                      rewards.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="mt-1 h-5 w-5 flex-shrink-0 text-green-500" />
                  <div>
                    <h3 className="font-medium">For Fans</h3>
                    <p className="text-gray-600">
                      Buy artist tokens to unlock exclusive content, get early
                      access to releases, and support your favorite creators
                      directly.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>Key Features</h2>
              <ul className="space-y-3 text-gray-600">
                <li>
                  • <strong>Token Launch System:</strong> Easy-to-use tools for
                  launching artist tokens on multiple blockchains
                </li>
                <li>
                  • <strong>Gated Content:</strong> Upload exclusive content
                  accessible only to token holders
                </li>
                <li>
                  • <strong>Revenue Sharing:</strong> Earn from token
                  transactions, content access, and referrals
                </li>
                <li>
                  • <strong>Community Building:</strong> Build loyal fanbases
                  through tokenized access and rewards
                </li>
                <li>
                  • <strong>Multi-Chain Support:</strong> Deploy on Ethereum,
                  BSC, Solana, or Base
                </li>
                <li>
                  • <strong>Non-Custodial:</strong> You maintain full control of
                  your tokens and earnings
                </li>
              </ul>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>Getting Started</h2>
              <p className="mb-4 text-gray-600">
                Ready to join TEMOC? Here&quot;s what you need to do:
              </p>
              <ol className="space-y-2 text-gray-600">
                <li>1. Create your artist profile</li>
                <li>2. Connect your wallet or create one with email</li>
                <li>3. Launch your artist token</li>
                <li>4. Upload your first gated content</li>
                <li>5. Share with your community and start earning</li>
              </ol>
            </div>

            <div className="rounded-lg bg-gray-50 p-6">
              <h3 className="mb-2 font-semibold">Need Help?</h3>
              <p className="text-sm text-gray-600">
                Our support team is here to help you succeed. Reach out via live
                chat, email, or phone if you have any questions.
              </p>
            </div>
          </div>
        ),
      },
      'creating-profile': {
        title: 'Creating Your Artist Profile',
        content: (
          <div className="space-y-6">
            <div>
              <h2 className={`mb-4 ${headingstyle}`}>Step 1: Sign Up</h2>
              <p className="mb-4 text-gray-600">
                Create your TEMOC account using one of these methods:
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>
                  • <strong>Email:</strong> Standard email registration with
                  password
                </li>
                <li>
                  • <strong>Google:</strong> Quick signup with your Google
                  account
                </li>
                <li>
                  • <strong>Twitter/X:</strong> Connect your existing Twitter
                  presence
                </li>
                <li>
                  • <strong>Discord:</strong> Perfect for gaming and music
                  artists
                </li>
              </ul>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>
                Step 2: Basic Profile Information
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="mb-2 font-medium">Artist Name & Username</h3>
                  <p className="text-sm text-gray-600">
                    Choose a memorable artist name and unique username. Your
                    username will be part of your profile URL
                    (temoc.io/artist/yourusername).
                  </p>
                </div>
                <div>
                  <h3 className="mb-2 font-medium">Profile Picture</h3>
                  <p className="text-sm text-gray-600">
                    Upload a high-quality profile image (minimum 400x400px, PNG
                    or JPG format). This represents your brand across the
                    platform.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>
                Step 3: Visual Branding
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="mb-2 font-medium">Banner Image</h3>
                  <p className="text-sm text-gray-600">
                    Upload a banner image (1920x480px recommended) that
                    showcases your artistic style. This appears at the top of
                    your profile page.
                  </p>
                </div>
                <div>
                  <h3 className="mb-2 font-medium">Artist Bio</h3>
                  <p className="text-sm text-gray-600">
                    Write a compelling bio that tells your story. Include your
                    musical style, achievements, and what fans can expect from
                    your content. Keep it under 500 characters for optimal
                    display.
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-blue-200 bg-blue-50 p-6">
              <h3 className="mb-2 font-semibold text-blue-700">Pro Tips</h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Use consistent branding across all uploaded images</li>
                <li>• Keep your bio engaging but concise</li>
                <li>• Link verified social accounts for faster verification</li>
                <li>• Update your profile regularly to keep fans engaged</li>
              </ul>
            </div>
          </div>
        ),
      },
      'launching-token': {
        title: 'Launching Your Token',
        content: (
          <div className="space-y-6">
            <div>
              <h2 className={`mb-4 ${headingstyle}`}>
                Choose Your Launch Type
              </h2>
              <p className="mb-6 text-gray-600">
                TEMOC offers two main ways to launch your artist token, each
                designed for different goals and audiences.
              </p>

              <div className="grid gap-6 md:grid-cols-2">
                <div className="rounded-lg border border-gray-200 p-6">
                  <h3 className="mb-3 text-lg font-semibold text-blue-600">
                    Direct Liquidity Launch
                  </h3>
                  <p className="mb-4 text-sm text-gray-600">
                    Deploy your token with immediate liquidity on decentralized
                    exchanges. Best for established artists with existing
                    fanbases.
                  </p>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• Instant trading availability</li>
                    <li>• Set your own initial price</li>
                    <li>• Immediate liquidity pool creation</li>
                    <li>• Higher launch fee required</li>
                  </ul>
                </div>

                <div className="rounded-lg border border-gray-200 p-6">
                  <h3 className="mb-3 text-lg font-semibold text-purple-600">
                    Fair Launch Presale
                  </h3>
                  <p className="mb-4 text-sm text-gray-600">
                    Community-driven presale with soft and hard caps. Perfect
                    for building anticipation and early community engagement.
                  </p>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• Set presale duration and caps</li>
                    <li>• Community price discovery</li>
                    <li>• Built-in marketing period</li>
                    <li>• Lower upfront costs</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <h2 className={`mb-4 ${headingstyle}`}>Token Configuration</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="mb-2 font-medium">Basic Token Details</h3>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>
                      • <strong>Token Name:</strong> Your artist or brand name
                      (e.g., &quot;Drake Token&quot;)
                    </li>
                    <li>
                      • <strong>Token Symbol:</strong> 3-5 character ticker
                      (e.g., &quot;DRAKE&quot;)
                    </li>
                    <li>
                      • <strong>Total Supply:</strong> Total number of tokens
                      (recommended: 1,000,000 - 100,000,000)
                    </li>
                    <li>
                      • <strong>Decimals:</strong> Usually 18 (standard for most
                      tokens)
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="rounded-lg border border-orange-200 bg-orange-50 p-6">
              <h3 className="mb-2 font-semibold text-orange-700">
                Important Notes
              </h3>
              <ul className="space-y-1 text-sm text-orange-600">
                <li>• Token details cannot be changed after deployment</li>
                <li>• Always test with small amounts first</li>
                <li>• Keep your private keys secure</li>
                <li>• Consider your tax structure before launching</li>
              </ul>
            </div>
          </div>
        ),
      },
      'dashboard-overview': {
        title: 'Dashboard Overview',
        content: (
          <div className="space-y-6">
            <div>
              <h2 className={`mb-4 ${headingstyle}`}>Dashboard Sections</h2>
              <p className="mb-6 text-gray-600">
                Your artist dashboard is your command center for managing your
                tokenized community. Here&quot;s what each section includes:
              </p>
            </div>

            <div>
              <h3 className="mb-4 text-xl font-semibold">
                📊 Token Performance
              </h3>
              <div className="mb-4 rounded-lg bg-gray-50 p-4">
                <p className="mb-3 text-sm text-gray-600">
                  Track your token&quot;s market performance in real-time:
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>
                    • <strong>Current Price:</strong> Live token price with 24h
                    change
                  </li>
                  <li>
                    • <strong>Market Cap:</strong> Total value of all
                    circulating tokens
                  </li>
                  <li>
                    • <strong>Trading Volume:</strong> 24h trading activity
                  </li>
                  <li>
                    • <strong>Price Charts:</strong> 1D, 7D, 1M, and all-time
                    price history
                  </li>
                  <li>
                    • <strong>Liquidity Pool:</strong> Total liquidity and your
                    share
                  </li>
                </ul>
              </div>
            </div>

            <div>
              <h3 className="mb-4 text-xl font-semibold">
                👥 Fan Holders List
              </h3>
              <div className="mb-4 rounded-lg bg-gray-50 p-4">
                <p className="mb-3 text-sm text-gray-600">
                  See who&quot;s supporting you and how:
                </p>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>
                    • <strong>Total Holders:</strong> Number of unique token
                    holders
                  </li>
                  <li>
                    • <strong>Top Holders:</strong> Biggest supporters and their
                    holdings
                  </li>
                  <li>
                    • <strong>New Fans:</strong> Recent token purchases and
                    holders
                  </li>
                  <li>
                    • <strong>Holder Distribution:</strong> How tokens are
                    spread across your community
                  </li>
                  <li>
                    • <strong>Engagement Levels:</strong> Which fans access
                    content most frequently
                  </li>
                </ul>
              </div>
            </div>

            <div className="rounded-lg border border-blue-200 bg-blue-50 p-6">
              <h3 className="mb-2 font-semibold text-blue-700">
                Dashboard Tips
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>
                  • Check your dashboard daily to stay connected with your
                  community
                </li>
                <li>• Use analytics to plan your content strategy</li>
                <li>• Engage with top holders to build loyalty</li>
                <li>• Monitor token price trends to time content releases</li>
              </ul>
            </div>
          </div>
        ),
      },
    },
  };

  const filteredTopics = helpTopics.filter(
    (topic) =>
      topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      topic.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      topic.articles.some((article) =>
        article.title.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
  );

  // Article View
  if (category && slug && articleContent[category]?.[slug]) {
    const article = articleContent[category][slug];
    const categoryInfo = helpTopics.find((t) => t.id === category);

    return (
      <div className="min-h-screen bg-gray-50">
        <div className="border-b bg-white pb-5 shadow-sm">
          <div className="mx-auto px-4 py-4 sm:px-7">
            <button className="mb-4 inline-flex items-center text-gray-600 transition-colors hover:text-gray-900">
              <span
                className="flex cursor-pointer items-center gap-1 hover:!text-primary hover:!underline"
                onClick={navigateBack}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to
              </span>{' '}
              / Help Center
            </button>
            <div className="mb-2 text-sm text-gray-500">
              Help Center / {categoryInfo?.title}
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              {article.title}
            </h1>
          </div>
        </div>

        <div className="mx-auto max-w-[1000px] px-4 py-8">
          <div className="rounded-lg border !bg-primary/10 p-8 shadow-sm">
            <div className="max-w-none">
              {article.content}

              <div className="mt-12 rounded-lg border border-blue-200 bg-blue-50 p-6">
                <h3 className="mb-3 text-lg font-semibold text-blue-700">
                  Need More Help?
                </h3>
                <p className="leading-relaxed text-blue-600">
                  Our support team is here to help you succeed. Reach out via
                  live chat, email, or phone if you have any questions about
                  this topic or need personalized assistance with your TEMOC
                  platform journey.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Main Help Center View
  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <section className="bg-gradient-to-b from-white to-gray-50 px-4 pb-16 pt-24">
        <div className="container mx-auto max-w-4xl text-center">
          <h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-5xl">
            How can we help you?
          </h1>
          <p className="mb-8 text-lg text-gray-600">
            Get answers to your questions and learn how to make the most of
            TEMOC
          </p>

          {/* Search Bar */}
          <div className="relative mx-auto max-w-2xl">
            <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 transform text-gray-400" />
            <input
              type="text"
              placeholder="Search for help articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full rounded-lg border-2 border-gray-200 bg-white py-4 pl-12 pr-4 text-lg focus:border-blue-500 focus:outline-none"
            />
          </div>
        </div>
      </section>

      {/* Help Topics Grid */}
      <section className="px-4 py-16">
        <div className="container mx-auto">
          <h2 className="mb-12 text-center !font-display !text-3xl !font-semibold text-gray-900">
            Popular Help Topics
          </h2>

          <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {filteredTopics.map((topic, index) => {
              const IconComponent = topic.icon;
              return (
                <div
                  key={index}
                  className="rounded-lg border border-gray-200 bg-white transition-all duration-300 hover:border-blue-300 hover:bg-primary/10 hover:shadow-lg"
                >
                  <div className="p-6">
                    {/* Icon */}
                    <div
                      className={`h-12 w-12 ${topic.bgColor} mb-4 flex items-center justify-center rounded-lg`}
                    >
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>

                    {/* Title and Description */}
                    <h3 className="mb-3 text-xl font-semibold text-gray-900">
                      {topic.title}
                    </h3>
                    <p className="mb-6 text-sm leading-relaxed text-gray-600">
                      {topic.description}
                    </p>

                    {/* Articles List */}
                    <div className="space-y-2">
                      {topic.articles.map((article, articleIndex) => (
                        <button
                          key={articleIndex}
                          onClick={() =>
                            navigateToArticle(topic.id, article.slug)
                          }
                          className="group flex w-full items-center justify-between py-1 text-left text-sm text-gray-600 transition-colors hover:text-blue-600"
                        >
                          <span className="transition-transform group-hover:translate-x-1">
                            {article.title}
                          </span>
                          <ChevronRight className="h-4 w-4 opacity-0 transition-opacity group-hover:opacity-100" />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Support Section */}
      <section className="bg-gray-50 px-4 py-16">
        <div className="container mx-auto max-w-4xl">
          <h2 className="mb-12 text-center !font-display !text-3xl !font-semibold text-gray-900">
            Still need help?
          </h2>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {/* Live Chat */}
            <div className="rounded-lg border border-gray-200 bg-white transition-colors hover:border-blue-300">
              <div className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-500">
                  <MessageCircle className="h-6 w-6 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  Live Chat
                </h3>
                <p className="mb-4 text-sm text-gray-600">
                  Chat instantly with support
                </p>
                <button className="w-full rounded-md bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600">
                  Start Chat
                </button>
              </div>
            </div>

            {/* Phone Support */}
            <div className="rounded-lg border border-gray-200 bg-white transition-colors hover:border-blue-300">
              <div className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-500">
                  <Phone className="h-6 w-6 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  Phone Support
                </h3>
                <p className="mb-4 text-sm text-gray-600">+92 300 1234567</p>
                <button className="w-full rounded-md bg-green-500 px-4 py-2 text-white transition-colors hover:bg-green-600">
                  Call Now
                </button>
              </div>
            </div>

            {/* Email Support */}
            <div className="rounded-lg border border-gray-200 bg-white transition-colors hover:border-blue-300">
              <div className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-orange-500">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <h3 className="mb-2 text-lg font-semibold text-gray-900">
                  Email Support
                </h3>
                <p className="mb-4 text-sm text-gray-600"><EMAIL></p>
                <button className="w-full rounded-md bg-orange-500 px-4 py-2 text-white transition-colors hover:bg-orange-600">
                  Send Email
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HelpPage;
