'use client';
import Upload from '@/components/common/Icons/Upload';
import React, { useRef, useState } from 'react';

import { MdOutlineEdit } from 'react-icons/md';
import 'react-toastify/dist/ReactToastify.css';
// import { PiLinkSimple } from 'react-icons/pi';
// import { Button } from '@/components/common';
// import SocialLinks from '@/components/ui/SocialLinks';
import {
  useUploadCoverPicture,
  useUploadProfilePicture,
} from '@/hooks/useUploadProfilePicture';
// import { useCopyToClipboard } from '@/lib/functions';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import { UserRes } from '@/types/user.interface';
import ArtistInfo from './ArtistInfo';
import InfoSkeleton from '@/components/ui/Skelton/InfoSkelton';
import Loader from '@/components/common/Loader';
import Image from 'next/image';
import UpdateSocialMedia from '@/components/ui/UpdateSocialMedia';
const InfoEdit = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const coverInputRef = useRef<HTMLInputElement>(null);
  const [previewProfilePhoto, setPreviewProfilePhoto] = useState<string | null>(
    null,
  );
  const [previewCoverPhoto, setPreviewCoverPhoto] = useState<string | null>(
    null,
  );

  const { data, isLoading } = useQuery<UserRes>({
    queryKey: ['profile-fan'],
    queryFn: () => userService?.getUsersProfile(),
  });

  const fanData = data?.data;

  const imagePreview =
    fanData?.role == 'artist'
      ? fanData?.artistProfile?.profilePic
      : fanData?.avatarUrl;
  const coverPreview =
    fanData?.role == 'artist'
      ? fanData?.artistProfile?.coverPhoto
      : fanData?.coverPicture;
  const fullName =
    fanData?.firstName && fanData?.lastName
      ? `${fanData.firstName} ${fanData.lastName}`
      : 'Andy Haskin';

  const hasCover = Boolean(coverPreview);

  const handleClick = () => fileInputRef.current?.click();
  const handleCoverClick = () => coverInputRef.current?.click();

  const uploadProfilePictureMutation = useUploadProfilePicture();
  const uploadCoverPictureMutation = useUploadCoverPicture();

  // const copyToClipboard = useCopyToClipboard();
  // const profileLink = 'temoc.com/brunomars';
  // const handleCopy = () => {
  //   copyToClipboard(profileLink);
  // };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    await uploadProfilePictureMutation.mutateAsync(file!);
    const previewUrl = URL.createObjectURL(file!);
    setPreviewProfilePhoto(previewUrl);
  };

  const handleCoverImageChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;
    await uploadCoverPictureMutation.mutateAsync(file!);
    const previewUrl = URL.createObjectURL(file!);
    setPreviewCoverPhoto(previewUrl);
  };

  // const [showModal, setShowModal] = useState(false);
  // const Artist = fanData?.role == 'artist';
  return (
    <>
      {fanData?.role === 'artist' ? (
        <ArtistInfo />
      ) : (
        <>
          {isLoading || uploadCoverPictureMutation.isPending ? (
            <div className="h-[300px]">
              <InfoSkeleton />
            </div>
          ) : (
            <div
              className="relative h-[300px] bg-cover px-4 py-5 sm:px-8"
              style={{
                backgroundImage:
                  previewCoverPhoto || coverPreview
                    ? `url(${previewCoverPhoto || coverPreview})`
                    : 'url(/assets/images/admin/info-upload.png)',
              }}
            >
              <div className="flex h-full w-full items-center justify-between gap-4 sm:items-end">
                <div className="flex items-center gap-3">
                  <div className="relative w-[100px] sm:w-[160px]">
                    <input
                      type="file"
                      accept="image/*"
                      ref={fileInputRef}
                      onChange={handleImageChange}
                      className="hidden"
                    />
                    <div
                      onClick={
                        !imagePreview && fanData?.role !== 'artist'
                          ? handleClick
                          : undefined
                      }
                      className={`${fanData?.role !== 'artist' ? 'cursor-pointer' : 'cursor-default'} `}
                    >
                      <div className="relative flex h-[100px] w-[100px] flex-col items-center justify-center rounded-full bg-white sm:h-[160px] sm:w-[160px]">
                        {uploadProfilePictureMutation.isPending ? (
                          <Loader />
                        ) : previewProfilePhoto || imagePreview ? (
                          <>
                            <Image
                              src={
                                previewProfilePhoto ||
                                imagePreview ||
                                '/assets/images/admin/info-upload.png'
                              }
                              alt="Profile Preview"
                              width={160}
                              height={160}
                              className="block h-[100px] w-[100px] rounded-full object-cover sm:h-[160px] sm:w-[160px]"
                            />
                            {/* Edit Icon on top of uploaded image */}
                            {fanData?.role !== 'artist' && (
                              <button
                                onClick={handleClick}
                                className="absolute bottom-1 right-2 z-10 rounded-full bg-white p-[2px] shadow-md transition hover:scale-105 sm:bottom-6 sm:right-6 sm:p-1"
                                title="Edit Profile Image"
                              >
                                <MdOutlineEdit className="text-sm text-primary sm:text-base" />
                              </button>
                            )}
                          </>
                        ) : (
                          <>
                            <Upload className="h-[20px] w-[20px] sm:h-auto sm:w-auto" />
                            <p className="mt-2 text-center text-[8px] leading-none text-[#666666] sm:!text-xs xs:!text-[10px]">
                              Upload Profile Image
                            </p>
                            <p className="text-center !text-[8px] leading-none text-[#666666]">
                              900px by 900px Recommended
                            </p>
                          </>
                        )}
                      </div>
                    </div>
                    <UpdateSocialMedia ArtistData={fanData} />
                  </div>

                  <div>
                    <h3
                      className={`max-w-[150px] truncate text-xl font-semibold capitalize sm:max-w-[400px] sm:text-[30px] ${
                        hasCover ? 'text-white' : 'text-[#333333]'
                      }`}
                    >
                      {fullName}
                    </h3>
                    <div className="mt-2 xs:max-w-[100px]">
                      <p
                        className={`line-clamp-3 max-w-[100px] text-xs font-medium sm:max-w-[256px] ${
                          hasCover ? 'text-white' : 'text-[#333333]'
                        }`}
                      >
                        {fanData?.role == 'fan'
                          ? fanData?.description || ''
                          : fanData?.artistProfile.bio || ''}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Cover Upload Section */}
                <div className="flex flex-col items-center justify-center gap-2">
                  <div className="relative w-[80px]">
                    <input
                      type="file"
                      accept="image/*"
                      ref={coverInputRef}
                      onChange={handleCoverImageChange}
                      className="hidden"
                    />
                    {!coverPreview && (
                      <div
                        onClick={
                          fanData?.role !== 'artist'
                            ? handleCoverClick
                            : undefined
                        }
                        className={
                          fanData?.role !== 'artist' ? 'cursor-pointer' : ''
                        }
                      >
                        <div className="flex h-[60px] w-[60px] flex-col items-center justify-center overflow-hidden rounded-xl bg-white sm:h-[80px] sm:w-[80px]">
                          <Upload className="h-[20px] w-[20px] sm:h-auto sm:w-auto" />
                          <p className="mt-2 text-center !text-[8px] leading-none text-[#666666] sm:!text-xs">
                            Set Cover
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                  {!coverPreview && (
                    <p
                      className={`text-[8px] sm:text-xs ${
                        hasCover ? 'text-white' : 'text-[#333333]'
                      }`}
                    >
                      1300px by 300px Recommended
                    </p>
                  )}
                </div>
              </div>
              {coverPreview && fanData?.role !== 'artist' && (
                <button
                  onClick={handleCoverClick}
                  className="absolute bottom-1 right-2 z-10 rounded-full bg-white p-[2px] shadow-md transition hover:scale-105 sm:bottom-6 sm:right-6 sm:p-1"
                  title="Edit Profile Image"
                >
                  <MdOutlineEdit className="text-sm text-primary sm:text-base" />
                </button>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default InfoEdit;
