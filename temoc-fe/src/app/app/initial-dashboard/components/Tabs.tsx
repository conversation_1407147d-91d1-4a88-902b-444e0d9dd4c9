'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import About from '../../about';
import LibraryGallery from '../../library/components/LibraryGallery';
import Holdings from '../../profile/components/Holdings';

const Tabs: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get tab and afterCreateToken from URL
  const urlTab = searchParams.get('tab') || 'About';
  const urlAfterCreateToken = searchParams.get('afterCreateToken') === 'true';
  const [activeTab, setActiveTab] = useState<string>(urlTab);
  const [afterCreateToken, setAfterCreateToken] =
    useState<boolean>(urlAfterCreateToken);

  // Keep state in sync with URL
  useEffect(() => {
    setActiveTab(urlTab);
    setAfterCreateToken(urlAfterCreateToken);
  }, [urlTab, urlAfterCreateToken]);

  const handleTabChange = (tabName: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', tabName);
    router.push(`?${params.toString()}`);
  };

  // Dynamically update tabs if afterCreateToken is true
  const tabsData = useMemo(() => {
    const baseTabs = [
      { name: 'About' },
      { name: 'Wishlist' },
      { name: 'Holdings' },
    ];
    return baseTabs;
  }, [afterCreateToken]);

  return (
    <>
      <section className="flex justify-center bg-white py-5">
        <div className="flex w-full justify-center gap-7">
          {tabsData.map((item, index) => {
            const isActive = item.name === activeTab;
            return (
              <div
                key={item.name}
                className={`flex cursor-pointer justify-center pr-7 ${
                  index !== tabsData.length - 1
                    ? 'border-r border-[#CECECE]'
                    : ''
                }`}
                onClick={() => handleTabChange(item.name)}
              >
                <p
                  className={`text-sm font-normal leading-none ${
                    isActive
                      ? 'border-b border-primary text-primary'
                      : 'text-[#333333]'
                  }`}
                >
                  {item.name}
                </p>
              </div>
            );
          })}
        </div>
      </section>

      {activeTab === 'About' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <About />
        </div>
      )}
      {activeTab === 'Wishlist' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <LibraryGallery />
        </div>
      )}
      {activeTab === 'Holdings' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] lg:p-12">
          <Holdings />
        </div>
      )}
    </>
  );
};

export default Tabs;
