'use client';
import React, { useState } from 'react';
import { Button, RadioButton } from '@/components/common';
import { Arrow, Copy, Temoc } from '@/components/common/Icons';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

interface pageProps {
  setData: (value: 'add-liquidity') => void;
  setAfterCreateToken: (value: true | false) => void;
}
const PayFee: React.FC<pageProps> = ({ setData, setAfterCreateToken }) => {
  const [selected, setSelected] = useState<string>('faster');

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText('1FfmbHfnpaZjKFvyi1okTjJJusN455paPH');
      toast.success('Copied to clipboard!');
    } catch {
      toast.error('Failed to copy link');
    }
  };

  return (
    <>
      <div className="mx-auto flex w-full max-w-[590px] flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          Pay Fee
        </h3>
        <div className="mt-5 w-full bg-white">
          <div className="p-5">
            <p className="text-sm font-normal text-black-300">
              Pay token creation fee in <span className="font-bold">TEMOC</span>
            </p>
            <div className="mt-5 block items-center justify-between sm:flex">
              <div className="block">
                <p className="text-xs font-normal leading-none text-black-600">
                  Default account information
                </p>
                <p className="flex items-center gap-1 text-sm leading-none text-black-300">
                  1FfmbHfnpaZjKFvyi1okTjJJusN455paPH{' '}
                  <div onClick={copyToClipboard} className="cursor-pointer">
                    <Copy />
                  </div>
                </p>
              </div>
              <div className="mt-3 flex items-center gap-[5px] sm:mt-0">
                <Temoc />{' '}
                <p className="text-base font-normal leading-none text-black-300">
                  5.477306
                </p>
              </div>
            </div>
            <hr className="my-5" />
            <div className="flex items-center justify-between">
              <p className="text-sm leading-none text-black-300">Amount</p>
              <div className="flex items-center gap-[5px]">
                <Temoc />{' '}
                <p className="text-base font-normal leading-none text-primary">
                  1.00433
                </p>
              </div>
            </div>
            <hr className="my-5" />
            <div className="space-y-2.5">
              {/* Faster */}
              <div className="flex items-center justify-between">
                <RadioButton
                  label={
                    <div className="block">
                      <p className="text-base font-normal leading-none text-black-300">
                        Faster
                      </p>
                      <p className="pt-0.5 text-[10px] font-normal leading-none text-black-600">
                        Token activate in 24 Hours
                      </p>
                    </div>
                  }
                  value="faster"
                  name="faster"
                  checked={selected === 'faster'}
                  onChange={setSelected}
                />
                <div className="flex items-center gap-[5px]">
                  <Temoc />{' '}
                  <p className="text-base font-normal leading-none text-black-300">
                    0.00041
                  </p>
                </div>
              </div>
              {/* Normal */}
              <div className="mt-5 flex items-center justify-between">
                <RadioButton
                  label={
                    <div className="block">
                      <p className="text-base font-normal leading-none text-black-300">
                        Normal
                      </p>
                      <p className="pt-0.5 text-[10px] font-normal leading-none text-black-600">
                        Token activate in 5 Days
                      </p>
                    </div>
                  }
                  value="normal"
                  name="normal"
                  checked={selected === 'normal'}
                  onChange={setSelected}
                />
                <div className="flex items-center gap-[5px]">
                  <Temoc />{' '}
                  <p className="text-base font-normal leading-none text-black-300">
                    0.00023
                  </p>
                </div>
              </div>
              {/* Slower */}
              <div className="mt-5 flex items-center justify-between">
                <RadioButton
                  label={
                    <div className="block">
                      <p className="text-base font-normal leading-none text-black-300">
                        Slower
                      </p>
                      <p className="pt-0.5 text-[10px] font-normal leading-none text-black-600">
                        Token activate in 15 Days
                      </p>
                    </div>
                  }
                  value="slower"
                  name="slower"
                  checked={selected === 'slower'}
                  onChange={setSelected}
                />
                <div className="flex items-center gap-[5px]">
                  <Temoc />{' '}
                  <p className="text-base font-normal leading-none text-black-300">
                    0.00010
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setData('add-liquidity')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            onClick={() => setAfterCreateToken(true)}
          >
            Create Your Token
            <Arrow />
          </Button>
        </div>
      </div>
    </>
  );
};

export default PayFee;
