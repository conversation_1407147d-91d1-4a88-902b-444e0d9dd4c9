'use client';
import React, { useState, useEffect, useMemo } from 'react';
import Button from '@/components/common/Button';
import Arrow from '@/components/common/Icons/Arrow';
import { liquidityService } from '@/services/liquidity.service';
import { useQuery } from '@tanstack/react-query';
import Loader from '@/components/common/Loader';
import NoData from '@/components/ui/NoData';
import { copyToClipboard, truncateAddress } from '@/lib/functions';
import axios from 'axios';
import { formatUnits } from 'ethers';
import { Copy } from '@/components/common/Icons';
import Link from 'next/link';

// Type definitions for better type safety
interface LiquidityPosition {
  _id: string;
  tokenAAddress: string;
  tokenBAddress: string;
  amountA: string;
  amountB: string;
  transactionHash: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  userAddress: string;
  poolAddress?: string;
  fee?: number;
  tickLower?: number;
  tickUpper?: number;
  timestamp?: string;
}

interface ProcessedPosition {
  id: string;
  tokenAAddress: string;
  tokenBAddress: string;
  transactionHash: string;
  amountA: number;
  amountB: number;
  amountAInUsd: number;
  amountBInUsd: number;
  createdAt: string;
}

interface PortfolioProps {
  onDetails: (position: ProcessedPosition) => void;
  onRemove: (position: ProcessedPosition) => void;
  setData: any;
  setCreating: React.Dispatch<React.SetStateAction<boolean>>;
  setCurrentStep: React.Dispatch<React.SetStateAction<any>>;
}

export const Portfolio: React.FC<PortfolioProps> = ({
  setCreating,
  setCurrentStep,
  setData,
}) => {
  const [pricesLoading, setPricesLoading] = useState<boolean>(true);

  const [tokenPrices, setTokenPrices] = useState<{ [key: string]: number }>({
    USDC: 1,
    ETH: 0,
  });

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['liquidity'],
    queryFn: () => liquidityService.getAllLiquidity(),
  });

  const tokenData: LiquidityPosition[] = data?.data || [];

  // Combined price fetching effect with error handling
  useEffect(() => {
    let isMounted = true;

    async function fetchEthPrice() {
      try {
        const res = await axios.get(
          'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
          { timeout: 10000 }, // 10 second timeout
        );

        if (isMounted) {
          const price = res.data.ethereum.usd;
          setTokenPrices((prev) => ({ ...prev, ETH: price }));
          setPricesLoading(false);
        }
      } catch (error) {
        console.error('Failed to fetch ETH price:', error);
        if (isMounted) {
          setTokenPrices((prev) => ({ ...prev, ETH: 0 }));
          setPricesLoading(false);
        }
      }
    }

    fetchEthPrice();
    const interval = setInterval(fetchEthPrice, 60000); // refresh every 60s

    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, []);

  // Format large token amounts properly
  const formatTokenAmount = (amount: string, decimals: number = 18): number => {
    try {
      const formatted = formatUnits(amount, decimals);
      return parseFloat(formatted);
    } catch (error) {
      console.error('Error formatting token amount:', error);
      return 0;
    }
  };

  // Memoize expensive calculations
  const liquidityPositions = useMemo((): ProcessedPosition[] => {
    if (!tokenData || tokenData.length === 0) return [];

    return tokenData.map((position: LiquidityPosition) => {
      // Convert raw amounts to human-readable values consistently
      const amountAReadable = formatTokenAmount(position.amountA, 18);
      const amountBReadable = formatTokenAmount(position.amountB, 18);
      const amountAInUsd = amountAReadable * tokenPrices.ETH;
      const amountBInUsd = amountBReadable * tokenPrices.USDC;

      return {
        id: position._id,
        tokenAAddress: position.tokenAAddress,
        tokenBAddress: position.tokenBAddress,
        transactionHash: position.transactionHash,
        amountA: amountAReadable,
        amountB: amountBReadable,
        amountAInUsd,
        amountBInUsd,
        createdAt: position.createdAt,
      };
    });
  }, [tokenData, tokenPrices]);

  // Enhanced currency formatting with fallback
  const formatCurrency = (amount: number): string => {
    if (isNaN(amount) || amount === null || amount === undefined) {
      return '$0.00';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  // Format large numbers with K, M, B suffixes
  const formatLargeNumber = (num: number): string => {
    if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
    return num.toLocaleString(undefined, {
      maximumFractionDigits: 6,
      minimumFractionDigits: 0,
    });
  };

  if (isLoading) {
    return (
      <div className="px-6 py-10 text-center">
        <Loader />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-6 text-center text-red-500">
        <p>Error fetching tokens. Please try again later.</p>
        <Button onClick={() => refetch()} className="mt-4">
          Retry
        </Button>
      </div>
    );
  }

  if (!tokenData || tokenData.length === 0) {
    return (
      <NoData
        heading="No Liquidity Yet. Create Your Liquidity"
        description="No liquidity has been added yet. Add liquidity to activate your token — once live, fans can access your albums by purchasing the token."
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="mb-4 flex items-center justify-between">
        <Button
          className="flex !gap-4 !border-none !bg-black-900 !py-3 uppercase sm:!px-[40px]"
          onClick={() => {
            setData('select-chain');
            setCreating(false);
            setCurrentStep('choose');
          }}
        >
          <Arrow className="rotate-180" />
          Back
        </Button>
      </div>

      {/* Price Status Indicator */}
      {pricesLoading && (
        <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 text-center">
          <p className="text-sm text-blue-600">Loading current prices...</p>
        </div>
      )}

      {/* Portfolio Overview */}
      <div className="rounded-lg border bg-white p-6 shadow-lg">
        <h3 className="mb-4 text-xl font-semibold text-gray-800">
          Portfolio Overview
        </h3>

        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Positions</p>
            <p className="text-2xl font-bold text-gray-800">
              {liquidityPositions.length}
            </p>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">Total Value (USD)</p>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(
                liquidityPositions.reduce(
                  (sum, pos) => sum + pos.amountAInUsd + pos.amountBInUsd,
                  0,
                ),
              )}
            </p>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">Status</p>
            <p className="text-2xl font-bold text-blue-600">Active</p>
          </div>
        </div>
      </div>

      {/* Liquidity Positions */}
      <div className="rounded-lg border bg-white text-black shadow-lg">
        <div className="border-b bg-gray-100 p-6">
          <h3 className="text-xl font-semibold text-gray-800">
            Your Liquidity Positions
          </h3>
          <p className="text-sm text-gray-600">
            Manage your active liquidity pools and track performance.
          </p>
        </div>

        <div className="space-y-6 p-6">
          {liquidityPositions.map((position: ProcessedPosition) => (
            <div
              key={position.id}
              className="rounded-lg border border-gray-200 bg-gray-50 shadow-sm transition-all hover:shadow-md"
            >
              <div className="p-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-3 lg:grid-cols-5">
                  <div className="space-y-2">
                    <h4 className="text-lg font-semibold text-gray-800">
                      Token A Address
                    </h4>
                    <div className="flex items-center gap-4">
                      <p className="font-mono text-sm text-gray-600">
                        {truncateAddress(position.tokenAAddress)}
                      </p>
                      <div
                        className="cursor-pointer hover:text-primary"
                        onClick={() => copyToClipboard(position.tokenAAddress)}
                      >
                        <Copy />
                      </div>
                    </div>
                    <div className="flex w-[60px] items-center justify-center rounded-full bg-green-200 px-2 py-1 text-xs text-green-800">
                      Active
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-lg font-semibold text-gray-800">
                      Token B Address
                    </h4>
                    <div className="flex items-center gap-4">
                      <p className="font-mono text-sm text-gray-600">
                        {truncateAddress(position.tokenBAddress)}
                      </p>
                      <div
                        className="cursor-pointer hover:text-primary"
                        onClick={() => copyToClipboard(position.tokenBAddress)}
                      >
                        <Copy />
                      </div>
                    </div>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-semibold text-gray-800">
                      Amount A
                    </p>
                    <p className="text-lg font-semibold">
                      {formatLargeNumber(position.amountA)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(position.amountAInUsd)}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-semibold text-gray-800">
                      Amount B
                    </p>
                    <p className="text-lg font-semibold">
                      {formatLargeNumber(position.amountB)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(position.amountBInUsd)}
                    </p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-semibold text-gray-800">
                      Transaction Hash
                    </p>
                    <Link
                      href={`https://sepolia.etherscan.io/tx/${position.transactionHash}`}
                      className="truncate font-mono text-sm text-primary"
                      target="_blank"
                    >
                      {truncateAddress(position.transactionHash)}
                    </Link>
                  </div>

                  {/* <div className="flex flex-col items-center space-y-3">
                    <Button
                      variant="outline"
                      disabled
                      className="flex !h-[36px] !w-[140px] items-center space-x-1 !rounded-md !text-xs"
                      onClick={() => onDetails(position)}
                    >
                      <Eye className="h-4 w-4" />
                      <span>Details</span>
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => onRemove(position)}
                      disabled
                      className="flex !h-[36px] !w-[140px] items-center space-x-1 !rounded-md !text-xs text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                      <span>Remove</span>
                    </Button>
                  </div> */}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
