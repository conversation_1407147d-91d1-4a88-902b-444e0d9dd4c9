import React from 'react';
// import <PERSON><PERSON><PERSON><PERSON> from './SelectChain';
import WalletConnect from './WalletConnect';
import AddLiquidity from './AddLiquidity';
import PayFee from './PayFee';

interface pageProps {
  data: any;
  setData: (
    value: 'select-chain' | 'wallet-connect' | 'add-liquidity' | 'pay-fee',
  ) => void;
  setAfterCreateToken: (value: true | false) => void;
  onBack: () => void;
}

const AddLiquidityFlow: React.FC<pageProps> = ({
  data,
  setData,
  setAfterCreateToken,
  // onBack,
}) => {
  return (
    <>
      {/* {data === 'select-chain' && (
        <div className="mx-auto max-w-[690px]">
          <SelectChain setData={setData} onBack={onBack} />
        </div>
      )}  */}
      {data === 'wallet-connect' && (
        <div className="mx-auto max-w-[690px]">
          <WalletConnect setData={setData} />
        </div>
      )}
      {data === 'add-liquidity' && (
        <div className="mx-auto max-w-[690px]">
          <AddLiquidity setData={setData} />
        </div>
      )}
      {data === 'pay-fee' && (
        <div className="mx-auto max-w-[690px]">
          <PayFee setData={setData} setAfterCreateToken={setAfterCreateToken} />
        </div>
      )}
    </>
  );
};

export default AddLiquidityFlow;
