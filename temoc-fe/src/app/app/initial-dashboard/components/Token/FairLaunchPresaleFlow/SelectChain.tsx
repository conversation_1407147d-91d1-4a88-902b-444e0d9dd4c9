import React, { useState } from 'react';
import { Button, RadioButton } from '@/components/common';
import { Arrow, Base, Ethereum, Solana, Ton } from '@/components/common/Icons';

interface pageProps {
  setDataFair: (value: 'select-chain' | 'select-chain-wallet') => void;
  onBack: () => void;
}
const SelectChain: React.FC<pageProps> = ({ setDataFair, onBack }) => {
  const [selected, setSelected] = useState<string>('ethereum');

  return (
    <>
      <div className="mx-auto flex flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          Select Chain
        </h3>
        <div className="mt-5 bg-white">
          <div className="p-5 md:w-[690px]">
            <p className="text-sm font-normal text-black-300">Chains</p>
            <div className="mt-5 flex flex-wrap items-center gap-6 md:flex-nowrap">
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Ethereum /> Ethereum
                  </p>
                }
                value="ethereum"
                name="ethereum"
                checked={selected === 'ethereum'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Base /> Base
                  </p>
                }
                value="base"
                name="base"
                checked={selected === 'base'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Solana /> Solana
                  </p>
                }
                value="solana"
                name="solana"
                checked={selected === 'solana'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Ton /> TON
                  </p>
                }
                value="ton"
                name="ton"
                checked={selected === 'ton'}
                onChange={setSelected}
              />
            </div>
          </div>
        </div>
        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={onBack}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            onClick={() => setDataFair('select-chain-wallet')}
          >
            Connect Wallet
            <Arrow />
          </Button>
        </div>
      </div>
    </>
  );
};

export default SelectChain;
