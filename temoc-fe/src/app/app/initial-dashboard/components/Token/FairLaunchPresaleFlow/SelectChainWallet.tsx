import React, { useState, useRef } from 'react';
import { Button, RadioButton } from '@/components/common';
import { Arrow, Base, Ethereum, Solana, Ton } from '@/components/common/Icons';
import Upload from '@/components/common/Icons/Upload';
import { Input } from '@/components/common/Forms/Input';
import { dataFairType } from '..';

interface pageProps {
  setDataFair: (value: dataFairType) => void;
}
const SelectChainWallet: React.FC<pageProps> = ({ setDataFair }) => {
  const [selected, setSelected] = useState<string>('ethereum');
  const [currency, setCurrency] = useState<string>('eth');
  const [listingOptions, setListiongOptions] =
    useState<string>('manual-listing');
  const [poolType, setPoolType] = useState<string>('presale');
  const [affiliate, setAffiliate] = useState<string>('disable-affiliate');
  const [fileName, setFileName] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFileName(file.name);
    }
  };

  return (
    <>
      <div className="mx-auto flex flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          Select Chain
        </h3>
        <div className="mt-5 bg-white">
          <div className="p-5 md:w-[690px]">
            <p className="text-sm font-normal text-black-300">Chains</p>
            <div className="mt-5 flex flex-wrap items-center gap-6 md:flex-wrap">
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Ethereum /> Ethereum
                  </p>
                }
                value="ethereum"
                name="ethereum"
                checked={selected === 'ethereum'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Base /> Base
                  </p>
                }
                value="base"
                name="base"
                checked={selected === 'base'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Solana /> Solana
                  </p>
                }
                value="solana"
                name="solana"
                checked={selected === 'solana'}
                onChange={setSelected}
              />
              <RadioButton
                label={
                  <p className="flex items-center gap-1">
                    <Ton /> TON
                  </p>
                }
                value="ton"
                name="ton"
                checked={selected === 'ton'}
                onChange={setSelected}
              />
            </div>
          </div>
        </div>
        <div className="mt-5 bg-white p-5 md:w-[690px]">
          {/* Upload Photo */}
          <label className="flex w-[200px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
            <h3 className="bd max-w-[170px] truncate text-center text-xs text-[#777777]">
              Add Logo (optional)
            </h3>
            <Upload />
            <div>
              <p className="text-center text-xs text-[#666666]">
                {fileName || 'Upload'}
              </p>
              <p className="-mt-1 text-center text-[8px] text-[#666666]">
                900px by 900px Recommended
              </p>
            </div>
            <input
              type="file"
              accept="image/*"
              ref={inputRef}
              onChange={handleFileChange}
              className="hidden"
            />
          </label>
          <Input
            placeholder="Token Address"
            name="token-address"
            className="mt-5"
          />
          <p className="mt-2.5 text-[10px] leading-none text-[#777777]">
            Creation Fee: 0.2 ETH. (Balance: 100 ETH)
          </p>
          <p className="mt-5 text-sm leading-none text-black-300">
            Currency{' '}
            <span className="text-xs text-black-600">
              Users will pay with ETH for your token
            </span>
          </p>
          <div className="mt-2.5 flex items-center gap-6">
            <RadioButton
              label="ETH"
              value="eth"
              name="eth"
              checked={currency === 'eth'}
              onChange={setCurrency}
            />
            <RadioButton
              label="USDT"
              value="usdt"
              name="usdt"
              checked={currency === 'usdt'}
              onChange={setCurrency}
            />
            <RadioButton
              label="USDC"
              value="usdc"
              name="usdc"
              checked={currency === 'usdc'}
              onChange={setCurrency}
            />
          </div>
          <p className="mt-5 text-sm leading-none text-black-300">
            Free options
          </p>
          <div className="mt-2.5 flex items-center gap-6">
            <RadioButton
              label="5% ETH raised only"
              value="eth-raised-only"
              name="eth-raised-only"
              checked
            />
          </div>
          <p className="mt-5 text-sm leading-none text-black-300">
            Listing options
          </p>
          <div className="mt-2.5 flex items-center gap-6">
            <RadioButton
              label="Auto Lisitng"
              value="auto-listing"
              name="auto-listing"
              checked={listingOptions === 'auto-listing'}
              onChange={setListiongOptions}
            />
            <RadioButton
              label={
                <p className="flex items-end gap-0.5 text-xs leading-none sm:text-sm">
                  Manual Listing{' '}
                  <span className="text-xs text-black-600">
                    (Recommended for Seed/Private Sale)
                  </span>
                </p>
              }
              value="manual-listing"
              name="manual-listing"
              checked={listingOptions === 'manual-listing'}
              onChange={setListiongOptions}
            />
          </div>

          {/* Choose Pool Type */}
          <p className="mt-5 text-sm leading-none text-black-300">
            Choose a pool type
          </p>
          <div className="mt-2.5 flex items-center gap-6">
            <RadioButton
              label="Presale"
              value="presale"
              name="presale"
              checked={poolType === 'presale'}
              onChange={setPoolType}
            />
            <RadioButton
              label="Private Sale"
              value="private-sale"
              name="private-sale"
              checked={poolType === 'private-sale'}
              onChange={setPoolType}
            />
            <RadioButton
              label="Seel Sale"
              value="seed-sale"
              name="seed-sale"
              checked={poolType === 'seed-sale'}
              onChange={setPoolType}
            />
          </div>
          {/* Affiliate Program */}
          <p className="mt-5 text-sm leading-none text-black-300">
            Affiliate Program
          </p>
          <div className="mt-2.5 flex items-center gap-6">
            <RadioButton
              label="Disable Affiliate"
              value="disable-affiliate"
              name="disable-ffiliate"
              checked={affiliate === 'disable-affiliate'}
              onChange={setAffiliate}
            />
            <RadioButton
              label="Enable Affiliate"
              value="enable-affiliate"
              name="enable-affiliate"
              checked={affiliate === 'enable-affiliate'}
              onChange={setAffiliate}
            />
          </div>
        </div>
        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setDataFair('select-chain')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            onClick={() => setDataFair('fairlaunch-information')}
          >
            Next Step
            <Arrow />
          </Button>
        </div>
      </div>
    </>
  );
};

export default SelectChainWallet;
