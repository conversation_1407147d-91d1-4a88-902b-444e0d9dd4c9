import SelectComponent from '@/components/common/Forms/Select';
import React, { Fragment, useState } from 'react';
import { dataFairType } from '..';
import { Arrow, InfoCircle } from '@/components/common/Icons';
import { Input } from '@/components/common/Forms/Input';
import { Button } from '@/components/common';

const intervalOptions = [
  { value: '5min', label: '5 Minutes' },
  { value: '10min', label: '10 Minutes' },
  { value: '1hr', label: '1 Hour' },
  { value: '1day', label: '1 Day' },
];

interface pageProps {
  setDataFair: (value: dataFairType) => void;
  setAfterCreateToken: (value: true | false) => void;
}

const Timing: React.FC<pageProps> = ({ setDataFair, setAfterCreateToken }) => {
  const [startInterval, setStartInterval] = useState<{
    value: string;
    label: string;
  } | null>(null);

  const [endInterval, setEndInterval] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const [unlockInterval, setUnlockInterval] = useState<{
    value: string;
    label: string;
  } | null>(null);

  const getTodayAtMidnight = (): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // month is 0-based
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}T00:00`;
  };

  const [presaleStart, setPresaleStart] = useState(getTodayAtMidnight());
  const [presaleEnd, setPresaleEnd] = useState(getTodayAtMidnight());

  return (
    <>
      <div className="mx-auto flex flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          FAIR LAUNCH timings
        </h3>
        <div className="mt-5 w-full bg-white p-5 md:w-[690px]">
          <div className="flex flex-col gap-[15px]">
            <p className="text-sm leading-none text-black-300">
              Presale Start Time
            </p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Input
                name="presaleStart"
                type="datetime-local"
                placeholder="Presale Start Time"
                className="!font-normal"
                // focusedLabelClass="!top-4"
                value={presaleStart}
                onChange={(e) => setPresaleStart(e.target.value)}
              />
              <SelectComponent
                options={intervalOptions}
                selected={startInterval}
                onSelect={setStartInterval}
                placeholder={
                  <div className="flex items-center gap-1">
                    Adjust Time <InfoCircle />
                  </div>
                }
              />
            </div>
            <p className="text-sm leading-none text-black-300">
              Presale End Time
            </p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Input
                name="presaleEnd"
                type="datetime-local"
                placeholder="Presale End Time"
                className="!font-normal"
                // focusedLabelClass="!top-4"
                value={presaleEnd}
                onChange={(e) => setPresaleEnd(e.target.value)}
              />
              <SelectComponent
                options={intervalOptions}
                selected={endInterval}
                onSelect={setEndInterval}
                placeholder={
                  <div className="flex items-center gap-1">
                    Adjust Time <InfoCircle />
                  </div>
                }
              />
            </div>
            <p className="text-sm leading-none text-black-300">
              Liquidity Unlock Time
            </p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <Input
                name="liquidityUnlock"
                type="datetime-local"
                placeholder="Liquidity Unlock Time"
                className="!font-normal"
                // focusedLabelClass="!top-4"
                value={presaleEnd}
                onChange={(e) => setPresaleEnd(e.target.value)}
              />
              <SelectComponent
                options={intervalOptions}
                selected={unlockInterval}
                onSelect={setUnlockInterval}
                placeholder={
                  <div className="flex items-center gap-1">
                    Adjust Time <InfoCircle />
                  </div>
                }
              />
            </div>
          </div>
        </div>

        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setDataFair('fairlaunch-information')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            onClick={() => setAfterCreateToken(true)}
          >
            Create Coin
            <Arrow />
          </Button>
        </div>
      </div>
    </>
  );
};

export default Timing;
