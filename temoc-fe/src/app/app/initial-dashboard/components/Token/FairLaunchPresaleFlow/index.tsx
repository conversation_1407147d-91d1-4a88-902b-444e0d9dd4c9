import React from 'react';
import <PERSON><PERSON>hai<PERSON> from './SelectChain';
import <PERSON><PERSON>hainWallet from './SelectChainWallet';
// import Information from './Information';
import { dataFairType } from '..';
import Timing from './Timing';

interface pageProps {
  dataFair: string;
  setDataFair: (value: dataFairType) => void;
  onBack: () => void;
  setAfterCreateToken: (value: true | false) => void;
}

const FairLaunchPresaleFlow: React.FC<pageProps> = ({
  dataFair,
  setDataFair,
  onBack,
  setAfterCreateToken,
}) => {
  return (
    <>
      {dataFair === 'select-chain' && (
        <div className="mx-auto max-w-[690px]">
          <SelectChain setDataFair={setDataFair} onBack={onBack} />
        </div>
      )}
      {dataFair === 'select-chain-wallet' && (
        <div className="mx-auto max-w-[690px]">
          <SelectChainWallet setDataFair={setDataFair} />
        </div>
      )}
      {dataFair === 'fairlaunch-information' && (
        <div className="mx-auto max-w-[690px]">
          {/* <Information setDataFair={setDataFair} /> */}
        </div>
      )}
      {dataFair === 'fairlaunch-timing' && (
        <div className="mx-auto w-full max-w-[690px]">
          <Timing
            setDataFair={setDataFair}
            setAfterCreateToken={setAfterCreateToken}
          />
        </div>
      )}
    </>
  );
};

export default FairLaunchPresaleFlow;
