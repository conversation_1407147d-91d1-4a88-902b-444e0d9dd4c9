'use client';
import React, { useState } from 'react';
import NoData from '@/components/ui/NoData';
import { Button } from '@/components/common';
import { MdArrowOutward } from 'react-icons/md';
import { TokenCreation } from '@/components/common/Icons';
import FairLaunchPresaleFlow from './FairLaunchPresaleFlow';
import AddLiquidityFlow from './AddLiquidityFlow';
import CreateToken from './CreateToken';

type dataType = 'select-chain' | 'wallet-connect' | 'add-liquidity' | 'pay-fee';

type currentStepType = 'choose' | 'liquidity' | 'fair-launch';

export type dataFairType =
  | 'select-chain'
  | 'select-chain-wallet'
  | 'fairlaunch-information'
  | 'fairlaunch-timing';

interface pageProps {
  setAfterCreateToken: (value: true | false) => void;
}

const Token: React.FC<pageProps> = ({ setAfterCreateToken }) => {
  const [creating, setCreating] = useState(false);
  const [data, setData] = useState<dataType>('select-chain');
  const [dataFair, setDataFair] = useState<dataFairType>('select-chain');

  const [currentStep, setCurrentStep] = useState<currentStepType>('choose');

  const handleCreateClick = () => {
    setCreating(true);
    setCurrentStep('choose'); // reset to Choose step
  };

  return (
    <div>
      {!creating ? (
        <div className="flex w-full flex-wrap items-center justify-between gap-3">
          <h3 className="text-xl uppercase text-[#333333] sm:text-[22px]">
            Your Token
          </h3>
          <Button
            className="group !text-xs font-semibold uppercase"
            onClick={handleCreateClick}
          >
            Create a Token
            <MdArrowOutward className="text-sm text-white" />
          </Button>
        </div>
      ) : currentStep === 'choose' ? (
        <CreateToken setCurrentStep={setCurrentStep} />
      ) : currentStep === 'liquidity' ? (
        <AddLiquidityFlow
          setAfterCreateToken={setAfterCreateToken}
          data={data}
          setData={setData}
          onBack={() => setCurrentStep('choose')}
        />
      ) : currentStep === 'fair-launch' ? (
        <FairLaunchPresaleFlow
          setAfterCreateToken={setAfterCreateToken}
          dataFair={dataFair}
          setDataFair={setDataFair}
          onBack={() => setCurrentStep('choose')}
        />
      ) : null}

      {!creating && (
        <NoData
          icon={<TokenCreation className="h-10 w-10" color="#333333" />}
          heading="No Token Yet. Create Your Token"
          description="Get Started by Creating Your Unique Token and Empower Your Fanbase."
        />
      )}
    </div>
  );
};

export default Token;
