'use client';
import { ReactNode, useState } from 'react';
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  TransitionChild,
} from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import FooterNav from './components/FooterNav';

export default function MainLayout({ children }: { children: ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="">
      <Dialog
        open={sidebarOpen}
        onClose={setSidebarOpen}
        className="relative z-50 lg:hidden"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-900/80 transition-opacity duration-300 ease-linear data-[closed]:opacity-0"
        />

        <div className="fixed inset-0 flex">
          <DialogPanel
            transition
            className="relative mr-16 flex w-full flex-1 transform transition duration-300 ease-in-out data-[closed]:-translate-x-full lg:max-w-xs"
          >
            <TransitionChild>
              <div className="absolute left-full top-0 z-50 flex w-16 justify-center pt-5 duration-300 ease-in-out data-[closed]:opacity-0">
                <button
                  type="button"
                  onClick={() => setSidebarOpen(false)}
                  className="-m-2.5 p-2.5"
                >
                  <span className="sr-only">Close sidebar</span>
                  <XMarkIcon
                    aria-hidden="true"
                    className="h-6 w-6 text-white"
                  />
                </button>
              </div>
            </TransitionChild>
            <Sidebar />
          </DialogPanel>
        </div>
      </Dialog>

      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-[230px] lg:flex-col">
        <Sidebar />
      </div>
      <Header setSidebarOpen={setSidebarOpen} />
      <div className="hideScrollbar h-[calc(100vh-104px)] overflow-y-auto px-4 sm:h-[calc(100vh-104px)] sm:px-6 lg:px-0 lg:pl-[245px]">
        {children}
      </div>
      <FooterNav />
    </div>
  );
}
