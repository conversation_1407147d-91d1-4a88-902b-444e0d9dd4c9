'use client';
import React, { useRef, useState } from 'react';
import TokenTable from '@/components/ui/TokenTable';
import { Heart, MessageCircle, Share2 } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';
import { IoPause } from 'react-icons/io5';
import { FaPlay } from 'react-icons/fa6';
import MobileLibraryItem from '@/components/ui/MobileLibrary';

const columns = [
  { key: 'popular', label: 'Popular' },
  { key: 'plays', label: 'Plays' },
  { key: 'album', label: 'Album' },
  { key: 'duration', label: 'Duration' },
  { key: 'share', label: 'Share' },
];
const LibraryGallery = () => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(false);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
      setShowControls(true);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    // Optional: setShowControls(false);
  };
  const Tabledata = [
    {
      popular: (
        <div className="flex flex-col items-center gap-2 sm:flex-row">
          1
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Freak In Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          2
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg2.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Baby One More Time</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          3
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg3.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Show Me How</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          4
          <ImageComponent
            src="/assets/images/trading/tableimg.svg"
            height={48}
            width={48}
          />
          <p className="text-sm">Freak In Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          5
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg4.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Baby Powder</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          6
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg5.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">You & Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          7
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg6.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">That&apos;s what i like</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
          </div>
        </div>
      ),
    },
  ];
  return (
    <div>
      <h3 className="ml-4 mt-8 text-lg text-[#333333] sm:text-[22px]">
        Wishlist
      </h3>
      <div className="hidden sm:block">
        <div className="mt-3">
          <TokenTable columns={columns} data={Tabledata} />
          <audio
            ref={audioRef}
            src="/assets/audio.mp3"
            controls={showControls}
            onEnded={handleEnded}
            className={`mt-4 w-full ${showControls ? 'block' : 'hidden'}`}
          />
        </div>
        <h3 className="ml-4 mt-8 text-lg text-[#333333] sm:text-[22px]">
          Recent Played
        </h3>
        <div className="mt-3">
          <TokenTable columns={columns} data={Tabledata} />
          <audio
            ref={audioRef}
            src="/assets/audio.mp3"
            controls={showControls}
            onEnded={handleEnded}
            className={`mt-4 w-full ${showControls ? 'block' : 'hidden'}`}
          />
        </div>
      </div>
      <div className="mt-3 block sm:hidden">
        {Tabledata.map((song, idx) => (
          <MobileLibraryItem
            key={idx}
            index={idx} // use the map index here
            track={song}
          />
        ))}
      </div>
    </div>
  );
};

export default LibraryGallery;
