'use client';
import React, { useState } from 'react';
import AccessManagement from './AccessManagement';
import UserManagement from './UserManagement';

const Management = () => {
  const [activeTab, setActiveTab] = useState('media');
  return (
    <>
      <div className="bg-white py-5 shadow-[0_4px_10px_rgba(0,0,0,0.08)]">
        <div className="mx-auto flex w-full max-w-[299px] items-center justify-between text-sm">
          <p
            className={`cursor-pointer text-sm font-semibold ${
              activeTab === 'media' ? 'text-primary' : 'text-black'
            }`}
            onClick={() => setActiveTab('media')}
          >
            Access Management
          </p>
          <div className="h-4 w-0.5 border border-black bg-black"></div>
          <p
            className={`cursor-pointer text-sm font-semibold ${
              activeTab === 'comments' ? 'text-primary' : 'text-black'
            }`}
            onClick={() => setActiveTab('comments')}
          >
            User Management
          </p>
        </div>
      </div>
      {activeTab === 'media' && (
        <div>
          <AccessManagement />
        </div>
      )}
      {/* comments div */}
      {activeTab === 'comments' && (
        <div>
          <UserManagement />
        </div>
      )}
    </>
  );
};

export default Management;
