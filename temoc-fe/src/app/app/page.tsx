'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AppPage = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to explore page as default
    router.replace('/app/explore');
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
        <p className="mt-2 text-gray-600">Redirecting...</p>
      </div>
    </div>
  );
};

export default AppPage;
