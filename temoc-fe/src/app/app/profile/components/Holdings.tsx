import { Copy, Ethereum } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';
import Select from '@/components/ui/Select';
import TokenTable from '@/components/ui/TokenTable';
import React, { useState } from 'react';

const collectionOption = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];

const columns = [
  { key: 'number', label: 'Number' },
  { key: 'coin', label: 'Coin' },
  { key: 'day', label: 'Day' },
  { key: 'amount', label: 'Amount' },
  { key: 'token', label: 'Token Address' },
  { key: 'chain', label: 'Chain' },
];
const Tabledata = [
  {
    number: '1',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 1</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Do<PERSON>',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '2',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 2</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '3',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 3</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '4',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 4</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '5',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 5</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '6',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 6</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '7',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 7</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '8',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 8</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '9',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 9</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '10',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 10</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '11',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 11</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '12',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 12</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
  {
    number: '13',
    coin: (
      <div className="flex items-center gap-1">
        <ImageComponent src="/assets/images/coin1.svg" height={30} width={30} />
        <p className="text-base">Coin 13</p>
      </div>
    ),
    day: 'May 10, 2025',
    amount: '200 Jon Doe Token',
    token: (
      <div className="flex items-center gap-1">
        0x93B3aE860a <Copy />
      </div>
    ),
    chain: (
      <div className="flex items-center gap-1">
        <Ethereum />
        Ethereum
      </div>
    ),
  },
];

const Holdings = () => {
  const [selectedHour, setSelectedHour] = useState(collectionOption[0]);
  const visibleColumnKeys = ['day', 'amount', 'token'];

  return (
    <>
      <div className="rounded-[20px] bg-white pt-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
        <div className="mt-2.5 flex flex-wrap gap-2.5 px-7">
          <input
            placeholder="Search by token name"
            className="h-[38px] w-full rounded-xl border-[#CECECE] placeholder:text-xs sm:w-[250px]"
          />
          <Select
            options={collectionOption}
            selected={selectedHour}
            onSelect={setSelectedHour}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
        </div>
        <div className="mt-2.5 hidden md:block">
          <TokenTable columns={columns} bg={true} data={Tabledata} />
        </div>
        <div className="mt-5 block cursor-pointer space-y-3 md:hidden">
          {Tabledata.map((items: any, index) => (
            <div
              className="rounded-[10px] bg-white p-2.5 shadow-[0_1px_10px_rgba(0,0,0,0.1)]"
              key={index}
            >
              <div className="flex items-center justify-between font-semibold">
                <span>{items.coin}</span>
                <span>{items.chain}</span>
              </div>
              <div className="mt-2 flex items-center justify-between text-sm text-gray-700">
                {columns
                  .filter((col) => visibleColumnKeys.includes(col.key))
                  .map((col, idx) => (
                    <div className="" key={idx}>
                      <div className="text-[10px]">{col.label}</div>
                      <div className="mt-1 text-xs text-primary">
                        {items[col.key] ?? '—'}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      <p className="mt-12 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </>
  );
};

export default Holdings;
