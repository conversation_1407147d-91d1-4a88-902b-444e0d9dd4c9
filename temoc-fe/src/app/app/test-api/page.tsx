'use client';

import { useState } from 'react';
import { discoveryApi } from '@/services/discovery';
import { socialApi } from '@/services/social';

export default function TestApiPage() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testDiscoveryGenres = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await discoveryApi.getGenres();
      setResults({ type: 'genres', data });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch genres');
    } finally {
      setLoading(false);
    }
  };

  const testDiscoveryArtists = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await discoveryApi.discoverArtists({}, 1, 5);
      setResults({ type: 'artists', data });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch artists');
    } finally {
      setLoading(false);
    }
  };

  const testSocialStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await socialApi.getMyStats();
      setResults({ type: 'stats', data });
    } catch (err: any) {
      setError(
        err.message || 'Failed to fetch stats - This requires authentication',
      );
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('http://localhost:8080/discovery/genres');
      const data = await response.json();
      setResults({ type: 'direct-api', data });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch direct API');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-gray-900">API Test Page</h1>

        <div className="mb-8 grid grid-cols-1 gap-4 md:grid-cols-4">
          <button
            onClick={testDirectAPI}
            disabled={loading}
            className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 disabled:opacity-50"
          >
            Test Direct API
          </button>

          <button
            onClick={testDiscoveryGenres}
            disabled={loading}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:opacity-50"
          >
            Test Genres API
          </button>

          <button
            onClick={testDiscoveryArtists}
            disabled={loading}
            className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700 disabled:opacity-50"
          >
            Test Artists API
          </button>

          <button
            onClick={testSocialStats}
            disabled={loading}
            className="rounded-md bg-primary px-4 py-2 text-white hover:bg-orange-600 disabled:opacity-50"
          >
            Test Social Stats API
          </button>
        </div>

        {loading && (
          <div className="py-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        )}

        {error && (
          <div className="mb-4 rounded-md border border-red-200 bg-red-50 p-4">
            <h3 className="font-medium text-red-800">Error:</h3>
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {results && (
          <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
            <h2 className="mb-4 text-xl font-semibold text-gray-900">
              Results ({results.type}):
            </h2>
            <pre className="overflow-auto rounded-md bg-gray-100 p-4 text-sm">
              {JSON.stringify(results.data, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}
