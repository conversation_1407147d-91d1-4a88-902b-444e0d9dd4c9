'use client';
import { Button } from '@/components/common';
import React, { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { transactionsService } from '@/services/transaction.service';
import Modal from '@/components/common/Modal';
import AOS from 'aos';
import {
  DynamicWidget,
  useDynamicContext,
  useDynamicEvents,
  useOpenFundingOptions,
} from '@dynamic-labs/sdk-react-core';
import { useBalance } from 'wagmi';
import { Address } from 'viem';
// import { tokenValues } from '@/app/constants/token.constants';
import ImageComponent from '@/components/common/ImageComponent';
import { formatDisplayAmount } from '@/lib/utils';
import ProgressBar from './ProgressBar';
import BuyWithCrypto from './BuyWithCrypto';
import BuyWithCard from './BuyWithCard';
import { presaleService } from '@/services/presale.service';
import { useAuth } from '@/hooks/useAuth';
import { formatToLocalDateTimeString } from '@/utils/truncateAddress';
import CountdownComponent from '@/components/common/Countdown';
import { tokenService } from '@/services/token.service';
import { isAfter } from 'date-fns';
interface profileProps {
  profile?: any;
  album?: any;
  hideFirstModal?: any;
  setOpenModal?: any;
}
const BuyModal = ({
  profile,
  album,
  hideFirstModal,
  setOpenModal,
}: profileProps) => {
  const [paymentType, setPaymentType] = useState('');
  const [typeToSwitch, setTypeToSwitch] = useState('');
  const [prevBalance, setPrevBalance] = useState(0);
  const [showOthers, setShowOthers] = useState(false);
  const [top, setTop] = useState(false);
  const { user: authUser } = useAuth();
  const [walletConfirmation, setWalletConfirmation] = useState(false);

  const { data } = useQuery({
    queryKey: ['presale', profile?._id],
    queryFn: () => presaleService.getPresale(profile?._id as string),
    enabled: !!profile?._id,
  });

  const presaleData = data?.data || {};

  console.log(presaleData, 'presale');

  const { data: useToken } = useQuery({
    queryKey: ['artist_token'],
    queryFn: () => tokenService.getArtistToken(profile?._id),
  });

  // console.log(currentTab, 'tracks');
  const token = useToken?.data?.[0];

  useDynamicEvents('walletAdded', async () => {
    if (typeToSwitch == 'crypto') {
      setPaymentType('Crypto');
      setTypeToSwitch('');
      setWalletConfirmation(false);
    } else if (typeToSwitch == 'card') {
      setPaymentType('Card');
      setTypeToSwitch('');
      setWalletConfirmation(false);
    }
  });

  const { openFundingOptions } = useOpenFundingOptions();
  const { user, setShowAuthFlow, primaryWallet } = useDynamicContext();

  const { data: balance } = useBalance({
    address: primaryWallet?.address as Address,
  });

  const formatedDate = formatToLocalDateTimeString(presaleData?.presaleEnd);

  const targetDate = new Date(formatedDate);
  console.log(targetDate);

  // const { userPurchasedAmountData } = usePresale();
  //   const { data: salesData } = useQuery({
  //     queryKey: ["sales"],
  //     queryFn: () => transactionsService.getTotalTokenSales(),
  //   });

  //   const soldTokens = salesData?.data?.data || 0;
  //   const totalAmount = tokenValues.totalAmount;
  //   const percentage = Number(Number((soldTokens / totalAmount) * 100).toFixed());

  const handleBuy = () => {
    if (!user) {
      setWalletConfirmation(true);
      setTypeToSwitch('crypto');
      return;
    }

    setPaymentType('Crypto');
  };

  // const handleBuyWithCard = () => {
  //   if (!user) {
  //     setWalletConfirmation(true);
  //     setTypeToSwitch('card');
  //     return;
  //   }

  //   setPaymentType('Card');
  // };

  useEffect(() => {
    AOS.init({
      duration: 1000,
      easing: 'ease-in-out',
    });
  }, []);

  useEffect(() => {
    if (prevBalance !== null && balance?.value && balance.value > prevBalance) {
      // setPaymentType('Crypto');
      setPrevBalance(0); // reset
    }
  }, [balance?.value, prevBalance]);

  useEffect(() => {
    if (primaryWallet?.isConnected) {
      setWalletConfirmation(false);
      if (typeToSwitch == 'crypto') {
        setPaymentType('Crypto');
      } else if (typeToSwitch == 'card') {
        setPaymentType('Card');
      }
    }
    if (!primaryWallet?.isConnected) {
      setPaymentType('');
    }
  }, [primaryWallet?.isConnected]);

  const { data: userTokens } = useQuery({
    queryKey: ['user_tokens'],
    queryFn: () =>
      transactionsService.getFanTotalTokens(
        authUser?._id as string,
        presaleData?._id,
      ),
    enabled: !!authUser?._id,
  });

  const userPurchasedAmountData = userTokens?.data || 0;
  console.log(userPurchasedAmountData, 'userPurchasedAmountData');
  const now = new Date();
  const presaleEnd = new Date(presaleData?.presaleEnd);

  return (
    <>
      <div className="relative w-full lg:p-5">
        <p className="text-center !text-3xl font-medium uppercase text-black">
          Offer Ends In
        </p>
        <div className="flex justify-center">
          <CountdownComponent date={targetDate} />
        </div>

        {isAfter(now, presaleEnd) && (
          <div className="mt-4 text-center font-semibold text-red-600">
            Presale has ended.
          </div>
        )}
        <div className="mt-6 flex justify-center">
          {primaryWallet?.isConnected && (
            <DynamicWidget
              variant="modal"
              innerButtonComponent={
                <Button className="AtConnect">Connect Wallet</Button>
              }
              buttonClassName="!bg-[#C9FA49] !text-[#333333] font-bold rounded-full"
            />
          )}
        </div>

        <div className="mt-7 flex items-center justify-between">
          <span className="text-xs text-black">Public Presale</span>
          <span className="text-xs text-black">
            Discounted Price - ${presaleData?.fairLaunchAmount} USD
          </span>
        </div>
        <ProgressBar percentage={36} />
        <div className="mt-2.5 flex flex-col items-center justify-center gap-2 sm:flex-row sm:justify-between">
          <span className="block w-full text-center text-xs text-black sm:text-right">
            {/* Total Supply: {totalAmount.toLocaleString()} */}
            Price At Launch: ${presaleData?.listingOn}
          </span>
        </div>

        {primaryWallet?.isConnected && userPurchasedAmountData ? (
          <div className="mt-4 flex items-center justify-between">
            <span className="text-base text-black/60">Token Balance</span>
            <div className="flex items-center gap-1 text-base text-primary">
              <ImageComponent
                src={token?.logoUrl || '/assets/images/coin.png'}
                figClassName="h-6 w-6 flex-shrink-0 rounded-full"
                fill
                alt=""
                priority
                className="cursor-pointer rounded-full object-cover"
              />
              {formatDisplayAmount(userPurchasedAmountData?.totalTokens) || 0}
            </div>
          </div>
        ) : null}
        {paymentType == '' ? (
          <div className="">
            <div className="mt-5 flex items-center justify-center">
              <Button
                className="h-[51px] w-full font-medium"
                onClick={() => handleBuy()}
              >
                Buy with Crypto
              </Button>
              {/* <Button
                className="h-[51px] w-full font-medium"
                onClick={() => handleBuyWithCard()}
              >
                Buy With Card
              </Button> */}
              {/* <Button
                className="col-span-2 h-[51px] w-full font-medium"
                onClick={() => setShowOthers(true)}
              >
                Bank Transfer/Others
              </Button> */}
            </div>
            {/* <div className="flex justify-center">
              {' '}
              <Link
                href="#"
                className="mt-5 text-center text-xs text-orange underline"
              >
                Don’t have a wallet?
              </Link>
            </div> */}
          </div>
        ) : paymentType == 'Crypto' ? (
          <BuyWithCrypto
            hideFirstModal={hideFirstModal}
            setOtherModal={setOpenModal}
            setPaymentType={setPaymentType}
            presaleData={presaleData}
            album={album}
            token={token}
          />
        ) : paymentType == 'Card' ? (
          <BuyWithCard
            setPaymentType={setPaymentType}
            presaleData={presaleData}
          />
        ) : null}
        {paymentType !== 'Card' && (
          <div className="mt-4 text-center text-xs font-normal text-white">
            <p className="text-black">
              Don&apos;t have enough Crypto?{' '}
              <span
                className="cursor-pointer text-primary hover:underline"
                onClick={() => setTop(true)}
              >
                Top up with Debit/Credit Card
              </span>
            </p>
          </div>
        )}

        {/* <div className="mt-4 flex items-center justify-center gap-1">
          <span className="text-xs text-primary">Powered by</span>
          <img src="/assets/images/home/<USER>" alt="" />
        </div> */}
      </div>
      <Modal
        className="hideScrollbar !h-max !max-w-[580px] !px-5 !pb-6 md:pb-0"
        show={top}
        hide={setTop}
      >
        <div className="relative mt-10">
          <h2 className="text-center text-[2rem] font-semibold text-[#212428] xs:mt-2">
            Top Up Your Wallet
          </h2>

          <p className="m-auto mt-5 max-w-[480px] text-center text-xl text-[#212428] xs:text-base">
            To begin, please read the instructions and click “Continue.” After
            you connect your wallet, select the{' '}
            <span className="font-semibold">“Buy Crypto”</span> option and
            choose your preferred exchange (e.g., Coinbase or Banxa) to complete
            the top-up. Once your wallet is funded, return to the home page to
            purchase Azuki tokens.
          </p>
          <div className="my-5 flex items-center justify-center">
            <Button
              arrow
              onClick={() => {
                if (!primaryWallet?.isConnected) {
                  setShowAuthFlow(true);
                  setTop(false);
                }
                openFundingOptions();
              }}
              className="!h-[65px] !w-[194px] !text-lg !font-medium xs:!h-[50px]"
            >
              Continue
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        className="hideScrollbar !h-max !max-w-[580px] !px-5 !pb-6 md:pb-0"
        show={walletConfirmation}
        hide={setWalletConfirmation}
      >
        <div className="relative mt-10">
          <h2 className="text-center text-[2rem] font-semibold text-[#212428] xs:mt-2">
            Connect Your Wallet
          </h2>

          <p className="m-auto mt-5 flex max-w-[480px] flex-col text-center text-xl text-[#212428] xs:text-base">
            To get started, please connect your wallet. Don&apos;t have one? No
            worries — just sign in with your email, and we’ll create a wallet
            for you automatically!
            <i className="mt-3 text-sm">
              <b>Note:</b> We need your wallet to deliver your tokens once the
              project launches.
            </i>
          </p>
          <div className="my-5 flex items-center justify-center">
            <Button
              arrow
              onClick={() => {
                if (!primaryWallet?.isConnected) {
                  setShowAuthFlow(true);
                  setWalletConfirmation(false);
                }
              }}
              className="!h-[65px] !w-[194px] !text-lg !font-medium xs:!h-[50px]"
            >
              Continue
            </Button>
          </div>
        </div>
      </Modal>

      <Modal
        className="hideScrollbar !h-max !max-w-[800px] !pb-6 md:pb-0"
        show={showOthers}
        hide={setShowOthers}
      >
        <div className="relative">
          <iframe
            width={660}
            className="h-[630px] w-full"
            title="Embedded URL"
            src="https://form.typeform.com/to/rhFjPMgx"
          ></iframe>
        </div>
      </Modal>
    </>
  );
};

export default BuyModal;
