import React, { Dispatch, SetStateAction, useContext, useState } from 'react';
import { Button } from '@/components/common';
import { Input } from '@/components/common/Forms/Input';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { usePrice } from '@/hooks/usePrice';
import { Interface } from 'ethers';
import { signSmartContractData } from '@wert-io/widget-sc-signer';
import WertWidget from '@wert-io/widget-initializer';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'react-toastify';
import Modal from '@/components/common/Modal';

// import { tokenValues } from '@/constants/token.constants';
import SuccessComponent from './SuccessComponent';
// import { tokenValues } from '@/app/constants/token.constants';
import ImageComponent from '@/components/common/ImageComponent';
import { ChainContext } from '@/context/ChainContextProvider';
import WertInitializing from './WertInitializing';

interface IProps {
  setPaymentType: Dispatch<SetStateAction<string>>;
  presaleData: any;
}

const BuyWithCard = ({ setPaymentType, presaleData }: IProps) => {
  const tokenPrice = presaleData?.fairLaunchAmount;

  // Define schema for validation using Yup
  const schema = yup.object({
    payAmount: yup
      .number()
      .typeError('Amount must be a number')
      .required('Pay amount is required')
      .positive('Amount must be greater than zero')
      .min(1, `Pay amount must be greater than or equal to ${1}`),
    receiveAmount: yup
      .number()
      .typeError('Amount must be a number')
      .required('Receive amount is required')
      .positive('Amount must be greater than zero')
      .min(1, `Receive amount must be greater than or equal to ${1}`),
  });

  type BuyCryptoFormValues = yup.InferType<typeof schema>;

  const [loading, setLoading] = useState<boolean>(false);
  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [hash, setHash] = useState('');
  const [wertInitializingModal, setWertInitializingModal] =
    useState<boolean>(false);
  const { user, setShowAuthFlow, primaryWallet } = useDynamicContext();
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<BuyCryptoFormValues>({
    resolver: yupResolver(schema),
  });

  const [payAmount, setPayAmount] = useState('');
  const [receiveAmount, setReceiveAmount] = useState('');

  const { chain } = useContext(ChainContext);

  const { data: ethPriceData } = usePrice('ETH');
  const ethPrice = ethPriceData?.quote?.USD?.price
    ? Number(ethPriceData?.quote?.USD?.price).toFixed(4)
    : 0;
  const ethPerUsd = 1 / (Number(ethPrice) | 0);

  const handlePayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPayAmount(value);

    const numeric = parseFloat(value);
    if (!isNaN(numeric)) {
      const received = (numeric / tokenPrice).toFixed(3);
      setReceiveAmount(received);
      setValue('receiveAmount', parseFloat(received), { shouldValidate: true });
    } else {
      setReceiveAmount('');
    }

    setValue('payAmount', parseFloat(value), { shouldValidate: true });
  };

  const handleReceiveChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setReceiveAmount(value);

    const numeric = parseFloat(value);
    if (!isNaN(numeric)) {
      const pay = (numeric * tokenPrice).toFixed(3);
      setPayAmount(pay);
      setValue('payAmount', parseFloat(pay), { shouldValidate: true });
    } else {
      setPayAmount('');
    }

    setValue('receiveAmount', parseFloat(value), { shouldValidate: true });
  };

  const sendEmail = async (amount: number, transactionId: string) => {
    const res = await fetch('/api/send-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        to: user?.email,
        numberOfTokens: Number(amount) / tokenPrice,
        amountPaid: Number(amount),
        transactionId,
      }),
    });

    const data = await res.json();
    console.log(data);
  };

  const encodeFunctionData = (
    functionName: string,
    typesArray: any[],
    valuesArray: any[],
  ) => {
    const iface = new Interface([`function ${functionName}`]);
    return iface.encodeFunctionData(functionName, valuesArray);
  };

  const buyWithCard = (data: BuyCryptoFormValues) => {
    if (!user) {
      setShowAuthFlow(true);
      return;
    }
    setLoading(true);
    setWertInitializingModal(true);
    setTimeout(() => {
      setWertInitializingModal(false);
    }, 3000);

    const tokens = Number(Number(data.payAmount) / tokenPrice).toFixed();
    const ethValue = Number((Number(data.payAmount) * ethPerUsd).toFixed(8));

    const functionName = 'purchase(address,uint256)';
    const types = ['address', 'uint256'];
    const values = [primaryWallet?.address, tokens];

    const encodedData = encodeFunctionData(functionName, types, values);

    const signedData = signSmartContractData(
      {
        address: primaryWallet?.address as string,
        commodity: 'ETH',
        network: chain.name.toLowerCase(),
        commodity_amount: ethValue,
        sc_address: process.env.NEXT_PUBLIC_PRESALE_ADDRESS as string,
        sc_input_data: encodedData, // Assuming you need the actual encoded function call here
      },
      process.env.NEXT_PUBLIC_PRIVATE_KEY as string,
    );

    const wertWidget = new WertWidget({
      email: user?.email,
      // ...(user.firstName && {
      //   full_name: `${user.firstName} ${user.lastName}`,
      // }),
      ...signedData,

      partner_id: process.env.NEXT_PUBLIC_PARTNER_ID as string,
      click_id: `${user?.email}+${uuidv4()}`, // Unique id of purchase in your system
      origin: process.env.NEXT_PUBLIC_WERT_URL,
      listeners: {
        loaded: () => {
          setWertInitializingModal(false);
        },
        'payment-status': (paymentData) => {
          if (paymentData.status === 'success') {
            sendEmail(data?.payAmount, paymentData?.tx_id as string);
            //success modal
            setHash(paymentData?.tx_id as string);
            setSuccessModal(true);
          } else if (paymentData.status === 'failed') {
            toast.error('Payment failed');
            console.error('Payment failed:', paymentData);
          }
          setLoading(false);
        },
        close: () => {
          setLoading(false);
        },
      },
    });

    wertWidget.open();
  };

  const onSubmit = (data: BuyCryptoFormValues) => {
    console.log('✅ Form Submitted:', {
      ...data,
    });
    buyWithCard(data);
  };

  return (
    <div className="mt-5 text-white">
      {/* Pay and Receive Section */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-2 gap-3 xs:grid-cols-1">
          <div className="rounded-[14px] border border-[#CECECE] bg-white p-3">
            <p className="text-xs text-[#777777]">You Pay (USD)</p>
            <div className="flex items-center justify-between">
              <div className="text-bas flex items-center">
                <Input
                  type="number"
                  placeholder="0.00"
                  name="payAmount"
                  value={payAmount}
                  onChange={handlePayChange}
                  register={register}
                  className="no-spinner !border-none !px-0 !py-1"
                />
              </div>
              <p className="text-black">$</p>
            </div>
            {errors.payAmount && (
              <p className="text-xs text-red-500">{errors.payAmount.message}</p>
            )}
          </div>

          <div className="rounded-[14px] border border-[#CECECE] bg-white p-3">
            <p className="text-xs text-[#777777]">You Receive $TEMOC</p>
            <div className="flex items-center justify-between">
              <Input
                type="number"
                placeholder="0.00"
                name="receiveAmount"
                value={receiveAmount}
                onChange={handleReceiveChange}
                register={register}
                className="no-spinner !border-none !px-0 !py-1"
              />
              <ImageComponent
                src="/assets/images/coin.png"
                figClassName="h-6 w-6 flex-shrink-0 rounded-full"
                fill
                alt=""
                priority
                className="cursor-pointer rounded-full object-cover"
              />
            </div>
            {errors.receiveAmount && (
              <p className="text-xs text-red-500">
                {errors.receiveAmount.message}
              </p>
            )}
          </div>
        </div>

        <p className="mt-2 text-xs text-black">
          ⓘ You will get {receiveAmount} TEMOC Coin
        </p>

        <Button
          type="submit"
          className="mt-5 w-full"
          isLoading={loading}
          disabled={loading}
        >
          Buy Now
        </Button>

        <Button
          variant="outline"
          className="mt-3 w-full"
          onClick={() => {
            setPaymentType('');
          }}
        >
          Back
        </Button>
      </form>

      <div className="mt-5 text-center text-xs font-normal text-black">
        <p className="text-xs font-normal text-black">
          Want to pay with Crypto instead?{' '}
          <span
            onClick={() => {
              setPaymentType('Crypto');
            }}
            className="cursor-pointer text-primary hover:underline"
          >
            Click Here
          </span>
        </p>
      </div>

      <Modal
        className="hideScrollbar !h-max !max-w-[600px]"
        show={wertInitializingModal}
        hide={() => setWertInitializingModal(false)}
      >
        <WertInitializing />
      </Modal>
      <Modal
        className="hideScrollbar !h-max !max-w-[600px]"
        show={successModal}
        hide={() => setSuccessModal(false)}
      >
        <SuccessComponent amount={payAmount} hash={hash} />
      </Modal>
    </div>
  );
};

export default BuyWithCard;
