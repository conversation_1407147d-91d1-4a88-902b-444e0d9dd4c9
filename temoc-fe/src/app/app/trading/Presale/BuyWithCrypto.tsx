import React, { Dispatch, SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/common';
import Modal from '@/components/common/Modal';
import { Input } from '@/components/common/Forms/Input';
import { useAccount, useBalance } from 'wagmi';
import {
  useDynamicContext,
  useOpenFundingOptions,
} from '@dynamic-labs/sdk-react-core';
import { toast } from 'react-toastify';
// import { supportedChains } from '@/utils/chains';
import { Address } from 'viem';
import {
  formatDisplayAmount,
  // getExplorerUrl,
  hasSufficientBalance,
} from '@/lib/utils';
import ImageComponent from '@/components/common/ImageComponent';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Usdt from '@/components/common/Icons/Usdt';
import PaymentMethodSelector, {
  PaymentOption,
  paymentOptions,
} from './PaymentMethodSelector';
import { usePrice } from '@/hooks/usePrice';
import { parseBlockchainError } from '@/utils/errorParser';
import SuccessComponent from './SuccessComponent';
import { useTransferToken } from '@/hooks/useTransferToken';
import { transactionsService } from '@/services/transaction.service';
import { useAuth } from '@/hooks/useAuth';
import { isAfter } from 'date-fns';

interface IProps {
  setPaymentType: Dispatch<SetStateAction<string>>;
  presaleData: any;
  album: any;
  token: any;
  hideFirstModal: any;
  setOtherModal: Dispatch<SetStateAction<boolean>>;
}

const BuyWithCrypto = ({
  setPaymentType,
  presaleData,
  album,
  hideFirstModal,
  token,
  setOtherModal,
}: IProps) => {
  const tokenPrice = presaleData?.fairLaunchAmount;

  const schema = yup.object({
    payAmount: yup
      .number()
      .typeError('Amount must be a number')
      .required('Pay amount is required')
      .positive('Amount must be greater than zero')
      .min(
        tokenPrice,
        `Pay amount must be greater than or equal to ${tokenPrice}`,
      ),
    receiveAmount: yup
      .number()
      .typeError('Amount must be a number')
      .required('Receive amount is required')
      .positive('Amount must be greater than zero')
      .min(1, `Receive amount must be greater than or equal to ${1}`),
  });

  type BuyCryptoFormValues = yup.InferType<typeof schema>;

  const { isConnected } = useAccount();
  const { user: authUser } = useAuth();
  const [insufficientFund, setInsufficientFund] = useState(false);
  const { setShowAuthFlow, user, primaryWallet } = useDynamicContext();
  // const chainId = useChainId();
  // const activeChain = supportedChains.find((chain) => chain.id == chainId);
  const [top, setTop] = useState(false);

  const [openModal, setOpenModal] = useState(false);
  const [successModal, setSuccessModal] = useState<boolean>(false);
  const [selectedPayment, setSelectedPayment] = useState<PaymentOption | null>(
    paymentOptions[0],
  );
  const [payAmount, setPayAmount] = useState('');
  const [receiveAmount, setReceiveAmount] = useState('');
  const [hash, setHash] = useState('');
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const { openFundingOptions } = useOpenFundingOptions();
  const { data: balanceData } = useBalance({
    address: primaryWallet?.address as Address,
    chainId: selectedPayment?.chainId,
    token: selectedPayment?.tokenAddress,
  });

  const transactionMutation = useMutation({
    mutationFn: (data: any) => transactionsService.createTransaction(data),
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ['user_tokens'] });
      queryClient.refetchQueries({ queryKey: ['access_map'] });
      queryClient.refetchQueries({ queryKey: ['creator_albums'] });
      console.log('transactionMutation transection successfull');
    },
  });
  const userBalance = balanceData?.formatted || '0';
  const { transfer } = useTransferToken();
  // const explorerUrl = selectedPayment?.chainId
  //   ? getExplorerUrl(selectedPayment.chainId)
  //   : null;
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<BuyCryptoFormValues>({
    resolver: yupResolver(schema),
  });

  // const { data: ethPriceData } = usePrice('ETH');
  // const ethPrice = ethPriceData?.quote?.USD?.price
  //   ? Number(ethPriceData?.quote?.USD?.price).toFixed(4)
  //   : 0;
  // const ethPerUsd = 1 / (Number(ethPrice) | 0);
  // console.log(ethPerUsd, 'ethPerUsd');

  // 🔼 Top-level inside component
  const ethPriceData = usePrice('ETH');
  const usdtPriceData = usePrice('USDT');
  const usdcPriceData = usePrice('USDC');

  // Price map
  const priceMap: Record<string, number> = {
    ETH: ethPriceData?.data?.quote?.USD?.price || 0,
    USDT: usdtPriceData?.data?.quote?.USD?.price || 0,
    USDC: usdcPriceData?.data?.quote?.USD?.price || 0,
  };

  // const sendEmailMutation = useMutation({
  //   mutationFn: (data: {
  //     to: string | undefined;
  //     numberOfTokens: number;
  //     amountPaid: string;
  //     explorerLink: string;
  //   }) => emailService.senEmail(data),
  // });

  const handlePayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPayAmount(value);

    const numeric = parseFloat(value);
    if (!isNaN(numeric)) {
      const received = (numeric / tokenPrice).toFixed(4);
      setReceiveAmount(formatDisplayAmount(received));
      setValue('receiveAmount', parseFloat(received), { shouldValidate: true });
    } else {
      setReceiveAmount('');
    }

    setValue('payAmount', parseFloat(value), { shouldValidate: true });
  };

  const handleReceiveChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setReceiveAmount(value);

    const numeric = parseFloat(value);
    if (!isNaN(numeric)) {
      const pay = (numeric * tokenPrice).toFixed(4);
      setPayAmount(pay);
      setValue('payAmount', parseFloat(pay), { shouldValidate: true });
    } else {
      setPayAmount('');
    }

    setValue('receiveAmount', parseFloat(value), { shouldValidate: true });
  };

  const onSelectPayment = (option: PaymentOption) => {
    setSelectedPayment(option);
    setOpenModal(false); // Close modal after selection
  };
  console.log(balanceData, 'userBalance');

  const onSubmit = (data: BuyCryptoFormValues) => {
    console.log('✅ Form Submitted:', {
      ...data,
      paymentMethod: selectedPayment,
    });

    if (!isConnected) {
      setShowAuthFlow(true);
      return;
    }

    buyTokens(data);
  };

  const buyTokens = async (data: BuyCryptoFormValues) => {
    try {
      setLoading(true);
      console.log(selectedPayment, 'selectedPayment--');
      if (primaryWallet?.connector.supportsNetworkSwitching()) {
        await primaryWallet.switchNetwork(selectedPayment?.chainId as number);
        await new Promise((resolve) => setTimeout(resolve, 300));
      }

      const currencySymbol = selectedPayment?.symbol;
      const currencyPrice = priceMap[currencySymbol || 'ETH'] || 0;

      // ✅ Convert USD to selected currency (ETH, USDT, etc.)
      const amountToPayInCurrency = data?.payAmount / currencyPrice;

      if (
        !hasSufficientBalance(
          userBalance,
          amountToPayInCurrency,
          setInsufficientFund,
        )
      ) {
        setLoading(false);
        return;
      }
      console.log(amountToPayInCurrency, 'amountToPayInCurrency');

      // ✅ Convert to smallest unit (wei or token decimals)
      // const amountInSmallestUnit = selectedPayment?.tokenAddress
      //   ? amountToPayInCurrency
      //   : amountToPayInCurrency;

      // ✅ Calculate how many tokens the user will get
      // const tokensToReceive = data?.payAmount / 0.0005; // token price in USD

      const hash = await transfer({
        tokenAddress: selectedPayment?.tokenAddress,
        amount: amountToPayInCurrency.toString(),
        recipient: presaleData?.ownerAddress,
        decimals: selectedPayment?.decimals,
      });
      const transectionData = {
        userId: authUser?._id,
        artistId: authUser?._id,
        presaleId: presaleData?._id,
        albumId: album?._id,
        amount: data.payAmount,
        tokens: data.receiveAmount,
        tokenAddress: presaleData?.tokenAddress,
        email: user?.email,
        tokensSpent: data.receiveAmount,
        address: primaryWallet?.address,
        txHash: hash,
        quote: selectedPayment?.symbol,
        currencyAddress: selectedPayment?.tokenAddress,
        timestamp: Math.floor(Date.now() / 1000),
        chainId: selectedPayment?.chainId,
        currencyPrice,
        expectedRecipient: presaleData?.ownerAddress,
      };
      // const emailData = {
      //   to: user?.email,
      //   numberOfTokens: Number(receiveAmount),
      //   amountPaid: Number(amountToPayInCurrency).toFixed(4),
      //   explorerLink: `${explorerUrl}${hash}`,
      // };
      if (hash) {
        setHash(hash);
        // sendEmailMutation.mutate(emailData);
        transactionMutation.mutate(transectionData);
      }

      setSuccessModal(true);
      hideFirstModal(false);
      setOtherModal(false);
      setLoading(false);
    } catch (err: any) {
      console.error(err);
      toast.error(parseBlockchainError(err));
      setLoading(false);
    }
  };
  const now = new Date();
  const presaleEnd = new Date(presaleData?.presaleEnd);
  const isPresaleEnded = isAfter(now, presaleEnd);
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="mt-5 text-white">
        <div className="grid grid-cols-2 gap-3 xs:grid-cols-1">
          <div className="rounded-[14px] border border-[#CECECE] bg-white p-3">
            <p className="text-xs text-[#777777]">You Pay (USD)</p>
            <div className="flex items-center justify-between">
              <div className="text-bas flex items-center text-black">
                <Input
                  type="number"
                  placeholder="0.00"
                  name="payAmount"
                  value={payAmount}
                  onChange={handlePayChange}
                  register={register}
                  className="no-spinner !border-none !px-0 !py-1"
                />
              </div>
              <p className="text-black">$</p>
            </div>
            {errors.payAmount && (
              <p className="text-xs text-red-500">{errors.payAmount.message}</p>
            )}
          </div>

          <div className="rounded-[14px] border border-[#CECECE] bg-white p-3">
            <p className="text-xs text-[#777777]">
              You Receive ${token?.symbol}
            </p>
            <div className="flex items-center justify-between">
              <Input
                type="number"
                placeholder="0.00"
                name="receiveAmount"
                value={receiveAmount}
                onChange={handleReceiveChange}
                register={register}
                className="no-spinner !border-none !px-0 !py-1"
              />
              <ImageComponent
                src={token?.logoUrl || '/assets/images/coin.png'}
                figClassName="h-6 w-6 flex-shrink-0 rounded-full"
                fill
                alt=""
                priority
                className="cursor-pointer rounded-full object-cover"
              />
            </div>
            {errors.receiveAmount && (
              <p className="text-xs text-red-500">
                {errors.receiveAmount.message}
              </p>
            )}
          </div>
        </div>

        <div
          className="mt-3 flex cursor-pointer items-center justify-between rounded-[14px] border border-[#CECECE] bg-white p-3"
          onClick={() => setOpenModal(true)}
        >
          <div>
            <p className="mb-1 text-xs text-[#777777]">Select Payment Method</p>
            <div className="flex items-center gap-2">
              {selectedPayment?.icon ?? <Usdt />}
              <span className="text-black">
                {selectedPayment?.symbol ?? 'USDT'}
              </span>
              <span className="text-xs text-gray-400">
                {selectedPayment?.network && `(${selectedPayment.network})`}
              </span>
            </div>
          </div>
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>

        <p className="mt-2 text-xs text-black">
          ⓘ You will get <strong>{formatDisplayAmount(receiveAmount)}</strong>{' '}
          {token?.name} Coin
        </p>
        <Button
          type="submit"
          className="mt-5 w-full"
          isLoading={loading}
          disabled={loading || isPresaleEnded}
        >
          Buy Now
        </Button>

        <Button
          variant="outline"
          className="mt-3 w-full"
          onClick={() => setPaymentType('')}
        >
          Back
        </Button>

        {/* <div className="mt-5 text-center text-xs font-normal text-black">
          <p className="text-xs">
            Want to pay with Card instead?{' '}
            <span
              onClick={() => setPaymentType('Card')}
              className="cursor-pointer text-primary hover:underline"
            >
              Click Here
            </span>
          </p>
          {/* <p className="mt-4 text-primary">
            Not enough ETH?{" "}
            <a href="#" className="text-primary hover:underline">
              Top up now
            </a>
          </p> */}
        {/* </div>  */}
      </form>

      <Modal
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
        show={openModal}
        hide={() => setOpenModal(false)}
      >
        <PaymentMethodSelector
          setPaymentType={setPaymentType}
          onSelectPayment={onSelectPayment}
        />
      </Modal>

      <Modal
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
        show={successModal}
        hide={() => setSuccessModal(false)}
      >
        <SuccessComponent amount={receiveAmount} hash={hash!} />
      </Modal>

      <Modal
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
        show={insufficientFund}
        hide={setInsufficientFund}
      >
        <div className="relative mt-10">
          <h2 className="text-center !font-display !text-3xl font-semibold text-[#212428] sm:!text-4xl xs:mt-2">
            Insufficient balance
          </h2>

          <p className="m-auto mt-5 max-w-[427px] text-center text-xl text-[#212428] xs:text-base">
            You don’t have enough funds in your wallet to complete this
            purchase. Top up using your debit/credit card instantly.
          </p>
          <div className="my-5 flex items-center justify-center">
            <Button
              arrow
              onClick={() => setTop(true)}
              className="!h-[65px] !w-[194px] !text-lg !font-medium xs:!h-[50px]"
            >
              Top Up Now
            </Button>
          </div>
        </div>
      </Modal>
      <Modal
        className="hideScrollbar !h-max !max-w-[580px] !px-5 !pb-6 md:pb-0"
        show={top}
        hide={setTop}
      >
        <div className="relative mt-10">
          <h2 className="text-center text-[2rem] font-semibold text-[#212428] xs:mt-2">
            Top Up Your Wallet
          </h2>

          <p className="m-auto mt-5 max-w-[480px] text-center text-xl text-[#212428] xs:text-base">
            To begin, please read the instructions and click “Continue.” On the
            next screen, select Deposit Funds and choose your preferred exchange
            (e.g., Coinbase or Veezlo) to complete the top-up. Once your wallet
            is funded, return to the home page to purchase Azuki tokens.
          </p>
          <div className="my-5 flex items-center justify-center">
            <Button
              arrow
              onClick={() => {
                if (top === true) {
                  setInsufficientFund(false);
                }
                if (!primaryWallet?.isConnected) {
                  setShowAuthFlow(true);
                  setTop(false);
                }
                openFundingOptions();
              }}
              className="!h-[65px] !w-[194px] !text-lg !font-medium xs:!h-[50px]"
            >
              Continue
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default BuyWithCrypto;
