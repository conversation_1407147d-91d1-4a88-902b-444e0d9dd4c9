// import { But<PERSON> } from '@/components/common';
import { ETHIcon, TetherIcon, USDCIcon } from '@/components/common/Icons';
import React, { Dispatch, ReactElement, SetStateAction, useState } from 'react';
import { base, baseSepolia, mainnet } from 'viem/chains';
import { useAccount, useBalance } from 'wagmi';

interface Iprops {
  setPaymentType: Dispatch<SetStateAction<string>>;
  onSelectPayment?: (option: PaymentOption) => void;
}

export interface PaymentOption {
  id: string;
  chainId: number;
  name: string;
  symbol: string;
  icon: ReactElement;
  value: number;
  network: string;
  disabled?: boolean;
  tokenAddress?: `0x${string}`; // optional for native tokens
  decimals?: number;
}

export const paymentOptions: PaymentOption[] = [
  {
    id: 'eth',
    chainId: mainnet.id,
    name: 'Ethereum',
    symbol: 'ETH',
    icon: <ETHIcon />,
    value: 99,
    network: 'Ethereum',
    tokenAddress: undefined, // Native ETH
  },
  {
    id: 'usdt',
    chainId: mainnet.id,
    name: 'Tether USD (Ethereum)',
    symbol: 'USDT',
    icon: <TetherIcon />,
    value: 99,
    network: 'Ethereum',
    tokenAddress: '******************************************',
    decimals: 6,
  },
  {
    id: 'usdc',
    chainId: mainnet.id,
    name: 'USD Coin (Ethereum)',
    symbol: 'USDC',
    icon: <USDCIcon />,
    value: 99,
    network: 'Ethereum',
    tokenAddress: '******************************************',
    decimals: 6,
  },
  {
    id: 'eth_base',
    chainId: base.id,
    name: 'Ethereum (Base)',
    symbol: 'ETH',
    icon: <ETHIcon />,
    value: 99,
    network: 'Base',
    tokenAddress: undefined, // Native ETH on Base
  },
  {
    id: 'usdt_base',
    chainId: base.id,
    name: 'Tether USD (Base)',
    symbol: 'USDT',
    icon: <TetherIcon />,
    value: 99,
    network: 'Base',
    tokenAddress: '******************************************', // Replace with actual Base USDT address
    decimals: 6,
  },
  {
    id: 'usdc_base',
    chainId: base.id,
    name: 'USD Coin (Base)',
    symbol: 'USDC',
    icon: <USDCIcon />,
    value: 99,
    network: 'Base',
    tokenAddress: '******************************************', // Replace with actual Base USDC address
    decimals: 6,
  },
  {
    id: 'usdc_base_sepolia',
    chainId: baseSepolia.id,
    name: 'USD Coin (Base Sepolia)',
    symbol: 'USDC',
    icon: <USDCIcon />,
    value: 99,
    network: 'Base Sepolia',
    tokenAddress: '******************************************', // Replace with actual Base USDC address
    decimals: 18,
  },
];

const networks: { name: string; disabled?: boolean }[] = [
  { name: 'ALL' },
  { name: 'Ethereum', disabled: false },
  { name: 'Base', disabled: false }, // Example: BSC is disabled
  { name: 'Base Sepolia', disabled: false }, // Example: BSC is disabled
];

const TokenBalance = ({
  address,
  option,
}: {
  address: `0x${string}` | undefined;
  option: PaymentOption;
}) => {
  const { data, isLoading, error } = useBalance({
    address,
    chainId: option.chainId,
    token: option.tokenAddress,
  });

  if (!address) return null;
  if (isLoading) return <span className="">0</span>;
  if (error) return <span className="">0</span>;

  return (
    <span className="">
      {data &&
      parseFloat(data.formatted) > 0 &&
      !/^0\.0{6}$/.test(parseFloat(data.formatted).toFixed(6))
        ? parseFloat(data.formatted).toFixed(6)
        : '0'}
    </span>
  );
};

const PaymentMethodSelector = ({ onSelectPayment }: Iprops) => {
  const [selectedNetwork, setSelectedNetwork] = useState<string>('ALL');
  const { address } = useAccount();
  const [selectedPaymentId, setSelectedPaymentId] = useState<string | null>(
    null,
  );

  const disabledNetworks = networks
    .filter((n) => n.disabled)
    .map((n) => n.name);

  const filteredOptions =
    selectedNetwork === 'ALL'
      ? paymentOptions.filter((opt) => !disabledNetworks.includes(opt.network))
      : paymentOptions.filter((opt) => opt.network === selectedNetwork);

  const handleSelect = (option: PaymentOption) => {
    if (option.disabled) return;
    setSelectedPaymentId(option.id);
    onSelectPayment?.(option);
  };

  return (
    <div className="w-full rounded-md text-black">
      <p className="mb-5 mt-8 text-center !text-3xl font-medium uppercase text-black">
        Please Select Payment Method
      </p>

      <div className="hideScrollbar mb-4 flex flex-nowrap gap-3 overflow-auto">
        {networks.map(({ name, disabled }) => (
          <button
            key={name}
            onClick={() => !disabled && setSelectedNetwork(name)}
            disabled={disabled}
            className={`block w-[130px] flex-shrink-0 whitespace-nowrap rounded-lg border-2 border-primary px-4 py-4 text-sm font-bold transition ${
              selectedNetwork === name
                ? 'bg-primary text-white'
                : 'bg-[#FF8000]/20 text-black'
            } ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
          >
            {name === 'ALL' ? 'All' : name}
          </button>
        ))}
      </div>

      <div className="hideScrollbar h-[300px] space-y-3 overflow-auto rounded-[14px] bg-white p-4">
        {filteredOptions.map((option) => {
          const isDisabled = option.disabled;

          return (
            <div
              key={option.id}
              onClick={() => handleSelect(option)}
              className={`flex items-center justify-between rounded-md px-2 py-2 text-sm transition ${
                selectedPaymentId === option.id
                  ? 'bg-[#333] ring-1 ring-orange-500'
                  : ''
              } ${
                isDisabled
                  ? 'cursor-not-allowed opacity-50'
                  : 'cursor-pointer hover:bg-black/10'
              }`}
            >
              <div className="flex items-center gap-2">
                {option.icon}
                <p className="text-sm font-normal text-black">
                  {option.symbol}
                  <span className="block text-[10px] text-[#999999]">
                    {option.name}
                  </span>
                </p>
              </div>
              <span className="">
                $ <TokenBalance address={address} option={option} />
              </span>
            </div>
          );
        })}
      </div>

      {/* <div className="mt-8 pt-2 text-center">
        <div className="flex items-center justify-center">
          <span className="h-1 w-full border-t border-gray-600"></span>
          <span className="relative inline-block px-2 py-1 text-sm text-black">
            OR
          </span>
          <span className="h-1 w-full border-t border-gray-600"></span>
        </div>
        <Button onClick={() => setPaymentType('Card')} className="mt-2 w-full">
          Buy With Card
        </Button>
      </div> */}
    </div>
  );
};

export default PaymentMethodSelector;
