import React from 'react';

const ProgressBar = ({ percentage }: { percentage: number }) => {
  return (
    <div className="relative mt-2.5 h-7 w-full rounded-full border border-[#CECECE] bg-white">
      <div
        style={{
          width: `${percentage}%`,
        }}
        className={`h-full w-[${percentage}%] rounded-full bg-primary`}
      ></div>
      <span className="absolute right-3 top-1/2 z-20 -translate-y-1/2 text-xs font-medium text-black">
        {percentage}% Sold
      </span>
    </div>
  );
};

export default ProgressBar;
