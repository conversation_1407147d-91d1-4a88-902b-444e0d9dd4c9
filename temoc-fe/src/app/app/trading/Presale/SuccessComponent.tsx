import { formatDisplayAmount } from '@/lib/utils';
import React from 'react';
import { LiaCheckCircleSolid } from 'react-icons/lia';

// import {useTranslations } from 'next-intl';
type Props = {
  amount: string;
  hash: string;
};

const SuccessComponent = ({ amount, hash }: Props) => {
  //  const t = useTranslations();
  return (
    <>
      <div className="relative flex flex-col items-center pb-5">
        <LiaCheckCircleSolid className="h-20 w-20 text-primary" />
        <h2 className="text-center !font-display !text-3xl font-semibold text-[#212428] sm:!text-4xl xs:mt-2">
          Purchase Successful
        </h2>
        <p className="mt-7 text-center font-display text-base text-black/80 lg:w-[80%]">
          You&apos;ve successfully participated in the TEMOC presale. You
          purchased{' '}
          <span className="font-semibold text-black">
            {formatDisplayAmount(amount)} TEMOC
          </span>{' '}
          tokens.{' '}
          <span className="block">
            A confirmation email will be sent shortly.
          </span>
        </p>
        <a
          rel="noreferrer"
          target="_blank"
          href={`https://sepolia.basescan.org/tx/${hash}`}
          className="mt-4 cursor-pointer text-center text-base text-primary hover:underline sm:text-lg"
        >
          View Blockchain Receipt
        </a>
      </div>
    </>
  );
};

export default SuccessComponent;
