import React from 'react';
interface Iprops {
  className?: string;
}
const TetherIcon = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="20"
      height="16"
      viewBox="0 0 20 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.30015 0.385132H15.6728C15.9442 0.385132 16.1949 0.524114 16.3302 0.74959L19.6436 6.26993C19.8154 6.55621 19.7643 6.91754 19.5192 7.1497L10.3992 15.7884C10.1038 16.0682 9.62853 16.0682 9.3331 15.7884L0.225376 7.16137C-0.0254057 6.92382 -0.072366 6.55196 0.112047 6.26388L3.65374 0.731878C3.79167 0.516433 4.03644 0.385132 4.30015 0.385132ZM14.2044 2.84978V4.39871H10.9648V5.47262C13.2401 5.5874 14.9472 6.05873 14.9599 6.62369L14.9598 7.80148C14.9471 8.36644 13.2401 8.83778 10.9648 8.95255V11.5881H8.81364V8.95255C6.53833 8.83778 4.83126 8.36644 4.81863 7.80148L4.81867 6.62369C4.83135 6.05873 6.53833 5.5874 8.81364 5.47262V4.39871H5.57401V2.84978H14.2044ZM9.88924 8.14341C12.3174 8.14341 14.3469 7.74486 14.8436 7.2126C14.4224 6.76125 12.8989 6.40601 10.9648 6.30845V7.43286C10.6182 7.45034 10.2583 7.45955 9.88924 7.45955C9.52015 7.45955 9.16031 7.45034 8.81364 7.43286V6.30845C6.87954 6.40601 5.35606 6.76125 4.93488 7.2126C5.43155 7.74486 7.46107 8.14341 9.88924 8.14341Z"
        fill="#009393"
      />
    </svg>
  );
};

export default TetherIcon;
