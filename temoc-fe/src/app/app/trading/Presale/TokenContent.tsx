import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import Modal from '@/components/common/Modal';
import React, { useState } from 'react';
import BuyModal from './BuyModal';

const TokenContent = () => {
  const [openModal, setOpenModal] = useState(false);
  return (
    <>
      <div className="flex w-max flex-col items-center rounded-[20px] bg-white p-4 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-[30px]">
        <ImageComponent
          src="/assets/images/artist/lock.svg"
          height={60}
          width={60}
        />
        <h3 className="mt-5 text-center text-xl font-semibold sm:text-[30px]">
          Token-Gated Content
        </h3>
        <p className="mt-3 text-center text-sm font-normal sm:text-base">
          Hold 15 Billie Eilish Coin to access this content
        </p>
        <Button
          className="mt-7 w-full sm:!h-[62px] sm:!w-[300px]"
          arrow={true}
          onClick={() => {
            setOpenModal(true);
          }}
        >
          UNLOCK CONTENT
        </Button>
      </div>
      <Modal
        show={openModal}
        hide={setOpenModal}
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
      >
        <BuyModal />
      </Modal>
    </>
  );
};

export default TokenContent;
