'use client';
import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import { Heart, Share2, MessageCircle } from 'lucide-react';
import React, { useState } from 'react';
import { RiDeleteBin5Line } from 'react-icons/ri';

interface Comment {
  src: string;
  name: string;
  day: string;
  des: string;
  like: string;
  share: string;
  replies: { name: string; des: string }[];
}

const initialComments: Comment[] = [
  {
    src: '/assets/images/trading/fan2.svg',
    name: '<PERSON>',
    day: 'May 5, 2025',
    des: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
    like: '25K',
    share: '125K',
    replies: [],
  },
];

const Comments = () => {
  const [replyIndex, setReplyIndex] = useState<number | null>(null);
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [newComment, setNewComment] = useState('');
  const [replyText, setReplyText] = useState('');

  // <PERSON>le posting a new comment
  const handlePost = () => {
    if (!newComment.trim()) return;

    const today = new Date();
    const dateString = today.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });

    const newCommentObj: Comment = {
      src: '/assets/images/trading/coment.svg',
      name: 'You',
      day: dateString,
      des: newComment,
      like: '0',
      share: '0',
      replies: [],
    };

    setComments([newCommentObj, ...comments]);
    setNewComment('');
  };

  const handleReply = (index: number) => {
    if (!replyText.trim()) return;

    const updatedComments = [...comments];
    updatedComments[index].replies.push({
      name: 'You',
      des: replyText,
    });

    setComments(updatedComments); // Update the comments state
    setReplyText(''); // Clear the reply text
    setReplyIndex(null); // Hide the reply input after posting
  };

  return (
    <div className="rounded-2xl bg-white p-[30px] shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
      <h3 className="text-base font-semibold text-[#333333] sm:text-xl">
        Your Comment
      </h3>

      <div className="flex items-center gap-2.5 border border-b border-l-0 border-r-0 border-t-0 py-3.5 sm:py-[30px]">
        <ImageComponent
          src="/assets/images/trading/coment.svg"
          // height={60}
          // width={60}
          figClassName="sm:h-[60px] h-[30px] w-[30px] sm:w-[60px]"
          fill
        />
        <input
          name="Comment"
          placeholder="Type here"
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="flex-1 rounded border border-[#ccc] px-3 py-2 text-sm"
        />
        <Button
          className="!h-[30px] !w-[76px] sm:!h-[62px] sm:!w-[207px]"
          onClick={handlePost}
        >
          POST
        </Button>
      </div>

      {comments.map((item, index) => (
        <div
          className="border border-b border-l-0 border-r-0 border-t-0 py-[30px]"
          key={index}
        >
          <div className="flex justify-between">
            <div className="flex items-center gap-2.5">
              <ImageComponent src={item.src} height={30} width={30} />
              <div>
                <p className="text-xs sm:text-base">{item.name}</p>
                <p className="text-[10px] text-[#666666] sm:text-xs">
                  {item.day}
                </p>
              </div>
            </div>
            <RiDeleteBin5Line
              className="cursor-pointer text-red-600"
              onClick={() => {
                const updated = comments.filter((_, i) => i !== index);
                setComments(updated);
              }}
            />
          </div>
          <p className="mt-3 text-xs text-[#666666] sm:text-base">{item.des}</p>

          <div className="mt-3.5 flex items-center gap-5">
            <div className="flex items-center gap-1">
              <Heart className="h-4 w-4 text-[#666666]" />
              <span className="text-[10px] text-[#666666]">{item.like}</span>
            </div>
            <div className="flex items-center gap-1">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">{item.share}</span>
            </div>
            <div
              className="flex cursor-pointer items-center gap-1"
              onClick={() => setReplyIndex(index)}
            >
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">Reply</span>
            </div>
          </div>

          {replyIndex === index && (
            <div className="mt-3">
              <input
                type="text"
                placeholder="Write a reply..."
                className="w-full rounded border border-[#ccc] px-3 py-2 text-sm"
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
              />
              <Button
                className="mt-2"
                onClick={() => handleReply(index)} // post the reply
              >
                Post Reply
              </Button>
            </div>
          )}

          {item.replies.length > 0 && (
            <div className="mt-3">
              {item.replies.map((reply, rIndex) => (
                <div key={rIndex}>
                  <p className="text-sm font-semibold">{reply.name}</p>
                  <p className="text-sm text-[#666666]">{reply.des}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Comments;
