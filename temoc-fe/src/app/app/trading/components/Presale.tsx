import { Ethereum } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';
import Select from '@/components/ui/Select';
import TokenTable from '@/components/ui/TokenTable';
import React, { useState } from 'react';

const collectionOption = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptions = [
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Trending - Select Time', label: 'Trending - Select Time' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];
const collectionOptionss = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Ranked By', label: 'Ranked By' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];

const columns = [
  { key: 'tokens', label: 'Tokens' },
  { key: 'price', label: 'Price (Softcap)' },
  { key: 'market', label: 'Market Cap' },
  { key: 'launch', label: 'Launch' },
  { key: 'change', label: 'Change 24h (Hardcap)' },
];
const Tabledata = [
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
  {
    tokens: (
      <div className="flex items-center gap-1">
        <Ethereum />
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base">Jon Doe Coin</p>
      </div>
    ),
    price: '$20B USDT',
    market: 'Presale',
    launch: 'In 1 month',
    change: '$30B USDT',
  },
];

const LiveToken = () => {
  const [selectedHour, setSelectedHour] = useState(collectionOption[0]);
  const [selectedTime, setSelectedTime] = useState(collectionOptions[1]);
  const [selectedRank, setSelectedRank] = useState(collectionOptionss[2]);

  const visibleColumnKeys = ['market', 'launch', 'change'];
  return (
    <>
      <div className="pb-5">
        <div className="mt-2.5 flex flex-wrap gap-2.5">
          <Select
            options={collectionOption}
            selected={selectedHour}
            onSelect={setSelectedHour}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
          <Select
            options={collectionOptions}
            selected={selectedTime}
            onSelect={setSelectedTime}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
          <Select
            options={collectionOptionss}
            selected={selectedRank}
            onSelect={setSelectedRank}
            placeholder=""
            className="!rouned-[6px] w-full !bg-white sm:max-w-[200px]"
          />
        </div>
        <div className="mt-2.5 hidden sm:block">
          <TokenTable columns={columns} bg={true} data={Tabledata} />
        </div>
        <div className="mt-5 block cursor-pointer space-y-3 sm:hidden">
          {Tabledata.map((items: any, index) => (
            <div
              className="bg-white p-2.5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
              key={index}
            >
              <div className="flex items-center justify-between font-semibold">
                <span>{items.tokens}</span>
                <span>{items.price}</span>
              </div>
              <div className="mt-2 flex items-center justify-between text-sm text-gray-700">
                {columns
                  .filter((col) => visibleColumnKeys.includes(col.key))
                  .map((col, idx) => (
                    <div className="" key={idx}>
                      <div className="text-xs">{col.label}</div>
                      <div className="mt-1 text-primary">
                        {items[col.key] ?? '—'}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
        <p className="mt-12 text-center text-sm text-[#666666]">
          © 2025 TEMOC • All Rights Reserved
        </p>
      </div>
    </>
  );
};

export default LiveToken;
