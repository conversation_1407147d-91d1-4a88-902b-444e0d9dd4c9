import {
  FaSpotify,
  FaYoutube,
  <PERSON>a<PERSON>ik<PERSON>,
  F<PERSON><PERSON><PERSON><PERSON>ram,
  Fa<PERSON><PERSON><PERSON>,
} from 'react-icons/fa6';
import { MdVerified } from 'react-icons/md';
import { IoPlay } from 'react-icons/io5';
import { FaDollarSign, FaCoins } from 'react-icons/fa';
import { Button } from '@/components/common';
import UniSwap from '@/components/common/Icons/UniSwap';
import Taj from '@/components/common/Icons/Taj';
import ImageComponent from '@/components/common/ImageComponent';
import { IoIosArrowBack } from 'react-icons/io';
import Dropdown from '@/components/ui/Dropdown';

interface Iprops {
  setexploreDetail?: any;
}

export default function BillieEilishTokenCard({ setexploreDetail }: Iprops) {
  return (
    <>
      <div>
        <div className="Atbg relative flex w-full flex-col flex-wrap items-start justify-between bg-[url(/assets/images/artist/bg.png)] pt-4 sm:h-[300px] sm:p-2.5 md:flex-row lg:items-end lg:pt-0">
          <div className="absolute left-2 top-2 hidden lg:block">
            <Button
              className="!h-[43px] !w-[113px] !border-none !bg-white !text-sm !text-primary"
              onClick={() => setexploreDetail(false)}
            >
              <IoIosArrowBack
                className="text-[#181818]"
                onClick={() => setexploreDetail(false)}
              />{' '}
              Back
            </Button>
          </div>
          <IoIosArrowBack className="block text-xl font-bold text-white lg:hidden" />
          <div className="flex flex-row gap-2 pl-2 sm:gap-4 sm:pl-6">
            <div className="flex flex-col items-center justify-center rounded-full">
              <ImageComponent
                src="/assets/images/artist/billie.png"
                alt="Billie"
                // width={160}
                // height={160}
                figClassName="sm:h-[160px] sm:w-[160px] h-20 w-20"
                fill
              />
              <div className="mt-2 flex gap-1 text-xs text-white sm:gap-3">
                <FaYoutube />
                <FaSpotify />
                <FaInstagram />
                <FaXTwitter />
                <FaTiktok />
              </div>
            </div>
            <div>
              <div className="flex items-center gap-1 text-xs text-white">
                <MdVerified className="text-blue-500" />
                Verified Artist
              </div>
              <h3 className="text-xl font-semibold text-white sm:mt-1 sm:text-start sm:text-5xl xl:text-[80px]">
                Billie Eilish Token
              </h3>

              <div className="flex flex-wrap items-center gap-3.5 sm:mt-6">
                <p className="text-xs text-white">Followed by 2.1M fans</p>
                <div className="flex flex-nowrap items-center justify-center gap-1 sm:flex-wrap sm:gap-2.5 xs:justify-start xs:gap-2 xs1:flex-wrap">
                  <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary sm:h-[30px] sm:w-[30px]">
                    <IoPlay className="text-[8px] text-white" />
                  </div>
                  <Button
                    variant="outline"
                    className="!h-[22px] !w-[85px] !text-[10px] sm:!h-[36px] sm:!w-[142px] sm:!text-xs xs:!w-[100px]"
                  >
                    JOIN FAN CLUB
                  </Button>
                  <Button className="!h-[22px] !w-[85px] !text-[10px] uppercase sm:!h-[36px] sm:!w-[166px] sm:!text-xs xs:!w-[100px]">
                    Get $BILLIE Coins
                  </Button>
                  <Button className="!h-[22px] !w-[65px] !text-[10px] uppercase sm:!h-[36px] sm:!w-[126px] sm:!text-xs xs:!w-[100px]">
                    Send a Gift
                  </Button>
                  <Dropdown />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 flex w-full flex-row items-center justify-between rounded-md bg-black px-5 py-2.5 sm:mt-0 sm:hidden sm:w-auto sm:space-y-2.5 sm:bg-transparent md:flex-col xl:flex">
            <p className="flex items-center gap-0.5 !text-[10px] text-white sm:!text-xs">
              <FaCoins className="text-[#FF6E00]" />
              Bruno Token
              <span className="ml-6 text-[10px] font-medium text-[#FF6E00] sm:text-base">
                2.9k holders
              </span>
            </p>
            <p className="flex items-center gap-0.5 !text-[10px] text-white sm:!text-xs">
              <FaDollarSign className="text-[#FF6E00]" />
              Current Price:{' '}
              <span className="ml-6 text-[10px] font-medium text-[#FF6E00] sm:text-base">
                $0.013
              </span>
            </p>
          </div>
        </div>
        <div className="xs1-gap-2 flex w-full items-center justify-between bg-gradient-to-l from-[#FE2181] to-[#FE5F5E] px-5 py-2.5 sm:gap-5 sm:px-7 lg:gap-0 xs1:flex-wrap">
          <p className="flex items-center justify-center gap-2 !text-[10px] font-medium text-white sm:!text-base">
            <Taj /> Buy Billie Coin now to unlock premium content & early
            supporter rewards.
          </p>
          <div className="flex w-full justify-center gap-1 sm:gap-2.5 lg:w-auto">
            <Button className="!hover:none group flex !h-[30px] !w-[108px] items-center justify-center !border-none bg-white !text-[10px] !text-[#FF37C7] hover:!text-white sm:!h-[46px] sm:!w-[199px] sm:!text-sm">
              <span className="text-primary group-hover:text-white">
                BUY ON
              </span>{' '}
              <UniSwap />
              <span className="text-[#FF37C7] group-hover:text-white">
                Uniswap
              </span>
            </Button>
            {/* <Button className="flex !h-[30px] !w-[131px] items-center justify-center !border-none bg-white !text-[10px] !text-primary sm:!h-[46px] sm:!w-[240px] sm:!text-sm">
              <span className="group-hover:text-white">BUY ON</span>
              <span className="text-black">DexSCREENER</span>
            </Button> */}
          </div>
        </div>
      </div>
    </>
  );
}
