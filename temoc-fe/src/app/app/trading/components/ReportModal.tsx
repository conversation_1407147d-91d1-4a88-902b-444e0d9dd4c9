'use client';
import Button from '@/components/common/Button';
import SelectComponent from '@/components/common/Forms/Select';
import React, { useState } from 'react';
import SongReport from './SongReport';
import ArtistReport from './ArtistReport';
const collectionOptions = [
  { value: 'Song Report', label: 'Song Report' },
  { value: 'Artist Report', label: 'Artist Report' },
];
const ReportModal = ({ setCreating }: any) => {
  const [selectType, setSelectType] = useState<{
    value: string;
    label: string;
  } | null>(collectionOptions[0]);

  return (
    <form className="pb-10 pt-6 lg:px-10">
      <h3 className="mb-5 text-center text-2xl font-semibold text-[#333333] sm:text-[30px]">
        Report
      </h3>

      <SelectComponent
        //   {...field}
        options={collectionOptions}
        selected={selectType}
        onSelect={setSelectType}
        placeholder="Report Type"
        className="w-full"
      />
      {selectType?.value === 'Song Report' ? <SongReport /> : <ArtistReport />}

      <div className="mt-5 grid grid-cols-2 justify-between gap-4">
        <Button
          variant="outline"
          className="!h-12 w-full uppercase"
          onClick={() => setCreating(false)}
        >
          Cancle
        </Button>
        <Button
          type="submit"
          className="!h-12 w-full uppercase"
          arrow
          //   disabled={libraryMutation.isPending}
          //   isLoading={libraryMutation.isPending}
        >
          SEND
        </Button>
      </div>
    </form>
  );
};

export default ReportModal;
