'use client';
import Button from '@/components/common/Button';
import ImageComponent from '@/components/common/ImageComponent';
import TokenTable from '@/components/ui/TokenTable';
import React, { useRef, useState } from 'react';
import { Heart, Share2, MessageCircle } from 'lucide-react';
import TopFans from './TopFans';
import Comments from './Comments';
import { IoPause } from 'react-icons/io5';
import { FaPlay } from 'react-icons/fa6';
import MobileLibraryItem from '@/components/ui/MobileLibrary';
import TokenContent from '../Presale/TokenContent';

const columns = [
  { key: 'popular', label: 'Popular' },
  { key: 'plays', label: 'Plays' },
  { key: 'album', label: 'Album' },
  { key: 'duration', label: 'Duration' },
  { key: 'share', label: 'Share' },
];
interface Iprops {
  explore?: boolean;
}
const SocialLinks = ({ explore }: Iprops) => {
  const [activeTab, setActiveTab] = useState('media');
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(false);
  console.log(explore);
  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
      setShowControls(true);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    // Optional: setShowControls(false);
  };
  const Tabledata = [
    {
      popular: (
        <div className="flex flex-col items-center gap-2 sm:flex-row">
          1
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Freak In Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          2
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg2.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Baby One More Time</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          3
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg3.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Show Me How</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          4
          <ImageComponent
            src="/assets/images/trading/tableimg.svg"
            height={48}
            width={48}
          />
          <p className="text-sm">Freak In Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          5
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg4.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">Baby Powder</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          6
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg5.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">You & Me</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
    {
      popular: (
        <div className="flex items-center gap-2">
          7
          <div
            className="group relative w-fit cursor-pointer"
            onClick={togglePlay}
          >
            <ImageComponent
              src="/assets/images/trading/tableimg6.svg"
              height={48}
              width={48}
            />
            <div className="absolute left-1/2 top-1/2 hidden h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary group-hover:flex">
              {isPlaying ? (
                <IoPause className="text-[12px] text-white" />
              ) : (
                <FaPlay className="text-[10px] text-white" />
              )}
            </div>
          </div>
          <p className="text-sm">That&apos;s what i like</p>
        </div>
      ),
      plays: '6,146,935',
      album: 'Album Name Here',
      duration: '03:17',
      share: (
        <div className="flex">
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-center">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-[10px] text-[#666666]">25K</span>
            </div>
            <div className="flex flex-col items-center">
              <Share2 className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">125K</span>
            </div>
            <div className="flex flex-col items-center">
              <MessageCircle className="h-4 w-4" />
              <span className="text-[10px] text-[#666666]">100</span>
            </div>
            <Button className="h-[27px] !text-[10px]">Add To Wishlist</Button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="mx-auto mt-12 hidden w-full max-w-[194px] items-center justify-between rounded-[10px] bg-white p-5 text-sm shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:flex">
        <p
          className={`cursor-pointer text-sm ${
            activeTab === 'media' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('media')}
        >
          Media
        </p>
        <div className="h-4 w-0.5 border border-black bg-black"></div>
        <p
          className={`cursor-pointer text-sm ${
            activeTab === 'comments' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('comments')}
        >
          Comments
        </p>
      </div>

      {/* media div */}
      {activeTab === 'media' && (
        <div
          className={`relative mt-12 flex flex-col justify-between gap-10 xl:flex-row xl:gap-28`}
        >
          <div className="relative w-full">
            <div className={`relative hidden w-full blur-md sm:block`}>
              <TokenTable columns={columns} data={Tabledata} bg={true} />
              <audio
                ref={audioRef}
                src="/assets/audio.mp3"
                controls={showControls}
                onEnded={handleEnded}
                className={`mt-4 w-full ${showControls ? 'block' : 'hidden'}`}
              />
            </div>
            <div className={`block blur-md sm:hidden`}>
              {Tabledata.map((song, idx) => (
                <MobileLibraryItem
                  key={idx}
                  index={idx} // use the map index here
                  track={song}
                />
              ))}
              <audio
                ref={audioRef}
                src="/assets/audio.mp3"
                controls={showControls}
                onEnded={handleEnded}
                className={`mt-4 w-full ${showControls ? 'block' : 'hidden'}`}
              />
            </div>
            <div className="absolute top-0 z-20 flex h-full w-full items-center justify-center">
              <TokenContent />
            </div>
          </div>
          <div className="hidden sm:block">
            <TopFans />
          </div>
        </div>
      )}

      {/* comments div */}
      {activeTab === 'comments' && (
        <div className="mt-12">
          <Comments />
        </div>
      )}
    </>
  );
};

export default SocialLinks;
