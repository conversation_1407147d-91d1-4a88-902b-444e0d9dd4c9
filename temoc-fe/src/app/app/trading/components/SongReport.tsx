'use client';
import { Input } from '@/components/common/Forms/Input';
import { Textarea } from '@/components/common/Forms/TextArea';
// import NoCollection from '@/components/common/Icons/NoCollection';
import Upload from '@/components/common/Icons/Upload';
import ImageComponent from '@/components/common/ImageComponent';
import React, { useRef, useState } from 'react';

const SongReport = () => {
  const inputRef = useRef<HTMLInputElement | null>(null);

  // const [thumbnailName, setThumbnailName] = useState<string | null>(null);
  // const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [thumbnailName] = useState<string | null>(null);
  const [thumbnailFile] = useState<File | null>(null);

  const thumbnailPreview = thumbnailFile
    ? URL.createObjectURL(thumbnailFile)
    : '/assets/images/admin/avatar.png';
  return (
    <form>
      <div className="space-y-4">
        <h3 className="pt-7 text-center text-2xl font-semibold text-[#333333]">
          Song Report
        </h3>
        <Input placeholder="Song Name" name="title" />

        <Textarea
          //   register={register}
          name={'description'}
          placeholder="Description"
          className="h-[150px]"
        />

        <label className="flex h-[102px] w-[146px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
          <h3 className="max-w-[170px] truncate text-center text-xs text-[#777777]">
            {thumbnailName || 'Upload Screenshot'}
          </h3>
          <Upload />
          <div>
            <p className="text-center text-xs text-[#666666]">{'Upload'}</p>
          </div>
          <input
            type="file"
            accept="image/*"
            ref={inputRef}
            // onChange={handleThumbnailChange}
            className="hidden"
          />
        </label>

        {thumbnailFile && (
          <div className="m-auto mt-5 max-w-[690px] bg-white p-5">
            <p className="text-sm text-[#333333]">Preview</p>
            <div className="mt-5 flex flex-wrap gap-2 2xl:flex-nowrap">
              <ImageComponent
                src={thumbnailPreview}
                figClassName="h-[102px] w-[146px] flex-shrink-0 rounded-lg"
                fill
                alt=""
                priority
                className="cursor-pointer rounded-lg object-cover"
              />
            </div>
          </div>
        )}
      </div>
    </form>
  );
};

export default SongReport;
