import React from 'react';
import TokenTable from '@/components/ui/TokenTable';

const columns = [
  { key: 'type', label: 'Type' },
  { key: 'usd', label: 'USD' },
  { key: 'date', label: 'Date' },
  { key: 'acc', label: 'ACC' },
  { key: 'eth', label: 'ETH' },
  { key: 'price', label: 'Price' },
];

const data = [
  {
    type: 'Buy',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Sell',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Buy',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Sell',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Buy',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Sell',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Buy',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
  {
    type: 'Sell',
    usd: '13.07',
    date: '1s ago',
    acc: '151,000',
    eth: '0.07',
    price: '$0.023',
  },
];

const Table = () => {
  return (
    <div className="p-6">
      <TokenTable columns={columns} data={data} bg={true} />
    </div>
  );
};

export default Table;
