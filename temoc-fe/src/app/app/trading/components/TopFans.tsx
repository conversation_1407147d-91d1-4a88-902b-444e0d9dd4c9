import Button from '@/components/common/Button';
import Youtube from '@/components/common/Icons/Youtube';
import Facebook from '@/components/common/Icons/Facebook';
import X from '@/components/common/Icons/X';
import TikTok from '@/components/common/Icons/TikTok';
import Spotify from '@/components/common/Icons/Spotify';
import React from 'react';
import ImageComponent from '@/components/common/ImageComponent';
import Position from '@/components/common/Icons/Position';
import Link from 'next/link';
import Insta from '@/components/common/Icons/Insta';

const fans = [
  {
    id: 1,
    name: 'Fan 1',
    image: '/assets/images/trading/fan1.svg',
  },
  {
    id: 2,
    name: 'Fan 2',
    image: '/assets/images/trading/fan2.svg',
  },
  {
    id: 3,
    name: 'Fan 3',
    image: '/assets/images/trading/fan3.svg',
  },
  {
    id: 4,
    name: 'Fan 4',
    image: '/assets/images/trading/fan4.svg',
  },
  {
    id: 5,
    name: 'Fan 5',
    image: '/assets/images/trading/fan5.svg',
  },
];
const TopFans = () => {
  return (
    <>
      <div className="mt-3.5 flex flex-row flex-nowrap justify-between gap-5 pb-5 sm:mt-0 sm:flex-wrap sm:gap-0 sm:pb-0 xl:flex-col">
        <div className="w-[185px] sm:w-auto">
          <p className="text-sm">Social Links</p>
          <div className="mt-5 grid grid-cols-3 sm:flex sm:flex-col sm:gap-0 sm:space-y-5">
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <Insta width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <Youtube width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <Spotify width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <Facebook width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <X width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
            <div className="flex flex-col items-center gap-1 sm:flex-row sm:gap-2">
              <TikTok width="24px" height="24px" />
              <span className="text-[10px] sm:text-sm">@billieeilish</span>
            </div>
          </div>
          <Button className="!mt-8 hidden !h-[42px] !w-[173px] !py-0 sm:block">
            JOIN PRESALE
          </Button>
        </div>

        <div className="flex flex-col gap-2.5 sm:gap-0">
          <Button className="block !h-[32px] !w-[144px] sm:hidden">
            JOIN PRESALE
          </Button>
          <Link href="/app/management" className="mt-0 sm:mt-10">
            <div className="flex h-max flex-col items-start gap-2.5 rounded-[10px] bg-white px-5 py-2.5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
              <div className="mx-auto flex items-center gap-2">
                <span className="text-xl font-bold text-yellow-500">
                  <Position />
                </span>
                <h3 className="text-sm font-medium sm:text-base">Top Fans</h3>
              </div>
              <div className="mx-auto flex items-center space-x-[-16px]">
                {fans.slice(0, 4).map((fan) => (
                  <div key={fan.id} className="relative">
                    <ImageComponent
                      src={fan.image}
                      alt={fan.name}
                      // height={40}
                      // width={40}
                      figClassName="sm:h-10 h-5 w-5 sm:w-10"
                      fill
                      className="rounded-full border-2 border-white shadow-md"
                    />
                  </div>
                ))}
                {fans.length > 4 && (
                  <div className="flex h-5 w-5 items-center justify-center rounded-full border-2 border-white bg-gray-300 text-gray-600 shadow-md sm:h-10 sm:w-10">
                    +{fans.length - 4}
                  </div>
                )}
              </div>
              <Button className="mx-auto !border-none !bg-transparent !px-0 !py-0 !text-xs !text-primary">
                View All <span className="text-[#666666]">→</span>
              </Button>
            </div>
          </Link>
        </div>
      </div>
    </>
  );
};

export default TopFans;
