'use client';
import React, { useState } from 'react';
import LiveToken from './LiveToken';
import Presale from './Presale';

const Trading = () => {
  const [activeTab, setActiveTab] = useState('Live Tokens');
  return (
    <>
      <div className="bg-white py-5 shadow-[0_4px_10px_rgba(0,0,0,0.08)]">
        <div className="mx-auto flex w-full max-w-[165px] items-center justify-between text-sm">
          <p
            className={`cursor-pointer text-sm font-semibold ${
              activeTab === 'Live Tokens' ? 'text-primary' : 'text-black'
            }`}
            onClick={() => setActiveTab('Live Tokens')}
          >
            Live Tokens
          </p>
          <div className="h-4 w-0.5 border border-black bg-black"></div>
          <p
            className={`cursor-pointer text-sm font-semibold ${
              activeTab === 'Presale' ? 'text-primary' : 'text-black'
            }`}
            onClick={() => setActiveTab('Presale')}
          >
            Presale
          </p>
        </div>
      </div>
      {activeTab === 'Live Tokens' && (
        <div>
          <div className="">
            <LiveToken />
          </div>
        </div>
      )}
      {/* comments div */}
      {activeTab === 'Presale' && (
        <div>
          <Presale />
        </div>
      )}
    </>
  );
};

export default Trading;
