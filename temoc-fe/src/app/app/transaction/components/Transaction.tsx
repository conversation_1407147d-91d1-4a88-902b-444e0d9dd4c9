'use client';
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { transactionsService } from '@/services/transaction.service';
import { formatNumber, formatTimestamp } from '@/lib/utils';
import Search from '@/components/ui/Search';
import { PiCopySimpleLight } from 'react-icons/pi';
// import { useChainId } from 'wagmi';
// import { supportedChains } from '@/utils/chains';
import Table from '@/components/common/Table';
import TruncateAddress from '@/components/ui/TruncateAddress';
const columns = [
  { header: 'Date', accessor: 'date' },
  { header: 'Email', accessor: 'email' },
  { header: 'Tokens', accessor: 'tokens' },
  { header: 'Tx Hash', accessor: 'hash' },
  { header: 'Wallet Address', accessor: 'walletAddress' },
  { header: 'Purchase', accessor: 'purchase' },
  { header: 'Amount Paid', accessor: 'amountPaid' },
  // { header: 'Amount in USD', accessor: 'amountUsd' },
];

const Transaction = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [copy, setCopy] = useState<string | null>(null);
  // const chainId = useChainId();
  const [filters, setFilters] = useState({
    searchQuery: '',
  });

  const queryFilters = {
    search: filters.searchQuery,
    limit,
    page,
  };

  const { data, isLoading } = useQuery({
    queryKey: ['transactions', queryFilters],
    queryFn: () => transactionsService.getFanTransactions(queryFilters),
  });

  const transactions = data?.data?.data || [];
  const copyToClipboard = (text: any, id: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopy(id);
      setTimeout(() => {
        setCopy(null);
      }, 1000);
    });
  };
  const TableData = transactions?.map((transaction: any) => {
    return {
      date: formatTimestamp(transaction?.timestamp),
      tokens: transaction?.tokens ? formatNumber(transaction.tokens, 4) : '-',
      walletAddress: (
        <>
          {transaction?.address ? (
            <>
              <TruncateAddress address={transaction?.address} />
              <button
                onClick={() =>
                  copyToClipboard(transaction?.address, transaction?._id)
                }
                className="ml-2 cursor-pointer text-blue-500"
                title="Copy address"
              >
                {copy === transaction?._id ? (
                  <span className="text-green-500">Copied</span>
                ) : (
                  <span className="text-blue-500">
                    <PiCopySimpleLight />
                  </span>
                )}
              </button>
            </>
          ) : (
            '-'
          )}
        </>
      ),
      email: transaction?.email || '-',
      purchase:
        transaction?.type === 'blockchain' ? 'Crypto Payment' : 'Card Payment ',
      hash: (
        <>
          {transaction?.txHash ? (
            <a
              rel="noreferrer"
              target="_blank"
              href={`https://basescan.org/tx/${transaction?.txHash}`}
              className="mt-6 cursor-pointer text-center text-base text-primary sm:text-xl"
            >
              <TruncateAddress address={transaction?.txHash} />
            </a>
          ) : (
            '-'
          )}
        </>
      ),

      stage: transaction?.stage == 0 && 'Presale',
      amountPaid: (
        <div className="flex items-center gap-1 text-black">
          {transaction?.quote == 'ethereum' ? 'ETH' : '$'}
          <p className="mt-1 leading-none">{transaction?.amount || '-'}</p>
        </div>
      ),
    };
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ ...filters, searchQuery: e.target.value });
  };

  const handlePageChange = (page: number) => {
    setPage(page);
  };
  const initialPage = data?.data?.data?.page || 1;
  const totalPages = data?.data?.data?.totalPages || 0;

  return (
    <>
      <div className="px-5">
        <div className="flex justify-between gap-4">
          <p className="text-[22px] font-normal">All Transactions</p>
          <div className="relative">
            <Search
              value={filters.searchQuery}
              onChange={handleInputChange}
              className="w-full sm:w-[320px]"
              placeholder="Search by address"
            />
          </div>
        </div>
        <Table
          noDataMessage="No Transaction Available"
          data={TableData || []}
          columns={columns}
          lastRowColumnCount={5}
          loading={isLoading}
          onPageChange={handlePageChange}
          initialPage={initialPage}
          totalPages={totalPages}
        />
      </div>
    </>
  );
};

export default Transaction;
