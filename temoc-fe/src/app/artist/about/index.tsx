import { Input } from '@/components/common/Forms/Input';
import React, { useEffect, useRef } from 'react';
import { FaRegEdit } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ArtistFormData, ArtistProfileRes } from '@/types/user.interface';
import { userService } from '@/services/user.service';
import { useForm, useWatch } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ArtistProfileSchema } from '@/utils/schema';
import InputError from '@/components/common/Forms/InputError';
import { Textarea } from '@/components/common/Forms/TextArea';
import { Button } from '@/components/common';
import SocialMedia from '@/components/ui/UpdateSocialMedia/SocialMedia';

// import Link from 'next/link';

const About = () => {
  const { data } = useQuery<ArtistProfileRes>({
    queryKey: ['artist-profile'],
    queryFn: () => userService.getArtistProfile(),
  });

  const ArtistData = data?.data;

  console.log(ArtistData, 'ArtistData');

  const queryClient = useQueryClient();

  // Initialize react-hook-form
  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<ArtistFormData>({
    resolver: yupResolver(ArtistProfileSchema),
    mode: 'all', // validate on change and blur
  });

  // Store initial values for comparison
  const initialValuesRef = useRef<ArtistFormData | null>(null);

  useEffect(() => {
    if (ArtistData) {
      const initialValues = {
        firstName: ArtistData.firstName || '',
        lastName: ArtistData.lastName || '',
        username: ArtistData.username || '',
        email: ArtistData.email || '',
        bio: ArtistData.bio || '',
      };
      initialValuesRef.current = initialValues;

      setValue('firstName', initialValues.firstName);
      setValue('lastName', initialValues.lastName);
      setValue('username', initialValues.username);
      setValue('email', initialValues.email);
      setValue('bio', initialValues.bio);
    }
  }, [ArtistData, setValue]);

  // Watch all fields for changes
  const watchedValues = useWatch({ control });

  // Function to check if form has changes compared to initial values
  const isFormChanged = () => {
    if (!initialValuesRef.current) return false;
    const initial = initialValuesRef.current;
    return (
      initial.firstName !== watchedValues.firstName ||
      initial.lastName !== watchedValues.lastName ||
      initial.username !== watchedValues.username ||
      initial.email !== watchedValues.email ||
      initial.bio !== watchedValues.bio
    );
  };

  const disableSaveButton = !isFormChanged();

  const updateMutation = useMutation({
    mutationFn: userService.updateArtistData,
    onSuccess: (data) => {
      const response = data?.data;
      toast.success(response?.message);
      queryClient.invalidateQueries({ queryKey: ['artist-profile'] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  // Submit handler
  const onSubmit = async (formData: ArtistFormData) => {
    try {
      await updateMutation.mutateAsync(formData);
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  return (
    <>
      <div className="">
        <h3 className="text-[22px] font-normal">ABOUT</h3>
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="mx-auto mt-7 w-full max-w-[1040px] rounded-[20px] bg-white p-5 shadow-[0_4px_10px_rgba(0,0,0,0.08)] sm:p-10"
          noValidate
        >
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-12">
            <div className="relative flex flex-col">
              <div className="flex items-center justify-between px-1">
                <p className="font-bold">First Name</p>
                <label htmlFor="F_Name">
                  {' '}
                  <FaRegEdit className="cursor-pointer text-base" />
                </label>
              </div>
              <Input
                placeholder="First Name"
                name="firstName"
                id="F_Name"
                register={register}
                className="mt-1 w-full !rounded-md !pl-3 !font-normal"
              />
              <InputError error={errors.firstName?.message} />
            </div>

            <div className="flex flex-col">
              <div className="flex w-full items-center justify-between">
                <p className="font-bold">Last Name</p>
                <label htmlFor="L_Name">
                  <FaRegEdit className="mt-1 block cursor-pointer text-base" />
                </label>
              </div>
              <Input
                placeholder="Username"
                name="lastName"
                id="L_Name"
                register={register}
                className="mt-1 w-full !rounded-md !pl-3 !font-normal"
              />

              <InputError error={errors.lastName?.message} />
            </div>
          </div>
          <div className="my-4 grid grid-cols-1 gap-4 sm:my-5 sm:gap-12 md:grid-cols-2">
            <div className="relative flex flex-col">
              <div className="flex items-center justify-between px-2">
                <p className="font-bold">Username</p>
                <label htmlFor="User_Name">
                  <FaRegEdit className="mt-1 block cursor-pointer text-base" />
                </label>
              </div>
              <Input
                placeholder="Username"
                name="username"
                id="User_Name"
                register={register}
                className="mt-1 w-full !rounded-md !pl-3 !font-normal"
              />
              <InputError error={errors.username?.message} />
            </div>
            <div className="flex flex-col">
              <div className="flex w-full items-center justify-between px-1">
                <p className="font-bold">Email</p>
              </div>
              <Input
                placeholder="Email"
                name="email"
                readOnly
                register={register}
                className="mt-1 w-full !rounded-md !pl-3 !font-normal focus:!border-[#CECECE]"
              />
              <InputError error={errors.email?.message} />
            </div>
          </div>
          <Textarea
            placeholder="Description"
            name="bio"
            register={register}
            className="!rounded-md !p-3 !font-normal"
          />

          <SocialMedia />

          <div className="mt-5 flex justify-center sm:mt-8 sm:justify-end">
            <Button
              isLoading={isSubmitting}
              type="submit"
              disabled={isSubmitting || disableSaveButton}
              className=""
            >
              Save Changes
            </Button>
          </div>
        </form>

        <p className="mt-12 text-center text-sm text-[#666666]">
          © 2025 TEMOC • All Rights Reserved
        </p>
      </div>
    </>
  );
};

export default About;
