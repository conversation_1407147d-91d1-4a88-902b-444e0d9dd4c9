'use client';
import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Input } from '@/components/common/Forms/Input';
import SwitchButton from '@/components/ui/SwitchButton';
import { Button } from '@/components/common';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';
import ImageComponent from '@/components/common/ImageComponent';

interface Iprops {
  removebg?: boolean;
  library: any;
  setOpenModal: any;
}

const AccessManagementModal = ({ removebg, library, setOpenModal }: Iprops) => {
  const [enabled, setEnabled] = useState(false);
  const [selectedAlbums, setSelectedAlbums] = useState<string[]>([]);
  const [tokenMap, setTokenMap] = useState<{ [key: string]: string }>({});
  const [globalTokenValue, setGlobalTokenValue] = useState('');

  const { data: allAlbums } = useQuery({
    queryKey: ['albums', library?._id],
    queryFn: () => libraryService.getAlbumsByLibraryId(library._id),
    enabled: !!library?._id,
  });

  const albums = allAlbums?.data?.albums || [];

  const { mutate: updateAccess, isPending } = useMutation({
    mutationFn: (payload: any) =>
      libraryService.updateAlbumAccessControl(payload),
    onSuccess: () => {
      toast.success('Access updated successfully!');
      setOpenModal(false);
    },
    onError: () => {
      toast.error('Failed to update access.');
    },
  });

  const toggleAlbumSelection = (albumId: string) => {
    setSelectedAlbums((prev) =>
      prev.includes(albumId)
        ? prev.filter((id) => id !== albumId)
        : [...prev, albumId],
    );
  };

  const handleSelectAll = () => {
    if (selectedAlbums.length === albums.length) {
      setSelectedAlbums([]);
    } else {
      setSelectedAlbums(albums.map((a: any) => a._id));
    }
  };

  const handleTokenChange = (albumId: string, value: string) => {
    setTokenMap((prev) => ({ ...prev, [albumId]: value }));
  };

  const handleSave = () => {
    const payload = selectedAlbums.map((id) => ({
      albumId: id,
      tokensRequired: Number(tokenMap[id] || 0),
    }));
    updateAccess(payload);
  };

  const applyTokenToAllSelected = () => {
    const updatedMap = { ...tokenMap };
    selectedAlbums.forEach((id) => {
      updatedMap[id] = globalTokenValue;
    });
    setTokenMap(updatedMap);
    toast.success('Token applied to all selected albums!');
  };

  return (
    <div className="mt-12 pb-5">
      <div className="mt-5 w-full">
        <h3 className="text-center text-2xl font-semibold uppercase sm:text-[30px]">
          Access Management
        </h3>
        {albums.length > 1 && (
          <div className="mt-8">
            <h3 className="text-sm font-semibold">
              Apply Token to All Selected Albums
            </h3>
            <div className="mt-2 flex items-center gap-2">
              <Input
                name="globalToken"
                placeholder="Min Tokens"
                type="number"
                value={globalTokenValue}
                onChange={(e) => setGlobalTokenValue(e.target.value)}
              />
              <Button
                className="!h-12"
                onClick={applyTokenToAllSelected}
                disabled={!globalTokenValue || selectedAlbums.length === 0}
              >
                Apply to All
              </Button>
            </div>
            <div className="mt-5 flex items-center justify-between">
              <h3 className="text-sm font-semibold">Select Albums</h3>
              <label className="flex cursor-pointer items-center gap-2 text-sm">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded-sm border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
                  checked={selectedAlbums.length === albums.length}
                  onChange={handleSelectAll}
                />
                Select All
              </label>
            </div>
          </div>
        )}

        {/* Apply to All Token Section */}

        <div className="hideScrollbar mt-4 max-h-[500px] w-full space-y-3 overflow-y-auto bg-white pl-2">
          {albums.map((album: any) => (
            <div
              key={album?._id}
              className="flex flex-wrap items-center gap-2 sm:gap-4 xl:flex-nowrap"
            >
              <input
                type="checkbox"
                className="h-4 w-4 rounded-sm border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
                checked={selectedAlbums.includes(album?._id)}
                onChange={() => toggleAlbumSelection(album?._id)}
              />
              <ImageComponent
                src={album?.thumbnailUrl || '/assets/images/home/<USER>'}
                fill
                figClassName="h-[32px] w-[32px] flex-shrink-0"
                className="w-full flex-shrink-0 rounded-lg object-cover"
                alt="Album Thumbnail"
              />
              <p
                className="w-[200px] truncate text-sm"
                // onClick={() => setOpenModal(false)}
              >
                {album?.title}
              </p>
              <Input
                name=""
                placeholder="Min Tokens"
                type="number"
                value={tokenMap[album?._id] || ''}
                onChange={(e) => handleTokenChange(album?._id, e.target.value)}
              />
            </div>
          ))}
        </div>

        <label
          className={`mt-5 w-full flex-col gap-2 bg-white ${
            removebg ? '' : 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]'
          }`}
        >
          <div className="mt-5 flex w-full items-center justify-between">
            <h3 className="text-sm font-semibold">Enable Gated Access</h3>
            <SwitchButton
              enable={enabled}
              onChange={setEnabled}
              disabled={false}
              loading={false}
            />
          </div>
        </label>

        <div className="mt-5 flex w-full flex-wrap justify-between gap-4">
          <Button
            className="!h-12 w-full !border-none !bg-[#999999] !text-white sm:!h-[62px] sm:!w-[200px]"
            onClick={() => setOpenModal(false)}
          >
            CANCEL
          </Button>
          <Button
            className="!h-12 w-full sm:!h-[62px] sm:!w-[200px]"
            onClick={handleSave}
            disabled={isPending}
            isLoading={isPending}
          >
            SAVE
          </Button>
        </div>
      </div>

      {!removebg && (
        <p className="mt-12 text-center text-sm text-[#666666]">
          © 2025 TEMOC • All Rights Reserved
        </p>
      )}
    </div>
  );
};

export default AccessManagementModal;
