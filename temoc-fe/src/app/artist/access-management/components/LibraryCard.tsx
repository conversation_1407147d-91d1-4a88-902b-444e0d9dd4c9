import ImageComponent from '@/components/common/ImageComponent';
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import AccessManagementModal from './AccessManagementModal';
import Modal from '@/components/common/Modal';

interface LibraryCardProps {
  library: any;
}
const AccessManagementLibraryCard = ({ library }: LibraryCardProps) => {
  const [openModal, setOpenModal] = useState(false);
  const { data: allAlbums } = useQuery({
    queryKey: ['albums', library?._id],
    queryFn: () => libraryService.getAlbumsByLibraryId(library?._id),
    enabled: !!library?._id,
  });

  const albums = allAlbums?.data?.albums;
  return (
    <div className="group relative rounded-[20px] shadow-lg">
      <div
        className="m-auto w-full cursor-pointer rounded-[20px] bg-white p-3 sm:p-5"
        onClick={() => setOpenModal(true)}
      >
        <div className="flex flex-col gap-5 xl:flex-row">
          <ImageComponent
            src={library?.thumbnailUrl || '/assets/images/admin/avatar.png'}
            figClassName="h-[120px] !w-full sm:!w-[120px] flex-shrink-0 rounded-lg"
            fill
            alt=""
            priority
            className="cursor-pointer rounded-lg object-cover"
          />
          <div>
            <p
              className="text-base font-medium text-[#333333]"
              onClick={() => setOpenModal(true)}
            >
              {library?.title}
            </p>
            <p
              className="mt-1 text-xs text-[#666666]"
              onClick={() => setOpenModal(true)}
            >
              {library?.description ||
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'}
            </p>
            <div className="mt-2 flex items-center gap-10">
              <div>
                <p className="text-base text-[#666666] xs:text-sm"> Type:</p>
                <p className="text-base text-primary xs:text-sm">
                  {' '}
                  {library?.type}
                </p>
              </div>
              <div>
                <p className="text-base text-[#666666] xs:text-sm"> Genres:</p>
                <p className="text-base text-primary xs:text-sm">
                  {library?.genre}
                </p>
              </div>
              <div>
                <p className="text-base text-[#666666] xs:text-sm">Albums:</p>
                <p className="text-base text-primary xs:text-sm">
                  {albums?.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        show={openModal}
        hide={setOpenModal}
        className="hideScrollbar !h-max !max-w-[720px] !px-5 !pb-6 md:!px-10 md:pb-0"
      >
        <AccessManagementModal
          removebg={true}
          library={library}
          setOpenModal={setOpenModal}
        />
      </Modal>
    </div>
  );
};

export default AccessManagementLibraryCard;
