'use client';
import { libraryService } from '@/services/library.service';
import { useQuery } from '@tanstack/react-query';
import { Index } from 'viem';
import { Button } from '@/components/common';
import Link from 'next/link';
import AccessManagementLibraryCard from './components/LibraryCard';
import { IoIosArrowBack } from 'react-icons/io';

const AccessManagementPage = () => {
  // const [openModal, setOpenModal] = useState(false);
  const { data } = useQuery({
    queryKey: ['All_library'],
    queryFn: libraryService.getAllLibraries,
  });

  const libraries = data?.data?.libraries;
  return (
    <>
      <div className="min-h-screen rounded-bl-lg rounded-tl-lg bg-[#F8F8F8] p-4 sm:p-7">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-semibold uppercase sm:text-xl">
            Access Management
          </h3>
          <Link href={'/artist/initial-dashboard?tab=Home'}>
            <Button
              variant="outline"
              className="flex !h-10 !w-[100px] items-center gap-2"
            >
              <IoIosArrowBack className="text-[#181818]" />
              Back
            </Button>
          </Link>
        </div>
        <div className="mt-5 grid items-center gap-5 sm:grid-cols-2 2xl:grid-cols-3">
          {libraries?.map((item: any, i: Index) => (
            <AccessManagementLibraryCard library={item} key={i} />
          ))}
        </div>
      </div>
    </>
  );
};

export default AccessManagementPage;
