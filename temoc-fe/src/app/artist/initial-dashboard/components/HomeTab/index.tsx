'use client';
import React, { useState } from 'react';
import { SlArrowRight } from 'react-icons/sl';
import {
  CompleteIcon,
  CreateArtistProfile,
  CreateLibrary,
  GatedCommunitySetup,
  TokenCreation,
} from '@/components/common/Icons';
// import Link from 'next/link';
import { IoMdInformationCircleOutline } from 'react-icons/io';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import { useQuery } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { tokenService } from '@/services/token.service';

const HomeTab: React.FC = () => {
  const { user } = useAuth();
  const [, setModalType] = useState<null | 'gated' | 'profile'>(null);

  const openModal = (type: 'gated' | 'profile') => setModalType(type);
  // const closeModal = () => setModalType(null);

  const router = useRouter();

  const handleClick = (url: string, title: string) => {
    if (
      user?.artistProfile?.status === 'pending' &&
      title == 'Token Creation'
    ) {
      toast.info('Your KYC is under review, please wait for approval');
    } else if (
      user?.artistProfile?.status !== 'pending' &&
      title == 'Token Creation'
    ) {
      router.push(url);
    } else {
      router.push(url);
    }
  };
  const handleSecondClick = (url: string, title: string) => {
    // if (user?.artistProfile?.status === 'pending' && title == 'Profile Setup') {
    //   toast.info('Your KYC is under review, please wait for approval');
    // }
    if (title == 'Profile Setup') {
      router.push(url);
    } else if (title == 'Content Access Management') {
      router.push(url);
    } else {
      openModal('gated');
    }
  };
  const { data } = useQuery({
    queryKey: ['All_library'],
    queryFn: libraryService.getAllLibraries,
  });

  const libraries = data?.data?.libraries;

  const { data: tokenData } = useQuery({
    queryKey: ['token'],
    queryFn: () => tokenService.getToken(),
  });

  const tokens = tokenData?.data?.[0];

  const cardData = [
    {
      step: 'Step 01',
      status: libraries?.length > 0 ? 'Completed' : 'Not Completed',
      icon: <CreateLibrary />,
      href: '/artist/initial-dashboard?tab=Library',
      title: 'Create Library',
      description:
        'Share music with cover images. Choose content type and set access as free, subscription, or paid.',
    },
    {
      step: 'Step 02',
      status:
        !tokens || tokenData?.data?.length === 0
          ? 'Not Completed'
          : tokens?.status === 'pending' || tokens?.status === 'presale'
            ? 'In Progress'
            : tokens?.status === 'launched'
              ? 'Completed'
              : 'Not Completed',
      icon: <TokenCreation />,
      href: '/artist/initial-dashboard?tab=Token',
      title: 'Token Creation',
      description:
        'Once your artist profile is verified through KYC, you can create your own custom token to power your fan ecosystem.',
    },
    {
      step: 'Step 03',
      status: 'Not Completed',
      icon: <GatedCommunitySetup />,
      href: '/artist/access-management',
      title: 'Content Access Management',
      description:
        'Set token-based access to unlock exclusive content and perks for your top fans, boosting engagement and token demand.',
    },

    {
      step: 'Step 04',
      status:
        user?.artistProfile?.status === 'pending'
          ? ' In Progress'
          : 'Completed',
      icon: <CreateArtistProfile />,
      href: '/artist/initial-dashboard?tab=About',
      title: 'Profile Setup',
      description:
        'Add your profile image, bio, and social links to set up your artist identity and start building your fanbase.',
    },
  ];
  return (
    <div>
      <div className="bg-[#F8F8F8] px-4 pt-7">
        <div className="relative flex w-full flex-col items-center sm:pt-[50px]">
          <h2 className="text-center font-display !text-xl font-normal leading-none text-[#333333] sm:text-left sm:!text-[22px]">
            Your account is ready! Here’s how to make it yours
          </h2>
          <p className="pt-2.5 text-center text-xs font-normal text-[#666666] sm:text-left sm:text-base">
            Adding some details helps visitors learn more about you and what you
            plan to share here.
          </p>

          <div
            className="mt-7 rounded-[20px] bg-white p-5 sm:mt-[50px] sm:p-10"
            style={{ boxShadow: '0px 0px 20px 0px #0000000' }}
          >
            {/* First two cards */}
            <div className="grid max-w-[1040px] gap-4 sm:grid-cols-2 sm:gap-[30px]">
              {cardData.slice(0, 2).map((item, index) => (
                <div
                  key={index}
                  className={`cursor-pointer border-b border-[#CECECE] pb-4 sm:border-b-0 sm:pb-0 ${index === 0 ? 'border-r-none border-[#CECECE] sm:border-r sm:pr-[30px]' : ''}`}
                  onClick={() => {
                    handleClick(item.href, item.title);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="mb-1 flex w-full items-center justify-between gap-2">
                      <p className="pl-6 text-xs text-primary">{item.step}</p>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-[#666666]">
                          {item.status}
                        </span>
                        {index === 0 ? (
                          libraries?.length > 0 &&
                          item.title === 'Create Library' ? (
                            <CompleteIcon />
                          ) : (
                            <SlArrowRight size={12} className="text-primary" />
                          )
                        ) : index === 1 ? (
                          tokenData?.data?.length > 0 &&
                          tokens?.status === 'launched' ? (
                            <CompleteIcon />
                          ) : (
                            <SlArrowRight size={12} className="text-primary" />
                          )
                        ) : (
                          <SlArrowRight size={12} className="text-primary" />
                        )}
                      </div>
                    </div>
                  </div>
                  <h3 className="mb-1 flex items-center gap-1 font-display text-base font-normal text-[#333333] sm:mb-2">
                    <span className="pr-1">{item.icon}</span> {item.title}
                    <IoMdInformationCircleOutline
                      className="text-[#666666]"
                      size={14}
                    />
                  </h3>

                  <p className="font-display text-sm text-[#666666] sm:mt-1">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
            <hr className="my-[30px] hidden border-[#CECECE] sm:block" />
            {/* Second two cards */}
            <div className="mt-4 grid max-w-[1040px] gap-4 sm:mt-0 sm:grid-cols-2 sm:gap-[30px]">
              {cardData.slice(2, 4).map((item, index) => (
                <div
                  key={index}
                  className={`cursor-pointer ${index === 0 ? 'border-r-none border-b border-[#CECECE] pb-4 sm:border-b-0 sm:border-r sm:pb-0 sm:pr-[30px]' : ''}`}
                  onClick={() => {
                    handleSecondClick(item.href, item.title);
                  }}
                >
                  <div className="mb-1 flex w-full items-center justify-between gap-2">
                    <p className="pl-6 text-xs text-primary">{item.step}</p>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-[#666666]">
                        {item.status}
                      </span>
                      {user?.artistProfile?.status !== 'pending' &&
                      item.title === 'Profile Setup' ? (
                        <CompleteIcon />
                      ) : (
                        <SlArrowRight size={12} className="text-primary" />
                      )}
                    </div>
                  </div>
                  <h3 className="mb-1 flex items-center gap-1 font-display text-base font-normal text-[#333333] sm:mb-2">
                    <span className="pr-1">{item.icon}</span> {item.title}
                    <IoMdInformationCircleOutline
                      className="text-[#666666]"
                      size={14}
                    />
                  </h3>

                  <p className="font-display text-sm text-[#666666] sm:mt-1">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          <p className="my-5 text-center text-sm text-[#666666] sm:mt-10">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      </div>

      {/* Modal for Gated Community Setup */}
      {/* <Modal
        className="hideScrollbar !h-max !max-w-[760px] !px-5 !pb-6 md:pb-0"
        show={modalType === 'gated'}
        hide={closeModal}
      >
        <AccessManagement removebg={true} />
      </Modal> */}

      {/* Modal for Create Artist Profile */}
      {/* <Modal
        className="hideScrollbar !h-max !max-w-[760px] !px-5 !pb-6 md:pb-0"
        show={modalType === 'profile'}
        hide={closeModal}
      >
        <div>
          <h3 className="text-center text-2xl font-semibold uppercase text-[#333333] sm:text-[30px]">
            Edit Profile
          </h3>
          <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
            <Input placeholder="Full Name" name="Full Name" />
            <Textarea name={'Bio'} placeholder="Bio" className="h-[150px]" />
            <label className="flex w-full cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
              <h3 className="text-center text-xs text-[#777777]">
                Set Cover Photo
              </h3>
              <NoCollection />
              <p className="text-sms text-center text-[#333333]">
                {fileName || 'No File Selected Yet'}
              </p>
              <input
                type="file"
                accept="audio/*"
                ref={inputRef}
                onChange={handleFileChange}
                className="hidden"
              />
            </label>
            <label className="flex w-[170px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
              <h3 className="text-center text-xs text-[#777777]">
                Upload Thumbnail
              </h3>
              <Upload />
              <div>
                <p className="text-center text-xs text-[#666666]">
                  {fileName || 'Upload Thumbnail Image'}
                </p>
                <p className="-mt-2 text-center text-[8px] text-[#666666]">
                  900px by 900px Recommended
                </p>
              </div>
              <input
                type="file"
                accept="audio/*"
                ref={inputRef}
                onChange={handleFileChange}
                className="hidden"
              />
            </label>
            <div className="mt-4">
              <SocialLinks />
            </div>
            <div className="mt-8 flex w-full flex-wrap justify-between gap-4 xl:flex-nowrap">
              <Button
                className="!h-12 w-full !border-none !bg-[#999999] !text-white sm:!h-[62px] sm:!w-1/2"
                onClick={closeModal}
              >
                CANCEL
              </Button>
              <Button className="!h-12 w-full sm:!h-[62px] sm:!w-1/2">
                SAVE
              </Button>
            </div>
          </div>
        </div>
      </Modal> */}
    </div>
  );
};

export default HomeTab;
