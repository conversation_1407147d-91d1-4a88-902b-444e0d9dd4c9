import React, { Fragment } from 'react';
import { PeopleAlts } from '@/components/common/Icons';

const tierArray = [
  {
    title: 'Tier 1: Fan',
    tokens: '5-24',
    subtitle: 'Access to basic exclusive content and community chat',
  },
  {
    title: 'Tier 2: Supporter',
    tokens: '25-99',
    subtitle:
      'Access to all content, monthly virtual meetups, and early releases',
  },
  {
    title: 'Tier 3: VIP',
    tokens: '100+',
    subtitle:
      'All benefits plus backstage passes, limited merch, and voting rights on future projects',
  },
];

const FansBenefits: React.FC = () => {
  return (
    <>
      <div className="flex items-center gap-[5px]">
        <PeopleAlts /> <p className="text-lg text-black-300">Fans Benefits</p>
      </div>
      <div className="mt-6 space-y-6">
        {tierArray.map((item, index) => {
          return (
            <div
              key={index}
              className="rounded-[10px] border border-[#CECECE] p-[15px]"
            >
              <div className="flex items-center justify-between">
                <p className="leading-none text-black-300">{item.title}</p>
                <p className="leading-none text-primary">
                  {item.tokens} Tokens
                </p>
              </div>
              <p className="mt-2.5 leading-none text-black-600">
                {item.subtitle}
              </p>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default FansBenefits;
