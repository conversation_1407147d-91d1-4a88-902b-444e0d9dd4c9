import React, { Fragment } from 'react';
import { MusicCard, Podcast, Received, Video } from '@/components/common/Icons';
import TokenTable from '@/components/ui/TokenTable';

const columns = [
  { key: 'cover', label: 'Cover' },
  { key: 'title', label: 'Title' },
  { key: 'type', label: 'Type' },
  { key: 'views', label: 'Views' },
  { key: 'tips', label: 'Tips' },
  { key: 'accessType', label: 'Access Type' },
];

const Tabledata = [
  {
    cover: (
      <div className="flex items-center gap-[5px] pl-2">
        <MusicCard /> Audio
      </div>
    ),
    title: 'New Track 01',
    type: 'Music',
    views: '12.4K',
    tips: '4.2K',
    accessType: 'Token-only',
  },
  {
    cover: (
      <div className="flex items-center gap-[5px] pl-2">
        <Video /> Video
      </div>
    ),
    title: 'Behind The Scenes  pl-2',
    type: 'Video',
    views: '9.8K',
    tips: '1.1K',
    accessType: 'VIP',
  },
  {
    cover: (
      <div className="flex items-center gap-[5px] pl-2">
        <Podcast /> Podcast
      </div>
    ),
    title: 'Podcast Ep 3',
    type: 'Audio',
    views: '7.3K',
    tips: '890',
    accessType: 'Subscribed',
  },
];

const RecentUploads: React.FC = () => {
  return (
    <>
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-[5px]">
          <MusicCard />{' '}
          <p className="whitespace-nowrap text-sm text-black-300 sm:text-lg">
            Recent Uploads
          </p>
        </div>
        <div className="flex items-center gap-[5px]">
          <Received />
          <p className="whitespace-nowrap text-sm text-black-300 sm:text-lg">
            Total Tips Received{' '}
            <span className="ml-[5px] text-primary">12,350</span>
          </p>
        </div>
      </div>
      <div className="mt-6 w-full sm:mt-[30px]">
        <TokenTable
          className="!rounded-none !shadow-none"
          columns={columns}
          data={Tabledata}
          headClass="sm:!px-0 w-[16.66%] whitespace-nowrap"
          bodyClass="sm:!px-0 w-[16.66%] whitespace-nowrap"
        />
      </div>
    </>
  );
};

export default RecentUploads;
