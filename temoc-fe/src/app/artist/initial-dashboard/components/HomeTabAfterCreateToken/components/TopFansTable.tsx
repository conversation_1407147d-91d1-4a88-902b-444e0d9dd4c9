import React, { Fragment, useState } from 'react';
import { PeopleAlts, Rank2, Rank3, Trophy } from '@/components/common/Icons';
import TokenTable from '@/components/ui/TokenTable';
import SelectComp from '@/components/common/Forms/SelectComp';

const columns = [
  { key: 'rank', label: 'Rank' },
  { key: 'username', label: 'Username' },
  { key: 'amount', label: 'Amount' },
  { key: 'tokens', label: 'Tokens' },
];

const Tabledata = [
  {
    rank: (
      <div className="flex items-center gap-[5px] pl-2">
        <Trophy /> 1
      </div>
    ),
    username: 'crypto_fan_452',
    amount: '$500',
    tokens: '200 Andy Haskin Token',
  },
  {
    rank: (
      <div className="flex items-center gap-[5px] pl-2">
        <Rank2 /> 2
      </div>
    ),
    username: 'crypto_fan_423',
    amount: '$350',
    tokens: '140 Andy <PERSON>',
  },
  {
    rank: (
      <div className="flex items-center gap-[5px] pl-2">
        <Rank3 /> 3
      </div>
    ),
    username: 'web3_enthuisast',
    amount: '$275',
    tokens: '100 Andy Haskin Token',
  },
  {
    rank: (
      <div className="flex items-center gap-[5px] pl-2">
        <Rank3 /> 4
      </div>
    ),
    username: 'web3_enthuisast',
    amount: '$275',
    tokens: '100 Andy Haskin Token',
  },
  {
    rank: (
      <div className="flex items-center gap-[5px] pl-2">
        <Rank3 /> 5
      </div>
    ),
    username: 'web3_enthuisast',
    amount: '$275',
    tokens: '100 Andy Haskin Token',
  },
];
const options = [{ name: 'Top 5' }, { name: 'Top 10' }, { name: 'Top 20' }];

const TopFansTable: React.FC = () => {
  const [select, setSelect] = useState<string>('');

  return (
    <>
      <section className="block w-full">
        {/* Header */}
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-[5px]">
            <PeopleAlts /> <p className="text-lg text-black-300">Top Fans</p>
          </div>
          <SelectComp
            Data={options}
            selected={select}
            setSelected={setSelect}
            placeholder="Top 5"
          />
        </div>
        {/* Table Data */}
        <div className="mt-6 w-full sm:mt-[30px]">
          <TokenTable
            className="!rounded-none !shadow-none"
            columns={columns}
            data={Tabledata}
            headClass="sm:!px-0 w-[16.66%] whitespace-nowrap"
            bodyClass="sm:!px-0 w-[16.66%] whitespace-nowrap"
          />
        </div>
      </section>
    </>
  );
};

export default TopFansTable;
