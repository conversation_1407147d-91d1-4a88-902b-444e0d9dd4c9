import React from 'react';
import {
  Copy,
  ArrowGrowth,
  InfoCircle,
  LiquidityLocked,
  Raised,
  Goal,
} from '@/components/common/Icons';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import RecentUploads from './components/RecentUploads';
import TopFansTable from './components/TopFansTable';
import FansBenefits from './components/FansBenefits';
import ReferralCard from '@/components/ui/ReferralCard';
import { useQuery } from '@tanstack/react-query';
import { presaleService } from '@/services/presale.service';
import { useAuth } from '@/hooks/useAuth';

const HomeTabAfterCreateToken: React.FC = () => {
  const { user: authUser } = useAuth();
  const { data } = useQuery({
    queryKey: ['presale', authUser?._id],
    queryFn: () => presaleService.getPresale(authUser?._id as string),
    enabled: !!authUser?._id,
  });
  const presaleData = data?.data || {};

  const statsArray = [
    { icon: <Raised />, title: 'Raised', stats: '0' },
    {
      icon: <Goal />,
      title: 'Soft Cap',
      stats: `$ ${presaleData?.softCap ?? '-'}`,
    },
    {
      icon: <Goal />,
      title: 'Hard Cap',
      stats: `$ ${presaleData?.hardCap ?? '-'}`,
    },
    { icon: <Goal />, title: 'Contributors', stats: '765' },
  ];

  const refrelAddress = presaleData?.tokenAddress ?? '';

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(refrelAddress);
      toast.success('Copied to clipboard!');
    } catch {
      toast.error('Failed to copy link');
    }
  };

  return (
    <>
      <section className="bg-[#f8f8f8] p-4 sm:p-[30px]">
        <div>
          <p className="text-sm font-normal text-black-300">Home</p>
          <div className="mt-2.5 flex flex-wrap items-center justify-between gap-3">
            <h3 className="text-lg uppercase text-black-300 sm:text-[22px]">
              Dashboard
            </h3>
            {/* Ref */}
            <div className="block w-full max-w-[315px] rounded-[10px] border border-[#CECECE] bg-white px-4 py-2.5">
              <p className="text-[8px] font-normal leading-[150%] text-[#777777]">
                Token Address
              </p>
              <div className="flex items-center justify-between gap-6">
                <p className="truncate text-xs font-normal leading-none text-[#181818]">
                  {refrelAddress}
                </p>
                <div className="cursor-pointer" onClick={copyToClipboard}>
                  <Copy />
                </div>
              </div>
            </div>
          </div>
          <ReferralCard presaleData={presaleData} />
        </div>

        {/* Graph & Trade Values */}
        <div className="mt-6 flex flex-col gap-[30px] sm:mt-[30px] md:flex-row">
          {/* Graph */}
          {/* <div className="w-full">
            <TokenPerformanceChart className="p-4 sm:p-6" />
          </div> */}
          <div
            className="w-full rounded-[20px] bg-white px-4 py-5 sm:px-[30px] sm:py-10"
            style={{ boxShadow: '0px 0px 20px 0px #0000000' }}
          >
            {/* Head */}
            <div className="flex items-start justify-between">
              <div className="block">
                <div className="flex items-center gap-[5px]">
                  <ArrowGrowth className="h-3 w-5" />{' '}
                  <p className="text-base leading-none text-black-300 xs:text-xs">
                    Volume Traded
                  </p>{' '}
                  <InfoCircle />
                </div>
                <h4 className="mt-2.5 text-xl font-normal text-primary xs:text-sm">
                  $120,000
                </h4>
              </div>

              <div className="block">
                <div className="flex items-center gap-[5px]">
                  <LiquidityLocked className="h-3 w-5" />{' '}
                  <p className="text-base leading-none text-black-300 xs:text-xs">
                    Liquidity Locked
                  </p>{' '}
                  <InfoCircle />
                </div>
                <h4 className="mt-2.5 text-xl font-normal text-primary xs:text-sm">
                  $85,000
                </h4>
              </div>
            </div>
            {/* Horizontal Line */}
            <hr className="my-6 border-[#CECECE] sm:my-[30px]" />
            {/* Card Body */}
            <h4 className="text-lg text-black-300">Presale Stats</h4>
            <div className="mt-2.5">
              {statsArray.map((item, index) => {
                return (
                  <div
                    key={index}
                    className={`flex items-center justify-between ${
                      index !== statsArray.length - 1
                        ? 'border-b border-[#CECECE] py-2.5'
                        : 'pt-2.5'
                    }`}
                  >
                    <div className="flex items-center gap-[5px]">
                      {item.icon}{' '}
                      <p className="text-black-300">{item.title}:</p>
                    </div>
                    <p className="text-primary">{item.stats}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        {/* Recent Uploads Table */}
        <div
          className="mt-6 rounded-2xl bg-white p-6 sm:mt-[30px] sm:p-[30px]"
          style={{ boxShadow: '0px 0px 20px 0px #0000001' }}
        >
          <RecentUploads />
        </div>
        {/* Top Fans & Fans Benefits Tabls and data */}
        <div className="mt-6 flex w-full flex-col items-start gap-5 sm:mt-[30px] md:flex-row">
          <div
            className="flex w-full rounded-2xl bg-white p-6 sm:p-[30px]"
            style={{ boxShadow: '0px 0px 20px 0px #0000001' }}
          >
            <TopFansTable />
          </div>
          <div
            className="h-full w-full max-w-[520px] rounded-[20px] bg-white p-6 sm:p-[30px]"
            style={{ boxShadow: '0px 0px 20px 0px #0000001' }}
          >
            <FansBenefits />
          </div>
        </div>
      </section>
    </>
  );
};

export default HomeTabAfterCreateToken;
