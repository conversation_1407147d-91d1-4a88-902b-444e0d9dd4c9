'use client';
import Upload from '@/components/common/Icons/Upload';
import React, { useRef, useState } from 'react';
import { MdOutlineEdit } from 'react-icons/md';
import 'react-toastify/dist/ReactToastify.css';

import {
  useUploadArtistCover,
  useUploadArtistProfile,
} from '@/hooks/useUploadProfilePicture';
// import { useCopyToClipboard } from '@/lib/functions';
import { ArtistProfileRes } from '@/types/user.interface';
import { useQuery } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import InfoSkeleton from '@/components/ui/Skelton/InfoSkelton';
// import Image from 'next/image';
import Loader from '@/components/common/Loader';
import UpdateSocialMedia from '@/components/ui/UpdateSocialMedia';
import Verifyemail from './Verifyemail';
import { useAuth } from '@/hooks/useAuth';
import ImageComponent from '@/components/common/ImageComponent';
const InfoEdit = () => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const coverInputRef = useRef<HTMLInputElement>(null);
  const [previewProfilePhoto, setPreviewProfilePhoto] = useState<string | null>(
    null,
  );
  const [previewCoverPhoto, setPreviewCoverPhoto] = useState<string | null>(
    null,
  );

  // const queryClient = useQueryClient();

  const { data, isLoading } = useQuery<ArtistProfileRes>({
    queryKey: ['artist-profile'],
    queryFn: () => userService.getArtistProfile(),
  });

  const ArtistData = data?.data;
  const profilePhoto = ArtistData?.profilePic;
  const coverPhoto = ArtistData?.coverPhoto;

  const fullName =
    ArtistData?.firstName && ArtistData?.lastName
      ? `${ArtistData.firstName} ${ArtistData.lastName}`
      : 'Andy Haskin';

  const handleClick = () => fileInputRef.current?.click();
  const handleCoverClick = () => coverInputRef.current?.click();

  const uploadArtistProfileMutation = useUploadArtistProfile();
  const uploadArtistCoverMutation = useUploadArtistCover();

  // const copyToClipboard = useCopyToClipboard();
  // const profileLink = 'temoc.com/brunomars';
  // const handleCopy = () => {
  //   copyToClipboard(profileLink);
  // };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    await uploadArtistProfileMutation.mutateAsync(file!);
    const previewUrl = URL.createObjectURL(file!);
    setPreviewProfilePhoto(previewUrl);
  };

  const handleCoverImageChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;
    await uploadArtistCoverMutation.mutateAsync(file!);
    const previewUrl = URL.createObjectURL(file!);
    setPreviewCoverPhoto(previewUrl);
  };
  const { user } = useAuth();
  return (
    <>
      {isLoading || uploadArtistCoverMutation.isPending ? (
        <div className="h-[300px]">
          <InfoSkeleton />
        </div>
      ) : (
        <div
          className="relative h-[300px] bg-cover px-4 py-5 sm:px-8"
          style={{
            backgroundImage:
              coverPhoto || previewCoverPhoto
                ? `url(${previewCoverPhoto || coverPhoto})`
                : 'url(/assets/images/admin/info-upload.png)',
          }}
        >
          <div className="flex h-full w-full items-center justify-between gap-4 sm:items-end">
            <div className="flex items-center gap-3">
              <div className="relative w-[100px] sm:w-[160px]">
                <input
                  type="file"
                  accept="image/*"
                  ref={fileInputRef}
                  onChange={handleImageChange}
                  className="hidden"
                />
                <div
                  onClick={
                    !profilePhoto || previewProfilePhoto
                      ? handleClick
                      : undefined
                  }
                  className="cursor-pointer"
                >
                  <div className="relative flex h-[100px] w-[100px] flex-col items-center justify-center rounded-full bg-white sm:h-[160px] sm:w-[160px]">
                    {uploadArtistProfileMutation.isPending ? (
                      <Loader />
                    ) : profilePhoto || previewProfilePhoto ? (
                      <>
                        <ImageComponent
                          src={
                            previewProfilePhoto ||
                            profilePhoto ||
                            '/assets/images/admin/info-upload.png'
                          }
                          alt="Profile Preview"
                          fill
                          className="rounded-full object-cover"
                          figClassName="block h-[100px] w-[100px] rounded-full sm:h-[160px] sm:w-[160px]"
                        />
                        {/* Edit Icon on top of uploaded image */}
                        <button
                          onClick={handleClick}
                          className="absolute bottom-1 right-2 z-10 rounded-full bg-white p-[2px] shadow-md transition hover:scale-105 sm:bottom-4 sm:right-4 sm:p-1"
                          title="Edit Profile Image"
                        >
                          <MdOutlineEdit className="text-sm text-primary sm:text-base" />
                        </button>
                      </>
                    ) : (
                      <>
                        <Upload className="h-[20px] w-[20px] sm:h-auto sm:w-auto" />
                        <p className="mt-2 text-center text-[8px] leading-none text-[#666666] sm:!text-xs xs:!text-[10px]">
                          Upload Profile Image
                        </p>
                        <p className="mt-1 text-center !text-[8px] leading-none text-[#666666]">
                          900px by 900px Recommended
                        </p>
                      </>
                    )}
                  </div>
                </div>
                <div className="mt-2">
                  <UpdateSocialMedia ArtistData={ArtistData} />
                </div>
              </div>

              <div>
                <h3
                  className={`truncate text-xl font-semibold capitalize sm:max-w-[400px] sm:text-[30px] ${
                    coverPhoto
                      ? 'text-white xs:max-w-[200px]'
                      : 'text-[#333333] xs:max-w-[200px]'
                  }`}
                >
                  {fullName}
                </h3>
                <div className="mt-2">
                  <p
                    className={`line-clamp-3 text-xs font-medium sm:max-w-[256px] ${
                      coverPhoto
                        ? 'text-white xs:max-w-[200px]'
                        : 'text-[#333333] xs:max-w-[200px]'
                    }`}
                  >
                    {ArtistData?.bio || '--'}
                  </p>
                </div>
              </div>
            </div>

            {/* Cover Upload Section */}
            <div className="flex flex-col items-center justify-center gap-2">
              <div className="absolute right-0 top-2 w-[80px] sm:relative">
                <input
                  type="file"
                  accept="image/*"
                  ref={coverInputRef}
                  onChange={handleCoverImageChange}
                  className="hidden"
                />
                {!coverPhoto && (
                  <div onClick={handleCoverClick} className="cursor-pointer">
                    <div className="flex h-[60px] w-[60px] flex-col items-center justify-center overflow-hidden rounded-xl bg-white sm:h-[80px] sm:w-[80px]">
                      <Upload className="h-[20px] w-[20px] sm:h-auto sm:w-auto" />
                      <p className="mt-2 text-center !text-[8px] leading-none text-[#666666] sm:!text-xs">
                        Set Cover
                      </p>
                    </div>
                  </div>
                )}
              </div>
              {/* {!coverPhoto && (
                <p
                  className={`text-[8px] sm:text-xs ${
                    coverPhoto ? 'text-white' : 'text-[#333333]'
                  }`}
                >
                  1300px by 300px Recommended
                </p>
              )} */}
            </div>
          </div>
          {coverPhoto && (
            <button
              onClick={handleCoverClick}
              className="absolute right-2 top-2 z-10 rounded-full bg-white p-[2px] shadow-md transition hover:scale-105 sm:right-4 sm:top-4 sm:p-1"
              title="Edit Profile Image"
            >
              <MdOutlineEdit className="text-sm text-primary sm:text-base" />
            </button>
          )}
          {user?.artistProfile?.status === 'pending' && (
            <div className="absolute left-0 top-0 w-full">
              <Verifyemail />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default InfoEdit;
