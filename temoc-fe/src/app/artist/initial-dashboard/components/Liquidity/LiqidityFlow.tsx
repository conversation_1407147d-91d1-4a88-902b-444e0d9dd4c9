import { Droplets, TrendingUp, History, Info, Minus } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from './TokenTabes';
import { LiquidityForm } from './LiquidityForm';
import { RemoveLiquidity } from './RemoveLiquidity';

const LiqidityFlow = () => {
  return (
    <div className="">
      <div className="mx-auto px-4 py-8">
        <Tabs defaultValue="liquidity" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger
              value="liquidity"
              className="flex items-center space-x-2"
            >
              <Droplets className="h-4 w-4" />
              <span>Add Liquidity</span>
            </TabsTrigger>
            <TabsTrigger value="remove" className="flex items-center space-x-2">
              <Minus className="h-4 w-4" />
              <span>Remove</span>
            </TabsTrigger>
            <TabsTrigger
              value="portfolio"
              className="flex items-center space-x-2"
            >
              <TrendingUp className="h-4 w-4" />
              <span>Portfolio</span>
            </TabsTrigger>
            <TabsTrigger
              value="details"
              className="flex items-center space-x-2"
            >
              <Info className="h-4 w-4" />
              <span>Details</span>
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="flex items-center space-x-2"
            >
              <History className="h-4 w-4" />
              <span>History</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="liquidity">
            <LiquidityForm />
          </TabsContent>

          <TabsContent value="remove">
            <RemoveLiquidity />
          </TabsContent>

          <TabsContent value="portfolio">
            <h2>portfolio</h2>
            {/* <Portfolio walletAddress={walletAddress} /> */}
          </TabsContent>

          <TabsContent value="details">
            <h2>detail</h2>
            {/* <LiquidityDetails walletAddress={walletAddress} /> */}
          </TabsContent>

          <TabsContent value="history">
            <h2>history</h2>
            {/* <TransactionHistory walletAddress={walletAddress} /> */}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default LiqidityFlow;
