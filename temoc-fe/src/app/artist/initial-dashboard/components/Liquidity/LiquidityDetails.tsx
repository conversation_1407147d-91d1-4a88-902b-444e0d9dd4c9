import Button from '@/components/common/Button';
import React from 'react';
import Arrow from '@/components/common/Icons/Arrow';
import { Progress } from '@/components/ui/Progress';
import { TrendingUp, BarChart3, Users, DollarSign, Clock } from 'lucide-react';
interface PortfolioProps {
  setPortfolioView: any;
}
export const LiquidityDetails: React.FC<PortfolioProps> = ({
  setPortfolioView,
}) => {
  const poolDetails = {
    pair: 'USDC/ETH',
    poolAddress: '******************************************',
    totalLiquidity: 125000000,
    volume24h: 8500000,
    fees24h: 25600,
    totalFees7d: 178400,
    apr: 15.8,
    myLiquidity: 5420.3,
    myShare: 0.00432,
    priceRange: {
      current: 1850.25,
      min: 1800.0,
      max: 1900.0,
    },
    feeEarnings: {
      total: 156.75,
      pending: 8.25,
    },
  };

  const formatCurrency = (amount: number) =>
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);

  const formatAddress = (address: string) =>
    `${address.slice(0, 10)}...${address.slice(-8)}`;

  return (
    <div className="space-y-6">
      {/* Pool Overview */}
      <div className="mb-4 flex items-center justify-start">
        <Button
          className="flex !gap-4 !border-none !bg-black-900 !py-3 uppercase sm:!px-[40px]"
          onClick={() => setPortfolioView('main')}
        >
          <Arrow className="rotate-180" />
          Back
        </Button>
      </div>
      <div className="dark:bg-muted rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-start justify-between">
          <div>
            <h3 className="text-2xl font-semibold">{poolDetails.pair} Pool</h3>
            <p className="text-muted-foreground mt-2 text-sm">
              {formatAddress(poolDetails.poolAddress)}
            </p>
          </div>
          <span className="rounded-md bg-green-100 px-2 py-1 text-sm text-green-800">
            Active
          </span>
        </div>
        <div className="grid gap-6 md:grid-cols-3">
          <div className="space-y-2">
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <DollarSign className="h-4 w-4" />
              <span>Total Liquidity</span>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(poolDetails.totalLiquidity)}
            </p>
          </div>

          <div className="space-y-2">
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <BarChart3 className="h-4 w-4" />
              <span>24h Volume</span>
            </div>
            <p className="text-2xl font-bold">
              {formatCurrency(poolDetails.volume24h)}
            </p>
          </div>

          <div className="space-y-2">
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span>APR</span>
            </div>
            <p className="text-2xl font-bold text-green-600">
              {poolDetails.apr}%
            </p>
          </div>
        </div>
      </div>

      {/* My Position Details */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="dark:bg-muted rounded-lg border bg-white p-6 shadow-sm">
          <div className="mb-4">
            <h3 className="text-xl font-semibold">My Position</h3>
            <p className="text-muted-foreground text-sm">
              Your current liquidity position details
            </p>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Liquidity Value</span>
              <span className="font-semibold">
                {formatCurrency(poolDetails.myLiquidity)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Pool Share</span>
              <span className="font-semibold">
                {(poolDetails.myShare * 100).toFixed(4)}%
              </span>
            </div>
            <div className="space-y-2">
              <span className="text-muted-foreground text-sm">
                Share Visualization
              </span>
              <Progress value={poolDetails.myShare * 100} className="h-2" />
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Fees Earned</span>
              <span className="font-semibold text-green-600">
                {formatCurrency(poolDetails.feeEarnings.total)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Pending Fees</span>
              <span className="font-semibold">
                {formatCurrency(poolDetails.feeEarnings.pending)}
              </span>
            </div>
          </div>
        </div>

        <div className="dark:bg-muted rounded-lg border bg-white p-6 shadow-sm">
          <div className="mb-4">
            <h3 className="text-xl font-semibold">Price Range</h3>
            <p className="text-muted-foreground text-sm">
              Current price position within your range
            </p>
          </div>
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-2xl font-bold">
                {formatCurrency(poolDetails.priceRange.current)}
              </p>
              <p className="text-muted-foreground text-sm">Current ETH Price</p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Min: {formatCurrency(poolDetails.priceRange.min)}</span>
                <span>Max: {formatCurrency(poolDetails.priceRange.max)}</span>
              </div>
              <Progress value={75} className="h-2" />
              <p className="text-muted-foreground text-center text-xs">
                Position is 75% through range
              </p>
            </div>
            <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                Your position is actively earning fees as the current price is
                within your specified range.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Fee Analytics */}
      <div className="dark:bg-muted rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4">
          <h3 className="text-xl font-semibold">Fee Analytics</h3>
          <p className="text-muted-foreground text-sm">
            Detailed breakdown of your fee earnings
          </p>
        </div>
        <div className="grid gap-4 md:grid-cols-4">
          <div className="text-center">
            <div className="text-muted-foreground mb-2 flex items-center justify-center space-x-1 text-sm">
              <Clock className="h-4 w-4" />
              <span>24h Fees</span>
            </div>
            <p className="text-lg font-semibold">
              {formatCurrency(poolDetails.fees24h / 1000)}
            </p>
          </div>

          <div className="text-center">
            <div className="text-muted-foreground mb-2 flex items-center justify-center space-x-1 text-sm">
              <Users className="h-4 w-4" />
              <span>7d Fees</span>
            </div>
            <p className="text-lg font-semibold">
              {formatCurrency(poolDetails.totalFees7d / 1000)}
            </p>
          </div>

          <div className="text-center">
            <div className="text-muted-foreground mb-2 flex items-center justify-center space-x-1 text-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span>Projected Monthly</span>
            </div>
            <p className="text-lg font-semibold text-green-600">
              {formatCurrency((poolDetails.feeEarnings.total / 30) * 30)}
            </p>
          </div>

          <div className="text-center">
            <div className="text-muted-foreground mb-2 flex items-center justify-center space-x-1 text-sm">
              <DollarSign className="h-4 w-4" />
              <span>Claimable</span>
            </div>
            <p className="text-lg font-semibold">
              {formatCurrency(poolDetails.feeEarnings.pending)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
