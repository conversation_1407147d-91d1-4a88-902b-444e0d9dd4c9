import { useContext, useEffect, useState } from 'react';
import { formatUnits, parseUnits } from 'ethers';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';

import { Button } from '@/components/common';
import { AlertTriangle, ArrowDownUp, Info, Plus, Zap } from 'lucide-react';
import SelectComponent from '@/components/common/Forms/Select';
import { Input } from '@/components/common/Forms/Input';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';
import { useEthersSigner } from '@/hooks/useEthersSigner';
import { ChainContext } from '@/context/ChainContextProvider';
import { mintLiquidity } from '@/components/ui/addLiquidity/addLiquidity';
import { toast } from 'react-toastify';
import axios from 'axios';
import { copyToClipboard, truncateAddress } from '@/lib/functions';
import { Copy } from '@/components/common/Icons';
import LiquidityInfoSection from './LiquidityInfoSection';

function calculateEthValue(
  amountA: string,
  initialPrice: string,
  ethPrice: any,
): string {
  if (!ethPrice || !amountA || !initialPrice) return '0';

  // Convert values to BigInt using 18 decimals
  const amountABig = parseUnits(amountA, 18); // token amount
  const tokenUsdPriceBig = parseUnits(initialPrice, 18); // token price in USD
  const ethUsdPriceBig = parseUnits(ethPrice.toString(), 18); // ETH/USD price

  // tokenPriceInEth = tokenUsdPrice / ethPrice
  const tokenPriceInEth =
    (tokenUsdPriceBig * parseUnits('1', 18)) / ethUsdPriceBig;

  // ethValue = tokenAmount * tokenPriceInEth
  const ethValueBig = (amountABig * tokenPriceInEth) / parseUnits('1', 18);

  return formatUnits(ethValueBig, 18); // human-readable ETH
}
interface Iprops {
  setData?: any;
}
export const LiquidityForm = ({ setData }: Iprops) => {
  // const { chain } = useContext(ChainContext);

  const { data } = useQuery({
    queryKey: ['token'],
    queryFn: () => tokenService.getToken(),
  });

  const token = data?.data;

  const chainArray = [
    { label: 'WETH', value: '******************************************' },
  ];

  const tokenOptions =
    token?.map((t: any) => ({
      label: (
        <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
          {t?.symbol}
        </p>
      ),
      value: t?.address,
    })) || [];

  const [selectToken, setSelectToken] = useState<{
    value: string;
    label: string;
  } | null>(tokenOptions[0] || null);

  const [tokenA, setTokenA] = useState(tokenOptions[0]?.value);
  const [infoSection, setInfoSection] = useState(true);
  const [tokenB, setTokenB] = useState(chainArray[0]?.value);
  const [amountA, setAmountA] = useState('');
  const [ethPrice, setEthPrice] = useState<number | null>(null);
  const [initialPrice, setInitialPrice] = useState<any>();
  // const [totalLiquidityEth] = useState(0);
  const [loading, setLoading] = useState(false);
  const [, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const [selectChain, setSelectChain] = useState<{
    value: string;
    label: string;
  } | null>(chainArray[0] || null);

  const estimateGasFee = () => {
    return '~0.015 ETH';
  };

  // const deadlineSeconds = Math.floor(Date.now() / 1000) + Number(deadline) * 60;
  const { chain } = useContext(ChainContext);
  const signer = useEthersSigner({ chainId: chain.id });
  const { primaryWallet } = useDynamicContext();
  useEffect(() => {
    async function fetchEthPrice() {
      const res = await axios.get(
        'https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd',
      );
      setEthPrice(res.data.ethereum.usd);
    }

    fetchEthPrice();

    const interval = setInterval(fetchEthPrice, 60000); // refresh every 60s
    return () => clearInterval(interval);
  }, []);

  const ethValue: any = calculateEthValue(
    amountA?.toString(),
    initialPrice?.toString(),
    ethPrice?.toString(),
  );

  const handleAddLiquidity = async () => {
    setLoading(true);
    setStatus('idle');
    setError(null);
    const mintLiquidityData = {
      amountA,
      amountB: ethValue as string,
      tokenA,
      tokenB,
      recipient: primaryWallet?.address as string,
      signer,
      chainId: chain.id,
    };

    const res = await mintLiquidity(mintLiquidityData);
    if (res.status === 'success') {
      toast.success('Liquidity added successfully');
      queryClient.invalidateQueries({ queryKey: ['liquidity'] });
      if (setData) {
        setData('wallet-connect');
      }
    }
    setLoading(res.loading);
    setStatus(res.status);
    console.error(res.error);

    toast.error(res.error);
  };

  return (
    <>
      <div className="grid gap-3 lg:grid-cols-2">
        <div className="rounded-lg border border-[#CECECE] bg-white p-4 sm:p-8">
          {infoSection ? (
            <LiquidityInfoSection setInfoSection={setInfoSection} />
          ) : (
            <>
              <div>
                <div className="flex items-center justify-between gap-2">
                  <div className="flex items-center space-x-2">
                    <Plus className="h-5 w-5" />
                    <h3 className="text-xl font-semibold sm:text-left sm:text-[22px]">
                      Add Liquidity
                    </h3>
                  </div>
                  <button
                    onClick={() => setInfoSection(true)}
                    className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100"
                  >
                    <Info className="h-4 w-4 text-blue-600" />
                  </button>
                </div>
                <p className="mt-1">
                  Add liquidity to earn fees from trades in the pool
                </p>
              </div>
              <label className="mt-4 block">
                Initial Price of Token
                <Input
                  name="InitialPrice"
                  type="number"
                  step="0.0001"
                  placeholder="Initial Price"
                  className="input mt-1"
                  value={initialPrice}
                  onChange={(e) => {
                    const val = e.target.value;
                    setInitialPrice(Number(val));
                  }}
                />
              </label>
              <div className="mt-5 space-y-6">
                {/* Token A Input */}
                <div className="space-y-2">
                  <p className="font-medium">Token A</p>
                  <div className="flex space-x-2">
                    <SelectComponent
                      selected={selectToken}
                      onSelect={(val) => {
                        setSelectToken(val);
                        setTokenA(val?.value || '');
                      }}
                      options={tokenOptions}
                      className="w-full"
                      placeholder="Select Token"
                    />

                    <Input
                      name=""
                      type="number"
                      placeholder="Tokens Amount to Add Liquidity"
                      value={amountA}
                      onChange={(e) => {
                        const val = e.target.value;
                        setAmountA(val);
                      }}
                      className="!h-[62px]"
                    />
                  </div>
                </div>

                {/* Swap Icon */}
                <div className="flex justify-center">
                  <ArrowDownUp className="text-muted-foreground h-6 w-6" />
                </div>

                {/* Token B Input */}
                <div className="border-b border-[#CECECE] pb-5">
                  <p className="font-medium">Token B</p>
                  <div className="flex space-x-2">
                    <SelectComponent
                      selected={selectChain}
                      onSelect={(val) => {
                        setSelectChain(val);
                        setTokenB(val?.value || '');
                      }}
                      options={chainArray}
                      className="w-full"
                      placeholder="Chain"
                    />
                    <Input
                      name=""
                      type="number"
                      placeholder="0.0"
                      value={ethValue}
                      readOnly
                      className="!h-[62px] flex-1 cursor-not-allowed bg-gray-100"
                    />
                  </div>
                </div>

                {/* Slippage Settings */}
                {/* <div className="space-y-3">
              <div className="flex items-center justify-between">
                <p className="font-medium">Slippage Tolerance</p>
                <div>{slippage[0]}%</div>
              </div>
              <Slider
                value={slippage}
                onValueChange={setSlippage}
                max={5}
                min={0.1}
                step={0.1}
                className="w-full"
              />
              <div className="text-muted-foreground flex justify-between text-xs">
                <span>0.1%</span>
                <span>5%</span>
              </div>
            </div> */}

                {/* Deadline */}
                {/* <div className="space-y-2">
              <p className="font-medium">Transaction Deadline (minutes)</p>
              <Input
                name=""
                id="deadline"
                type="number"
                value={deadline}
                onChange={(e) => setDeadline(Number(e.target.value))}
                placeholder="20"
                className="!h-[62px]"
              />
            </div> */}

                {/* Add Liquidity Button */}
                <Button
                  disabled={loading}
                  className="w-full"
                  onClick={handleAddLiquidity}
                >
                  {loading ? (
                    <div className="flex items-center space-x-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                      <span>Processing Transaction...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Plus className="h-4 w-4" />
                      <span className="font-medium">Add Liquidity</span>
                    </div>
                  )}
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Transaction Summary */}
        <div className="rounded-lg border border-[#CECECE] bg-white p-4 sm:p-8">
          <div>
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <h3 className="text-xl font-semibold sm:text-left sm:text-[22px]">
                Transaction Summary
              </h3>
            </div>
          </div>
          <div className="mt-3 space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground mt-2 text-sm">
                  Pool Pair
                </span>
                <span className="text-sm font-medium">
                  {tokenA ? (
                    <div className="inline-flex items-center gap-1">
                      {truncateAddress(tokenA)}{' '}
                      <div
                        onClick={() => copyToClipboard(tokenA)}
                        className="cursor-pointer"
                      >
                        <Copy />
                      </div>
                    </div>
                  ) : (
                    'Select Token'
                  )}{' '}
                  /{' '}
                  {tokenB && (
                    <div className="inline-flex items-center gap-1">
                      {truncateAddress(tokenB)}{' '}
                      <div
                        onClick={() => copyToClipboard(tokenB)}
                        className="cursor-pointer"
                      >
                        <Copy />
                      </div>
                    </div>
                  )}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Estimated Gas Fee
                </span>
                <span className="text-sm font-medium">{estimateGasFee()}</span>
              </div>

              {/* <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Slippage Tolerance
                </span>
                <span className="text-sm font-medium">{slippage[0]}%</span>
              </div> */}

              {/* <div className="flex justify-between border-b border-[#CECECE] pb-4">
                <span className="text-muted-foreground text-sm">Deadline</span>
                <span className="text-sm font-medium">{deadline} minutes</span>
              </div> */}
              <div className="flex justify-between">
                <span className="text-muted-foreground text-sm">
                  Total Liquidity (ETH)
                </span>
                <span className="text-sm font-medium">{ethValue} ETH</span>
              </div>
            </div>

            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="mt-0.5 h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <div className="text-xs text-yellow-800 dark:text-yellow-200">
                  <p className="font-medium">Important Notes:</p>
                  <ul className="mt-1 list-inside list-disc space-y-1">
                    <li>You will receive LP tokens representing your share</li>
                    <li>Prices may change during transaction confirmation</li>
                    <li>Consider impermanent loss for volatile pairs</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
