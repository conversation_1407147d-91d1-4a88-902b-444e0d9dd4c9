import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Info, HelpCircle } from 'lucide-react';

interface IProps {
  setInfoSection: React.Dispatch<React.SetStateAction<boolean>>;
}

const LiquidityInfoSection = ({ setInfoSection }: IProps) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const infoItems = [
    {
      title: 'Initial Price of Token',
      description:
        'Define the starting price of your token in relation to the paired token (e.g., 1 Token A = X WETH).',
      purpose: 'Helps set the value for your pools first liquidity pair.',
    },
    {
      title: 'Token A (Your Token)',
      description: 'Select the token you created or wish to launch.',
      purpose: 'This is the token users will buy to access your content.',
    },
    {
      title: 'Token B (Paired Token)',
      description: 'Select a base token to pair with (e.g., WETH).',
      purpose:
        'This is typically a widely-used token that provides liquidity and trading flexibility.',
    },
    {
      title: 'Amount for Token A',
      description:
        'Enter how many of your tokens you&apos;re adding to the pool.',
      purpose: 'This helps define the pool size and your initial supply.',
    },
    {
      title: 'Amount for Token B',
      description:
        'Enter the amount of WETH (or chosen base token) to match the Token A value based on the initial price.',
      purpose: 'Make sure it aligns with your initial price settings.',
    },
  ];
  return (
    <div className="mb-6 rounded-xl border border-gray-200 bg-white shadow-sm">
      {/* Header */}
      <div
        className="flex cursor-pointer items-center justify-between rounded-xl p-4 transition-colors hover:bg-gray-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
            <Info className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">
              How to Add Liquidity
            </h3>
            <p className="text-sm text-gray-500">
              Understanding the liquidity pool parameters
            </p>
          </div>
        </div>
        {isExpanded ? (
          <ChevronUp className="h-5 w-5 text-gray-400" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-400" />
        )}
      </div>
      {/* Expandable Content */}
      {isExpanded && (
        <div className="border-t border-gray-100">
          <div className="space-y-6 p-6">
            {/* Quick Overview */}
            <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
              <h4 className="mb-2 font-medium text-blue-900">Quick Overview</h4>
              <p className="text-sm text-blue-700">
                Adding liquidity creates a trading pool for your token.
                You&apos;ll need to provide both your token and a paired token
                (like WETH) to enable trading and earn fees from transactions.
              </p>
            </div>
            {/* Field Explanations */}
            <div className="space-y-4">
              <h4 className="flex items-center gap-2 font-semibold text-gray-900">
                <HelpCircle className="h-4 w-4 text-gray-500" />
                Field Explanations
              </h4>
              {infoItems.map((item, index) => (
                <div
                  key={index}
                  className="rounded-lg border border-gray-200 p-4 transition-colors hover:border-gray-300"
                >
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-orange-100">
                      <span className="text-xs font-medium text-orange-600">
                        {index + 1}
                      </span>
                    </div>
                    <div className="flex-1">
                      <h5 className="mb-1 font-semibold text-gray-900">
                        {item.title}
                      </h5>
                      <p className="mb-2 text-sm text-gray-600">
                        {item.description}
                      </p>
                      <div className="rounded border-l-2 border-orange-300 bg-gray-50 p-2 text-xs text-gray-700">
                        <strong>Purpose:</strong> {item.purpose}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            {/* Tips Section */}
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
              <h4 className="mb-2 font-medium text-yellow-900">Pro Tips</h4>
              <ul className="space-y-1 text-sm text-yellow-800">
                <li>
                  • Start with a reasonable initial price based on your
                  token&apos;s intended value
                </li>
                <li>
                  • Ensure you have enough paired tokens (WETH) to match your
                  token amount
                </li>
                <li>
                  • Consider market conditions when setting your initial
                  liquidity amounts
                </li>
                <li>
                  • Double-check all amounts before confirming the transaction
                </li>
              </ul>
            </div>
            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <button
                className="flex-1 rounded-lg bg-orange-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-600"
                onClick={() => {
                  setIsExpanded(false);
                  setInfoSection(false);
                }}
              >
                Got It, Let&apos;s Continue
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default LiquidityInfoSection;
