import React from 'react';
import { useState } from 'react';
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Slider } from "@/components/ui/slider";
// import { Separator } from "@/components/ui/separator";
// import { Badge } from "@/components/ui/badge";
import { Minus, AlertCircle, Zap, ArrowDown } from 'lucide-react';
import { Button } from '@/components/common';
import { Slider } from './slider';
import { Input } from '@/components/common/Forms/Input';
import Arrow from '@/components/common/Icons/Arrow';
// import { useToast } from "@/hooks/use-toast";

// interface RemoveLiquidityProps {
//   walletAddress: string;
// }
interface PortfolioProps {
  setPortfolioView?: any;
}
export const RemoveLiquidity: React.FC<PortfolioProps> = ({
  setPortfolioView,
}) => {
  const [removePercentage, setRemovePercentage] = useState([25]);
  const [isProcessing] = useState(false);
  // const { toast } = useToast();

  const position = {
    pair: 'USDC/ETH',
    lpTokens: 1250.5,
    totalValue: 5420.3,
    token0Amount: 2710.15,
    token1Amount: 1.465,
    token0Symbol: 'USDC',
    token1Symbol: 'ETH',
    feesEarned: 156.75,
  };

  const calculateRemoval = () => {
    const percentage = removePercentage[0] / 100;
    return {
      lpTokensToRemove: position.lpTokens * percentage,
      token0ToReceive: position.token0Amount * percentage,
      token1ToReceive: position.token1Amount * percentage,
      valueToRemove: position.totalValue * percentage,
      feesToClaim: position.feesEarned,
    };
  };

  const removal = calculateRemoval();

  // const handleRemoveLiquidity = async () => {
  //   setIsProcessing(true);

  //   // Simulate transaction processing
  //   setTimeout(() => {
  //     setIsProcessing(false);
  //     toast({
  //       title: "Liquidity Removed Successfully",
  //       description: `Removed ${removePercentage[0]}% of your liquidity position`,
  //     });

  //     // Reset form
  //     setRemovePercentage([25]);
  //   }, 3000);
  // };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const presetPercentages = [25, 50, 75, 100];

  return (
    <div>
      <div className="mb-4 flex items-center justify-start">
        <Button
          className="flex !gap-4 !border-none !bg-black-900 !py-3 uppercase sm:!px-[40px]"
          onClick={() => setPortfolioView('main')}
        >
          <Arrow className="rotate-180" />
          Back
        </Button>
      </div>

      <div className="grid gap-3 sm:grid-cols-2">
        <div className="rounded-lg border border-[#CECECE] bg-white p-4 sm:p-8">
          <div>
            <div className="flex items-center space-x-2">
              <Minus className="h-5 w-5" />
              <h3 className="text-xl font-semibold sm:text-left sm:text-[22px]">
                Remove Liquidity
              </h3>
            </div>
            <p className="mt-1">
              Remove liquidity from your position and claim fees
            </p>
          </div>
          <div className="space-y-6">
            {/* Current Position */}
            <div className="rounded-lg bg-[#F6F6F6] p-4">
              <h3 className="mb-3 font-semibold">Current Position</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="">Pool</span>
                  <div>{position.pair}</div>
                </div>
                <div className="flex justify-between">
                  <span className="">LP Tokens</span>
                  <span className="font-medium">{position.lpTokens}</span>
                </div>
                <div className="flex justify-between">
                  <span className="">Total Value</span>
                  <span className="font-medium">
                    {formatCurrency(position.totalValue)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="">Unclaimed Fees</span>
                  <span className="font-medium text-green-600">
                    {formatCurrency(position.feesEarned)}
                  </span>
                </div>
              </div>
            </div>

            {/* Removal Percentage */}
            <div className="space-y-4">
              <p className="font-semibold">Amount to Remove (%)</p>

              {/* Preset Buttons */}
              <div className="grid grid-cols-4 gap-2">
                {presetPercentages.map((percentage) => (
                  <Button
                    key={percentage}
                    variant={
                      removePercentage[0] === percentage ? 'solid' : 'outline'
                    }
                    className="!rounded-md !py-2 !text-sm"
                    onClick={() => setRemovePercentage([percentage])}
                  >
                    {percentage} %
                  </Button>
                ))}
              </div>

              {/* Slider */}
              <div className="space-y-3">
                <Slider
                  value={removePercentage}
                  onValueChange={setRemovePercentage}
                  max={100}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs">
                  <span>1%</span>
                  <span className="font-medium">{removePercentage[0]}%</span>
                  <span>100%</span>
                </div>
              </div>

              {/* Custom Input */}
              <div className="flex items-center gap-2">
                <p className="text-sm font-semibold text-[#292D32]">Custom:</p>
                <div>
                  <Input
                    name=""
                    id="custom-percentage"
                    type="number"
                    value={removePercentage[0]}
                    onChange={(e) =>
                      setRemovePercentage([
                        Math.min(100, Math.max(1, Number(e.target.value))),
                      ])
                    }
                    min="1"
                    max="100"
                    className="!h-8 !w-16 !rounded-md !text-xs"
                  />
                </div>
                <span className="text-sm text-[#292D32]">%</span>
              </div>
            </div>

            {/* Remove Button */}
            <Button
              disabled={isProcessing || removePercentage[0] === 0}
              className="w-full"
              variant="outline"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Minus className="h-4 w-4" />
                  <span>Remove Liquidity</span>
                </div>
              )}
            </Button>
          </div>
        </div>

        {/* Removal Summary */}
        <div className="rounded-lg border border-[#CECECE] bg-white p-4 sm:p-8">
          <div>
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <h3 className="text-xl font-semibold sm:text-left sm:text-[22px]">
                {' '}
                Removal Summary
              </h3>
            </div>
          </div>
          <div className="mt-5 space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">LP Tokens to Remove</span>
                <span className="text-sm font-medium">
                  {removal.lpTokensToRemove.toFixed(2)}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="text-sm">Removal Percentage</span>
                <span className="text-sm font-medium">
                  {removePercentage[0]}%
                </span>
              </div>

              <div className="flex justify-between border-b border-[#CECECE] pb-5">
                <span className="text-sm">Est. Value to Remove</span>
                <span className="text-sm font-medium">
                  {formatCurrency(removal.valueToRemove)}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-semibold">You will receive:</h3>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{position.token0Symbol}</span>
                  </div>
                  <span className="font-medium">
                    {removal.token0ToReceive.toFixed(2)}
                  </span>
                </div>

                <ArrowDown className="mx-auto h-4 w-4" />

                <div className="flex items-center justify-between border-b border-[#CECECE] pb-5">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">{position.token1Symbol}</span>
                  </div>
                  <span className="font-medium">
                    {removal.token1ToReceive.toFixed(4)}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Claimable Fees</span>
                <span className="text-sm font-medium text-green-600">
                  {formatCurrency(removal.feesToClaim)}
                </span>
              </div>
            </div>

            <div className="rounded-lg border border-orange-200 bg-orange-50 p-3 dark:border-orange-800 dark:bg-orange-900/20">
              <div className="flex items-start space-x-2">
                <AlertCircle className="mt-0.5 h-4 w-4 text-orange-600 dark:text-orange-400" />
                <div className="text-xs text-orange-800 dark:text-orange-200">
                  <p className="font-medium">Important:</p>
                  <ul className="mt-1 list-inside list-disc space-y-1">
                    <li>Removing liquidity is irreversible</li>
                    <li>You&apos;ll stop earning fees on removed amount</li>
                    <li>Gas fees apply for this transaction</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
