'use client';
import React, { useEffect, useState, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import HomeTab from './HomeTab';
import Library from './library';
import Token from './Token';
import HomeTabAfterCreateToken from './HomeTabAfterCreateToken';
import About from '../../about';
import UserManagement from '../../management/components/UserManagement';
import Management from '../../management/components/Management';
import { toast } from 'react-toastify';
import { useAuth } from '@/hooks/useAuth';

const Tabs: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get tab and afterCreateToken from URL
  const urlTab = searchParams.get('tab') || 'Home';
  const { user } = useAuth();
  const urlAfterCreateToken = searchParams.get('afterCreateToken') === 'true';
  const [activeTab, setActiveTab] = useState<string>(urlTab);
  const [afterCreateToken, setAfterCreateToken] =
    useState<boolean>(urlAfterCreateToken);

  // Keep state in sync with URL
  useEffect(() => {
    setActiveTab(urlTab);
    setAfterCreateToken(urlAfterCreateToken);
  }, [urlTab, urlAfterCreateToken]);

  const handleTabChange = (tabName: string) => {
    // Delay the router.push to avoid triggering during the render phase
    setTimeout(() => {
      const params = new URLSearchParams(searchParams.toString());
      params.set('tab', tabName);
      if (
        (tabName === 'Token' || tabName === 'About') &&
        user?.artistProfile?.status === 'pending'
      ) {
        toast.info('Your KYC is under review, please wait for approval');
      }
      router.push(`?${params.toString()}`);
    }, 0);
  };

  const handleAfterCreateToken = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'Home');
    params.set('afterCreateToken', 'true');
    router.push(`?${params.toString()}`);
  };

  // Dynamically update tabs if afterCreateToken is true
  const tabsData = useMemo(() => {
    const baseTabs = [
      { name: 'Home' },
      { name: 'Token' },
      { name: 'Library' },
      { name: 'About' },
    ];
    // if (afterCreateToken) {
    //   baseTabs.push({ name: 'Fans' }, { name: 'Management' });
    // }
    return baseTabs;
  }, [afterCreateToken]);

  // Update URL when activeTab or afterCreateToken changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', activeTab);
    params.set('afterCreateToken', String(afterCreateToken));
    router.push(`?${params.toString()}`);
  }, [activeTab, afterCreateToken, router, searchParams]);

  return (
    <>
      <section className="flex w-full justify-center bg-white py-5">
        <div
          className={`${afterCreateToken ? 'gap-3 xs:justify-start' : 'gap-5'} hideScrollbar flex w-full justify-center overflow-auto sm:gap-7`}
        >
          {tabsData.map((item, index) => {
            const isActive = item.name === activeTab;
            return (
              <div
                key={item.name}
                className={`flex cursor-pointer justify-center sm:pr-7 ${afterCreateToken ? 'pr-3' : 'pr-5'} ${
                  index !== tabsData.length - 1
                    ? 'border-r border-[#CECECE]'
                    : ''
                }`}
                onClick={() => handleTabChange(item.name)}
              >
                <p
                  className={`text-sm font-normal leading-none xs:text-xs ${
                    isActive
                      ? 'border-b border-primary text-primary'
                      : 'text-[#333333]'
                  }`}
                >
                  {item.name}
                </p>
              </div>
            );
          })}
        </div>
      </section>

      {activeTab === 'Home' && (
        <>{afterCreateToken ? <HomeTabAfterCreateToken /> : <HomeTab />}</>
      )}
      {activeTab === 'Token' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <Token setAfterCreateToken={handleAfterCreateToken} />
        </div>
      )}

      {activeTab === 'Library' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <Library />
        </div>
      )}
      {activeTab === 'About' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <About />
        </div>
      )}
      {activeTab === 'Management' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <Management />
        </div>
      )}
      {activeTab === 'Fans' && (
        <div className="min-h-[50vh] bg-[#F8F8F8] p-4 sm:p-12">
          <UserManagement />
        </div>
      )}
    </>
  );
};

export default Tabs;
