import React, { useState } from 'react';
import { Button } from '@/components/common';
import {
  Arrow,
  Base,
  Ethereum,
  Solana,
  Temoc,
  Ton,
} from '@/components/common/Icons';
import SelectComponent from '@/components/common/Forms/Select';
import { Input } from '@/components/common/Forms/Input';

interface pageProps {
  setData: (
    value: 'select-chain' | 'wallet-connect' | 'add-liquidity' | 'pay-fee',
  ) => void;
}

const chainArray = [
  {
    label: (
      <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
        <Ethereum /> Ethereum
      </p>
    ),
    value: 'ethereum',
  },
  {
    label: (
      <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
        <Base /> Base
      </p>
    ),
    value: 'base',
  },
  {
    label: (
      <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
        <Solana /> Solana
      </p>
    ),
    value: 'solana',
  },
  {
    label: (
      <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
        <Ton /> TON
      </p>
    ),
    value: 'ton',
  },
];

const tokenArray = [
  {
    label: (
      <p className="flex items-center gap-1 text-base font-normal text-[#181818]">
        <Temoc /> Temoc
      </p>
    ),
    value: 'temoc',
  },
];

const AddLiquidity: React.FC<pageProps> = ({ setData }) => {
  const [selectChain, setSelectChain] = useState<{
    value: string;
    label: string;
  } | null>(null);

  const [selectToken, setSelectToken] = useState<{
    value: string;
    label: string;
  } | null>(null);

  return (
    <>
      <div className="mx-auto flex flex-col items-center">
        <div className="mt-5 bg-white">
          <div className="p-5 md:w-[690px]">
            <p className="text-sm font-normal text-black-300">
              Supply assets to the selected token and get rewards.
            </p>
            <div className="mt-5 flex items-center gap-4">
              <SelectComponent
                selected={selectChain}
                onSelect={setSelectChain}
                options={chainArray}
                className="w-full"
                placeholder="Chain"
              />
              <Input
                placeholder="Amount"
                name="amount"
                className="!font-normal"
              />
            </div>
            <div className="mt-[15px] flex items-center gap-4">
              <SelectComponent
                selected={selectToken}
                onSelect={setSelectToken}
                options={tokenArray}
                className="w-full"
                placeholder="Token"
              />
              <Input
                placeholder="Max Amount"
                name="max-amount"
                className="!font-normal"
              />
            </div>
          </div>
        </div>

        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setData('wallet-connect')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            onClick={() => setData('pay-fee')}
          >
            Create Coin
            <Arrow />
          </Button>
        </div>
      </div>
    </>
  );
};

export default AddLiquidity;
