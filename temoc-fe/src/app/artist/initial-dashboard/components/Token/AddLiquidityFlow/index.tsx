'use client';
import React, { useState } from 'react';
import AddLiquidity from './AddLiquidity';
import PayFee from './PayFee';
import { LiquidityForm } from '../../Liquidity/LiquidityForm';
import { Button } from '@/components/common';
import { Arrow } from '@/components/common/Icons';
import { Portfolio } from '@/app/app/initial-dashboard/components/Token/AddLiquidityFlow/Portfolio';
import { RemoveLiquidity } from '../../Liquidity/RemoveLiquidity';
import { LiquidityDetails } from '../../Liquidity/LiquidityDetails';

interface pageProps {
  setOpenModal?: (value: boolean) => void;
  data: any;
  setData: (
    value: 'select-chain' | 'wallet-connect' | 'add-liquidity' | 'pay-fee',
  ) => void;
  setAfterCreateToken: (value: true | false) => void;
  setCreating: React.Dispatch<React.SetStateAction<boolean>>;
  setCurrentStep: React.Dispatch<React.SetStateAction<any>>;
  onBack: () => void;
}

export const AddLiquidityFlow: React.FC<pageProps> = ({
  data,
  setData,
  setAfterCreateToken,
  setCreating,
  setCurrentStep,
  onBack,
}) => {
  const [portfolioView, setPortfolioView] = useState<
    'main' | 'details' | 'remove'
  >('main');
  const [selectedPosition, setSelectedPosition] = useState<any>(null);

  const handleDetails = (position: any) => {
    setSelectedPosition(position);
    setPortfolioView('details');
  };

  const handleRemove = (position: any) => {
    setSelectedPosition(position);
    setPortfolioView('remove');
  };

  return (
    <>
      {data === 'select-chain' && (
        <div className="">
          <Button
            className="mb-5 flex !gap-4 !border-none !bg-black-900 py-3 uppercase sm:!px-[40px]"
            onClick={onBack}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <LiquidityForm setData={setData} />
        </div>
      )}

      {data === 'wallet-connect' && (
        <div className="">
          {portfolioView === 'main' && (
            <Portfolio
              onDetails={handleDetails}
              onRemove={handleRemove}
              setCreating={setCreating}
              setCurrentStep={setCurrentStep}
              setData={setData}
            />
          )}
          {portfolioView === 'details' && selectedPosition && (
            <LiquidityDetails setPortfolioView={setPortfolioView} />
          )}
          {portfolioView === 'remove' && selectedPosition && (
            <RemoveLiquidity setPortfolioView={setPortfolioView} />
          )}
        </div>
      )}

      {data === 'add-liquidity' && (
        <div className="">
          <AddLiquidity setData={setData} />
        </div>
      )}
      {data === 'pay-fee' && (
        <div className="mx-auto max-w-[690px]">
          <PayFee setData={setData} setAfterCreateToken={setAfterCreateToken} />
        </div>
      )}
    </>
  );
};

export default AddLiquidityFlow;
