import React, { Fragment } from 'react';
import {
  FairLaunchPresale,
  InfoCircle,
  Liquidity,
} from '@/components/common/Icons';

interface pageProps {
  setCurrentStep: (step: 'choose' | 'liquidity' | 'fair-launch') => void;
}

const CreateToken: React.FC<pageProps> = ({ setCurrentStep }) => {
  return (
    <>
      <div className="mx-auto flex flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          Create Token
        </h3>
        <p className="pt-3 text-black-300 sm:pt-5">Choose One Option</p>
        <div className="mt-[30px] flex cursor-pointer items-center gap-10 sm:gap-14 md:gap-20">
          <div className="block" onClick={() => setCurrentStep('liquidity')}>
            <Liquidity />
            <p className="mt-4 flex items-center gap-1 font-medium text-black-300">
              Add Liquidity <InfoCircle />
            </p>
          </div>
          <div className="block" onClick={() => setCurrentStep('fair-launch')}>
            <FairLaunchPresale />
            <p className="mt-4 flex items-center gap-1 font-medium text-black-300">
              Fair Launch Presale <InfoCircle />
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateToken;
