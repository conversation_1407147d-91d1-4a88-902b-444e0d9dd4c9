import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronUp,
  Info,
  Target,
  Users,
  DollarSign,
  Clock,
  Shield,
  HelpCircle,
} from 'lucide-react';

interface IProps {
  setInfoSection: React.Dispatch<React.SetStateAction<boolean>>;
}

const FairInfoSection = ({ setInfoSection }: IProps) => {
  const [isOpen, setIsOpen] = useState(true);

  return (
    <div className="mx-auto mb-6 h-screen w-full max-w-4xl overflow-auto">
      {/* Header - Always Visible */}
      <div
        className="cursor-pointer rounded-lg border border-blue-200 bg-blue-50 p-4 transition-colors hover:bg-blue-100"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="rounded-full bg-blue-500 p-2">
              <Info className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900">
                What is a Fair Launch?
              </h3>
              <p className="text-sm text-blue-700">
                Click to learn how fair launch works and what each field means
              </p>
            </div>
          </div>
          <div className="text-blue-600">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </div>
        </div>
      </div>

      {/* Expandable Content */}
      {isOpen && (
        <div className="space-y-6 rounded-b-lg border border-t-0 border-gray-200 bg-white p-6">
          {/* What is Fair Launch */}
          <div className="rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 p-4">
            <h4 className="mb-3 text-xl font-bold text-gray-900">
              🚀 What is a Fair Launch?
            </h4>
            <p className="leading-relaxed text-gray-700">
              A Fair Launch is a community-driven way to launch your artist
              token. Instead of setting the price yourself, your fans decide the
              value by participating in a presale. This creates fair pricing and
              builds excitement in your community before your token becomes
              publicly tradeable.
            </p>
          </div>

          {/* How it Works */}
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h4 className="flex items-center text-lg font-semibold text-gray-900">
                <Target className="mr-2 h-5 w-5 text-green-500" />
                How It Works
              </h4>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <span className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
                    1
                  </span>
                  <p>
                    Set your fundraising goals (soft cap = minimum, hard cap =
                    maximum)
                  </p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
                    2
                  </span>
                  <p>
                    Choose how long the presale runs (we recommend 7-30 days)
                  </p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
                    3
                  </span>
                  <p>Fans buy tokens during the presale period</p>
                </div>
                <div className="flex items-start space-x-2">
                  <span className="mt-0.5 flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
                    4
                  </span>
                  <p>
                    If successful, tokens are distributed and trading begins!
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="flex items-center text-lg font-semibold text-gray-900">
                <Users className="mr-2 h-5 w-5 text-purple-500" />
                Benefits for You
              </h4>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span>Lower upfront costs to launch</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-green-500" />
                  <span>Build community excitement</span>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-500" />
                  <span>Fair price discovery by your fans</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-green-500" />
                  <span>Marketing period to promote</span>
                </div>
              </div>
            </div>
          </div>

          {/* Field Explanations */}
          <div className="border-t pt-6">
            <h4 className="mb-4 flex items-center text-lg font-semibold text-gray-900">
              <HelpCircle className="mr-2 h-5 w-5 text-orange-500" />
              What Each Field Means
            </h4>

            <div className="grid gap-4 text-sm md:grid-cols-2">
              <div className="space-y-3">
                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">
                    Participant Amount
                  </h5>
                  <p className="text-gray-600">
                    How much money each fan can contribute (minimum and
                    maximum). This keeps the launch fair for everyone.
                  </p>
                </div>

                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">
                    Listing Amount
                  </h5>
                  <p className="text-gray-600">
                    How much money goes to creating the trading pool after
                    presale ends. Higher = more stable price.
                  </p>
                </div>

                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">Soft Cap</h5>
                  <p className="text-gray-600">
                    Minimum money needed for success. If you don&apos;t reach
                    this, fans get refunded automatically.
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">Hard Cap</h5>
                  <p className="text-gray-600">
                    Maximum money you can raise. Once reached, the presale stops
                    automatically.
                  </p>
                </div>

                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">
                    Start & End Time
                  </h5>
                  <p className="text-gray-600">
                    When your presale begins and ends. Choose dates that give
                    you time to promote to your fans.
                  </p>
                </div>

                <div className="rounded bg-gray-50 p-3">
                  <h5 className="font-semibold text-gray-900">
                    Contribution Limits
                  </h5>
                  <p className="text-gray-600">
                    Minimum and maximum each person can invest. This prevents
                    big investors from buying everything.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tips */}
          <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
            <h4 className="mb-3 text-lg font-semibold text-yellow-800">
              💡 Pro Tips
            </h4>
            <div className="grid gap-3 text-sm text-yellow-700 md:grid-cols-2">
              <div>
                • Set realistic soft caps - better to exceed a lower goal
              </div>
              <div>• Give yourself 2-4 weeks for presale duration</div>
              <div>• Keep max contribution reasonable ($100-$1000)</div>
              <div>• Promote heavily on social media during presale</div>
            </div>
          </div>

          {/* Success Scenarios */}
          <div className="border-t pt-4">
            <h4 className="mb-3 text-lg font-semibold text-gray-900">
              What Happens Next?
            </h4>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="rounded-lg border border-green-200 bg-green-50 p-4">
                <h5 className="mb-2 font-semibold text-green-800">
                  ✅ If Successful (Soft Cap Reached)
                </h5>
                <ul className="space-y-1 text-sm text-green-700">
                  <li>• Tokens are distributed to all participants</li>
                  <li>• Trading pool is created automatically</li>
                  <li>• Your token becomes publicly tradeable</li>
                  <li>• You receive raised funds in your wallet</li>
                </ul>
              </div>

              <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                <h5 className="mb-2 font-semibold text-red-800">
                  ❌ If Unsuccessful (Soft Cap Not Reached)
                </h5>
                <ul className="space-y-1 text-sm text-red-700">
                  <li>• All participants get full refunds</li>
                  <li>• No tokens are distributed</li>
                  <li>• You can try launching again later</li>
                  <li>• No fees charged for failed launches</li>
                </ul>
              </div>
            </div>
          </div>
          <div className="flex gap-3 pt-2">
            <button
              className="flex-1 rounded-lg bg-orange-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-600"
              onClick={() => {
                setIsOpen(false);
                setInfoSection(false);
              }}
            >
              Got It, Let&apos;s Continue
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FairInfoSection;
