import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
// import SelectComponent from '@/components/common/Forms/Select';
import { Arrow } from '@/components/common/Icons';
import { Input } from '@/components/common/Forms/Input';
import { Button, RadioButton } from '@/components/common';
import { dataFairType } from '..';
import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { yupResolver } from '@hookform/resolvers/yup';
import { fairLaunchSchema } from '@/utils/schema';
import InputError from '@/components/common/Forms/InputError';
import Tooltip from '@/components/common/Tooltip';
import FairInfoSection from './FairInfoSection';
import { Info } from 'lucide-react';

// const liquidityArray = [
//   { value: '0', label: '0' },
//   { value: '10', label: '10' },
//   { value: '20', label: '20' },
//   { value: '30', label: '30' },
//   { value: '40', label: '40' },
//   { value: '50', label: '50' },
//   { value: '60', label: '60' },
//   { value: '70', label: '70' },
//   { value: '80', label: '80' },
//   { value: '90', label: '90' },
//   { value: '100', label: '100' },
// ];

// const dexArray = [{ value: 'uniswap', label: 'Uniswap' }];

interface PageProps {
  setDataFair: (value: dataFairType) => void;
  formData: FairLaunchFormData;
  setFormData: React.Dispatch<React.SetStateAction<FairLaunchFormData>>;
  tAddress?: string;
}

const Information: React.FC<PageProps> = ({
  setDataFair,
  formData,
  tAddress,
  setFormData,
}) => {
  const [infoSection, setInfoSection] = useState(true);
  const {
    control,
    handleSubmit,
    setValue,
    register,
    formState: { errors },
  } = useForm<FairLaunchFormData>({
    defaultValues: {
      tokenAddress: formData.tokenAddress || '',
      currency: 'eth',
    },
    //@ts-ignore
    resolver: yupResolver(fairLaunchSchema),
  });

  // You might want to extend the interface to include a 'dex' field locally, or manage it separately.
  // For this example, I'll add dex to the form data manually:

  useEffect(() => {
    if (tAddress) {
      setValue('tokenAddress', tAddress); // ✅ sets in the form
    }
  }, [tAddress, setValue, setFormData]);
  const onSubmit = (data: FairLaunchFormData) => {
    // Convert liquidityPercent from string to number
    if (typeof data.liquidityPercent === 'string') {
      data.liquidityPercent = Number(data.liquidityPercent);
    }
    // Map your dex value here if you add dex field in interface

    setFormData((prev) => ({
      ...prev,
      tokenAddress: data.tokenAddress,
      currency: data.currency,
      fairLaunchAmount: data.fairLaunchAmount,
      listingOn: data.listingOn,
      softCap: data.softCap,
      hardCap: data.hardCap,
      minBuy: data.minBuy,
      maxBuy: data.maxBuy,
      liquidityPercent: data.liquidityPercent,
      dex: data.dex,
    }));
    setDataFair('fairlaunch-timing');
  };

  return infoSection ? (
    <FairInfoSection setInfoSection={setInfoSection} />
  ) : (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="mx-auto flex flex-col items-center"
    >
      <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
        FAIR LAUNCH INFORMATION
      </h3>
      <div className="mt-5 w-full bg-white p-5 md:w-[690px]">
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between gap-4">
            <div className="relative flex-1">
              <p className="mb-1">Token Address</p>
              <Input
                placeholder="Token Address"
                // {...register('tokenAddress', {
                //   required: 'Token address is required',
                // })}
                name="tokenAddress"
                readOnly
                register={register}
                className="w-full"
              />
              <InputError error={errors.tokenAddress?.message} />
            </div>
            <div className="flex items-center">
              <button
                onClick={() => setInfoSection(true)}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100"
              >
                <Info className="h-4 w-4 text-blue-600" />
              </button>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="relative">
              <p className="mb-1">Fairlaunch Amount</p>
              <Input
                name="fairLaunchAmount"
                register={register}
                type="number"
                step="any"
                placeholder="Fairlaunch Amount"
                className="!font-normal"
                // error={errors.softCap?.message}
              />
              <InputError error={errors.fairLaunchAmount?.message} />
              <Tooltip description="This is the fixed price per token during the fairlaunch." />
            </div>

            <div className="relative">
              <p className="mb-1">Listing Amount</p>
              <Input
                name="listingOn"
                register={register}
                step="any"
                placeholder={
                  // <div className="flex items-center gap-1">
                  'Listing Amount'
                  // </div>
                }
                type="number"
                className="!font-normal"
                // error={errors.softCap?.message}
              />
              <InputError error={errors?.listingOn?.message} />
              <Tooltip description="This is the token price when it goes live on the exchange after the presale." />
            </div>
          </div>

          <div>
            <p className="mb-1">Soft Cap</p>
            <Input
              name="softCap"
              register={register}
              placeholder="Soft Cap"
              step="any"
              type="number"
              className="!font-normal"
            />
            <InputError error={errors?.softCap?.message} />
          </div>

          <div className="">
            <p className="mb-1">Hard Cap</p>
            <Input
              name="hardCap"
              register={register}
              placeholder="Hard Cap"
              step="any"
              type="number"
              className="!font-normal"
            />
            <InputError error={errors?.hardCap?.message} />
          </div>
          <p className="text-sm leading-none text-black-300">
            Contribution Level per User
          </p>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <p className="mb-1">Min Contribution</p>
              <Input
                register={register}
                name="minBuy"
                step="any"
                placeholder="Min Contribution"
                type="number"
                className="!font-normal"
              />
              <InputError error={errors?.minBuy?.message} />
            </div>
            <div>
              <p className="mb-1">Max Contribution</p>
              <Input
                register={register}
                name="maxBuy"
                placeholder="Max Contribution"
                type="number"
                step="any"
                className="!font-normal"
              />
              <InputError error={errors?.maxBuy?.message} />
            </div>
          </div>
          <div className="">
            <p className="text-sm leading-none text-black-300">
              Currency{' '}
              <span className="text-xs text-black-600">
                Users will pay with ETH for your token
              </span>
            </p>
            <Controller
              control={control}
              name="currency"
              render={({ field }) => (
                <div className="mt-2.5 flex items-center gap-6">
                  {['eth', 'usdt', 'usdc'].map((curr) => (
                    <RadioButton
                      key={curr}
                      label={curr.toUpperCase()}
                      value={curr}
                      name="currency"
                      checked={field.value === curr}
                      onChange={() => field.onChange(curr)}
                    />
                  ))}
                </div>
              )}
            />
          </div>
          {/* <p className="text-sm leading-none text-black-300">
            Liquidity % and DEX
          </p>
          <div className="flex flex-col gap-[15px] sm:flex-row">
            <Controller
              control={control}
              name="liquidityPercent"
              render={({ field }) => (
                <SelectComponent
                  options={liquidityArray}
                  selected={
                    field.value
                      ? {
                          value: field.value.toString(),
                          label: field.value.toString(),
                        }
                      : null
                  }
                  onSelect={(val) =>
                    field.onChange(val ? Number(val.value) : null)
                  }
                  placeholder={
                    <div className="flex items-center gap-1">
                      Liquidity % <InfoCircle />
                    </div>
                  }
                />
              )}
            />
            <Controller
              control={control}
              name="dex"
              render={({ field }) => (
                <SelectComponent
                  options={dexArray}
                  selected={
                    field.value
                      ? { value: field.value, label: field.value }
                      : dexArray[0]
                  }
                  onSelect={(val) => field.onChange(val?.value)}
                  placeholder={
                    <div className="flex items-center gap-1">
                      DEX <InfoCircle />
                    </div>
                  }
                />
              )}
            />
          </div> */}
        </div>
      </div>

      <div className="mt-5 flex w-full justify-between">
        <Button
          type="button"
          className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
          onClick={() => setDataFair('select-chain')}
        >
          <Arrow className="rotate-180" />
          Back
        </Button>
        <Button
          type="submit"
          className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
        >
          Next Step
          <Arrow />
        </Button>
      </div>
    </form>
  );
};

export default Information;
