import React, { useContext, useState } from 'react';
import { <PERSON>ton, RadioButton } from '@/components/common';
import { Arrow, Base, Ethereum } from '@/components/common/Icons';
import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { ChainContext } from '@/context/ChainContextProvider';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';

interface pageProps {
  setDataFair: (value: 'select-chain' | 'fairlaunch-information') => void;
  onBack: () => void;
  formData: FairLaunchFormData;
  setFormData: React.Dispatch<React.SetStateAction<FairLaunchFormData>>;
}
const chains = [
  // { value: 'ethereum', label: 'Ethereum', chainId: 1, Icon: Ethereum },
  // { value: 'base', label: 'Base', chainId: 8453, Icon: Base },
  {
    value: 'base-sepolia',
    label: 'Base Sepolia',
    chainId: 84532,
    Icon: Base,
  },
  { value: 'sepolia', label: 'Sepolia', chainId: 11155111, Icon: Ethereum },

  // { value: 'solana', label: 'Solana', chainId: 101, Icon: Solana },
  // { value: 'ton', label: 'TON', chainId: 110, Icon: Ton },
];
const SelectChain: React.FC<pageProps> = ({
  setDataFair,
  onBack,
  setFormData,
}) => {
  const [selected, setSelected] = useState<string>(chains[1].value);
  const { switchCurrentChain } = useContext(ChainContext);
  const { primaryWallet } = useDynamicContext();

  const handleChainSelect = async (value: string) => {
    setSelected(value);
    const selectedChain = chains.find((chain) => chain.value === value);
    if (selectedChain) {
      setFormData((prev) => ({
        ...prev,
        network: selectedChain.value,
        chainId: selectedChain.chainId,
      }));
      switchCurrentChain(11155111);
      if (primaryWallet) {
        await primaryWallet.switchNetwork(11155111);
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    }
  };

  return (
    <div className="mx-auto flex flex-col items-center">
      <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
        Select Chain
      </h3>
      <div className="mt-5 bg-white">
        <div className="p-5 md:w-[690px]">
          <p className="text-sm font-normal text-black-300">Chains</p>
          <div className="mt-5 flex flex-wrap items-center gap-6 md:flex-nowrap">
            {chains.map(({ value, label, Icon }) => (
              <RadioButton
                key={value}
                label={
                  <p className="flex items-center gap-1">
                    <Icon /> {label}
                  </p>
                }
                value={value}
                name={value}
                checked={selected === value}
                onChange={() => handleChainSelect(value)}
              />
            ))}
          </div>
        </div>
      </div>
      <div className="mt-5 flex w-full justify-between">
        <Button
          className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
          onClick={onBack}
        >
          <Arrow className="rotate-180" />
          Back
        </Button>
        <Button
          className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
          // onClick={() => setDataFair('select-chain-wallet')}
          onClick={() => setDataFair('fairlaunch-information')}
        >
          Next Step
          <Arrow />
        </Button>
      </div>
    </div>
  );
};

export default SelectChain;
