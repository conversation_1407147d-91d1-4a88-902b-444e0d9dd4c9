import React, { useState, useEffect } from 'react';
import { Button, RadioButton } from '@/components/common';
import { Arrow } from '@/components/common/Icons';
import { Input } from '@/components/common/Forms/Input';
import { dataFairType } from '..';
// import CreateToken from '@/components/ui/CreateToken';
import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { Controller, useForm } from 'react-hook-form';
import InputError from '@/components/common/Forms/InputError';

interface pageProps {
  setDataFair: (value: dataFairType) => void;
  formData: FairLaunchFormData;
  setFormData: React.Dispatch<React.SetStateAction<FairLaunchFormData>>;
  tAddress?: string;
}

type FormValues = {
  tokenAddress: string;
  currency: 'eth' | 'usdt' | 'usdc';
  listingOptions: 'manual-listing' | 'auto-listing';
  poolType: 'presale' | 'private-sale' | 'seed-sale';
  affiliateProgram: 'disable-affiliate' | 'enable-affiliate';
  logoUrl: FileList | null;
};
const SelectChainWallet: React.FC<pageProps> = ({
  setDataFair,
  formData,
  setFormData,
  tAddress,
}) => {
  useState<string>('manual-listing');

  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      tokenAddress: formData.tokenAddress || '',
      currency: 'eth',
      listingOptions: 'manual-listing',
      poolType: 'presale',
      affiliateProgram: 'disable-affiliate',
      logoUrl: null,
    },
  });

  useEffect(() => {
    if (tAddress) {
      setValue('tokenAddress', tAddress); // ✅ sets in the form
    }
  }, [tAddress, setValue, setFormData]);

  const onSubmit = (data: FormValues) => {
    setFormData((prev) => ({
      ...prev,
      tokenAddress: data.tokenAddress,
      currency: data.currency,
      listingOptions: data.listingOptions,
      poolType: data.poolType,
      affiliateProgram: data.affiliateProgram,
      logoUrl: null, // save File object if needed
    }));

    setDataFair('fairlaunch-information');
  };
  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mx-auto flex flex-col items-center"
      >
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          FAIR LAUNCH INFORMATION
        </h3>
        <div className="mt-5 bg-white p-5 md:w-[690px]">
          <div className="relative mt-5 flex items-center gap-2">
            <Input
              placeholder="Token Address"
              {...register('tokenAddress', {
                required: 'Token address is required',
              })}
              name="tokenAddress"
              readOnly
              register={register}
              className=""
            />
          </div>
          <InputError error={errors.tokenAddress?.message} />
          <p className="mt-5 text-sm leading-none text-black-300">
            Currency{' '}
            <span className="text-xs text-black-600">
              Users will pay with ETH for your token
            </span>
          </p>
          <Controller
            control={control}
            name="currency"
            render={({ field }) => (
              <div className="mt-2.5 flex items-center gap-6">
                {['eth', 'usdt', 'usdc'].map((curr) => (
                  <RadioButton
                    key={curr}
                    label={curr.toUpperCase()}
                    value={curr}
                    name="currency"
                    checked={field.value === curr}
                    onChange={() => field.onChange(curr)}
                  />
                ))}
              </div>
            )}
          />
        </div>

        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            type="button"
            onClick={() => setDataFair('select-chain')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
          >
            Next Step
            <Arrow />
          </Button>
        </div>
      </form>
    </>
  );
};

export default SelectChainWallet;
