import React, { useState, useEffect } from 'react';
import { Button, RadioButton } from '@/components/common';
import { Arrow } from '@/components/common/Icons';
import { Input } from '@/components/common/Forms/Input';
import { dataFairType } from '..';
// import CreateToken from '@/components/ui/CreateToken';
import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { Controller, useForm } from 'react-hook-form';
import InputError from '@/components/common/Forms/InputError';

interface pageProps {
  setDataFair: (value: dataFairType) => void;
  formData: FairLaunchFormData;
  setFormData: React.Dispatch<React.SetStateAction<FairLaunchFormData>>;
  tAddress?: string;
}

type FormValues = {
  tokenAddress: string;
  currency: 'eth' | 'usdt' | 'usdc';
  listingOptions: 'manual-listing' | 'auto-listing';
  poolType: 'presale' | 'private-sale' | 'seed-sale';
  affiliateProgram: 'disable-affiliate' | 'enable-affiliate';
  logoUrl: FileList | null;
};
const SelectChainWalletOld: React.FC<pageProps> = ({
  setDataFair,
  formData,
  setFormData,
  tAddress,
}) => {
  useState<string>('manual-listing');

  const {
    register,
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      tokenAddress: formData.tokenAddress || '',
      currency: 'eth',
      listingOptions: 'manual-listing',
      poolType: 'presale',
      affiliateProgram: 'disable-affiliate',
      logoUrl: null,
    },
  });

  useEffect(() => {
    if (tAddress) {
      setValue('tokenAddress', tAddress); // ✅ sets in the form
    }
  }, [tAddress, setValue, setFormData]);

  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const file = e.target.files?.[0];
  //   if (file) {
  //     setFileName(file.name);
  //   }
  // };

  const onSubmit = (data: FormValues) => {
    setFormData((prev) => ({
      ...prev,
      tokenAddress: data.tokenAddress,
      currency: data.currency,
      listingOptions: data.listingOptions,
      poolType: data.poolType,
      affiliateProgram: data.affiliateProgram,
      logoUrl: null, // save File object if needed
    }));

    setDataFair('fairlaunch-information');
  };
  return (
    <>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="mx-auto flex flex-col items-center"
      >
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          Select Chain
        </h3>
        <div className="mt-5 bg-white p-5 md:w-[690px]">
          <div className="relative mt-5 flex items-center gap-2">
            <Input
              placeholder="Token Address"
              {...register('tokenAddress', {
                required: 'Token address is required',
              })}
              name="tokenAddress"
              readOnly
              register={register}
              className=""
            />
            {/* <Button
              className="!h-10 !w-[120px] !text-xs"
              onClick={() => {
                setOpenModal(true);
              }}
            >
              {' '}
              CREATE TOKEN{' '}
            </Button> */}
          </div>
          <InputError error={errors.tokenAddress?.message} />
          {/* <p className="mt-2.5 text-[10px] leading-none text-[#777777]">
            Creation Fee: 0.2 ETH. (Balance: 100 ETH)
          </p> */}

          {/* Currency Radios */}
          <p className="mt-5 text-sm leading-none text-black-300">
            Currency{' '}
            <span className="text-xs text-black-600">
              Users will pay with ETH for your token
            </span>
          </p>
          <Controller
            control={control}
            name="currency"
            render={({ field }) => (
              <div className="mt-2.5 flex items-center gap-6">
                {['eth', 'usdt', 'usdc'].map((curr) => (
                  <RadioButton
                    key={curr}
                    label={curr.toUpperCase()}
                    value={curr}
                    name="currency"
                    checked={field.value === curr}
                    onChange={() => field.onChange(curr)}
                  />
                ))}
              </div>
            )}
          />

          {/* Free Options */}
          {/* <p className="mb-2.5 mt-5 text-sm leading-none text-black-300 xs:my-2">
            Free options
          </p>
          <RadioButton
            label="5% ETH raised only"
            value="eth-raised-only"
            name="eth-raised-only"
            checked
          
          /> */}

          {/* <p className="mt-5 text-sm leading-none text-black-300">
            Listing options
          </p>
          <Controller
            control={control}
            name="listingOptions"
            render={({ field }) => (
              <div className="mt-2.5 flex items-center gap-6 xs:flex-wrap xs:gap-2">
                <RadioButton
                  label="Auto Listing"
                  value="auto-listing"
                  name="listingOptions"
                  checked={field.value === 'auto-listing'}
                  onChange={() => field.onChange('auto-listing')}
                />
                <RadioButton
                  label={
                    <p className="flex items-end gap-0.5 text-xs leading-none sm:text-sm xs:flex-wrap">
                      Manual Listing{' '}
                      <span className="text-xs text-black-600">
                        (Recommended for Seed/Private Sale)
                      </span>
                    </p>
                  }
                  value="manual-listing"
                  name="listingOptions"
                  checked={field.value === 'manual-listing'}
                  onChange={() => field.onChange('manual-listing')}
                />
              </div>
            )}
          /> */}

          {/* Pool Type */}
          {/* <p className="mt-5 text-sm leading-none text-black-300 xs:mt-3">
            Choose a pool type
          </p>
          <Controller
            control={control}
            name="poolType"
            render={({ field }) => (
              <div className="mt-2.5 flex items-center gap-6">
                {[
                  { label: 'Presale', value: 'presale' },
                  { label: 'Private Sale', value: 'private-sale' },
                  { label: 'Seed Sale', value: 'seed-sale' },
                ].map(({ label, value }) => (
                  <RadioButton
                    key={value}
                    label={label}
                    value={value}
                    name="poolType"
                    checked={field.value === value}
                    onChange={() => field.onChange(value)}
                  />
                ))}
              </div>
            )}
          />

        
          <p className="mt-5 text-sm leading-none text-black-300 xs:mt-3">
            Affiliate Program
          </p>
          <Controller
            control={control}
            name="affiliateProgram"
            render={({ field }) => (
              <div className="mt-2.5 flex items-center gap-6">
                <RadioButton
                  label="Disable Affiliate"
                  value="disable-affiliate"
                  name="affiliateProgram"
                  checked={field.value === 'disable-affiliate'}
                  onChange={() => field.onChange('disable-affiliate')}
                />
                <RadioButton
                  label="Enable Affiliate"
                  value="enable-affiliate"
                  name="affiliateProgram"
                  checked={field.value === 'enable-affiliate'}
                  onChange={() => field.onChange('enable-affiliate')}
                />
              </div>
            )}
          /> */}
        </div>

        <div className="mt-5 flex w-full justify-between">
          <Button
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            type="button"
            onClick={() => setDataFair('select-chain')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
          >
            Next Step
            <Arrow />
          </Button>
        </div>
      </form>
    </>
  );
};

export default SelectChainWalletOld;
