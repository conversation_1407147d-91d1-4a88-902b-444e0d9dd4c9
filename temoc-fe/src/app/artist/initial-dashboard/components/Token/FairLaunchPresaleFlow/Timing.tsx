import React from 'react';
import { useForm } from 'react-hook-form';
import { dataFairType } from '..';
import { Arrow } from '@/components/common/Icons';
import { Input } from '@/components/common/Forms/Input';
import { Button } from '@/components/common';
import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { useMutation } from '@tanstack/react-query';
import { presaleService } from '@/services/presale.service';
import { toast } from 'react-toastify';
import { yupResolver } from '@hookform/resolvers/yup';
import { timingSchema } from '@/utils/schema';
import InputError from '@/components/common/Forms/InputError';

interface pageProps {
  setDataFair: (value: dataFairType) => void;
  setAfterCreateToken: (value: true | false) => void;
  formData: FairLaunchFormData;
  setFormData: React.Dispatch<React.SetStateAction<FairLaunchFormData>>;
}

const Timing: React.FC<pageProps> = ({
  setDataFair,
  setAfterCreateToken,
  formData,
  setFormData,
}) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(timingSchema),
  });

  const presaleStartValue = watch('presaleStart');
  const presaleEndValue = watch('presaleEnd');

  console.log(presaleEndValue, presaleStartValue);

  const presaleMutation = useMutation({
    mutationFn: (data: FairLaunchFormData) =>
      presaleService.createPresale(data),
    onSuccess: () => {
      toast.success('Presale Created successfully');
    },
    onError: (data) => {
      console.log(data, 'presale error');
      toast.error('Failed to create presale. Please try again.');
    },
  });

  const onSubmit = async (data: any) => {
    const presaleStart = new Date(data.presaleStart);
    const presaleEnd = new Date(data.presaleEnd);
    const liquidityUnlock = new Date(data.liquidityUnlock);

    const unlockInterval = data.unlockInterval
      ? Number(data.unlockInterval)
      : undefined;

    // Update your formData state with Date objects
    setFormData({
      ...formData,
      presaleStart,
      presaleEnd,
      liquidityUnlock,
      unlockInterval,
    });

    // Pass the correctly formatted formData with Date objects to the API
    await presaleMutation.mutateAsync({
      ...formData,
      presaleStart,
      presaleEnd,
      liquidityUnlock,
      unlockInterval, // This will be undefined if not set
    });

    setAfterCreateToken(true);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="mx-auto flex flex-col items-center">
        <h3 className="text-center font-display text-2xl font-semibold uppercase text-black-300 sm:text-3xl">
          FAIR LAUNCH timings
        </h3>
        <div className="mt-5 w-full bg-white p-5 md:w-[690px]">
          <div className="outer grid grid-cols-1 gap-4 sm:grid-cols-2">
            {/* Presale Start */}
            <div className="inner">
              <p className="mb-2 text-sm leading-none text-black-300">
                Presale Start Time
              </p>
              <div className="">
                <Input
                  name="presaleStart"
                  register={register}
                  type="datetime-local"
                  placeholder="Presale Start Time"
                  className="!font-normal"
                  min={new Date().toISOString().slice(0, 16)}
                />
                <InputError error={errors.presaleStart?.message} />
              </div>
            </div>
            {/* Presale End */}
            <div className="inner">
              <p className="mb-2 text-sm leading-none text-black-300">
                Presale End Time
              </p>
              <div className="">
                <Input
                  name="presaleEnd"
                  type="datetime-local"
                  placeholder="Presale End Time"
                  className="!font-normal"
                  register={register}
                  min={
                    presaleStartValue
                      ? new Date(presaleStartValue).toISOString().slice(0, 16)
                      : undefined
                  }
                />
                <InputError error={errors.presaleEnd?.message} />
              </div>
            </div>
            {/* Liquidity Unlock */}
            <div className="inner">
              <p className="mb-2 text-sm leading-none text-black-300">
                Liquidity Unlock Time
              </p>
              <div className="">
                <Input
                  register={register}
                  name="liquidityUnlock"
                  type="datetime-local"
                  placeholder="Liquidity Unlock Time"
                  className="!font-normal"
                  min={
                    presaleEndValue
                      ? new Date(presaleEndValue).toISOString().slice(0, 16)
                      : undefined
                  }
                />
                <InputError error={errors.liquidityUnlock?.message} />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-5 flex w-full justify-between">
          <Button
            type="button"
            className="flex !gap-4 !border-none !bg-black-900 py-5 uppercase sm:!px-[60px]"
            onClick={() => setDataFair('fairlaunch-information')}
          >
            <Arrow className="rotate-180" />
            Back
          </Button>
          <Button
            type="submit"
            className="flex !gap-4 !border-none !py-5 uppercase sm:!px-[60px]"
            disabled={presaleMutation.isPending}
            isLoading={presaleMutation.isPending}
          >
            Create Coin
            <Arrow />
          </Button>
        </div>
      </div>
    </form>
  );
};

export default Timing;
