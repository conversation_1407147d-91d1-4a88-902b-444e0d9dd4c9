import React, { useState } from 'react';
import Select<PERSON>hain from './SelectChain';
import Select<PERSON>hainWallet from './SelectChainWallet';
import Information from './Information';
import { dataFairType } from '..';
import Timing from './Timing';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';

interface pageProps {
  dataFair: string;
  setDataFair: (value: dataFairType) => void;
  onBack: () => void;
  setAfterCreateToken: (value: true | false) => void;
  tAddress?: string;
}

const FairLaunchPresaleFlow: React.FC<pageProps> = ({
  dataFair,
  setDataFair,
  onBack,
  setAfterCreateToken,
  tAddress,
}) => {
  const { primaryWallet } = useDynamicContext();
  const [formData, setFormData] = useState<any>({
    walletAddress: primaryWallet?.address || '',
    ownerAddress: primaryWallet?.address || '',
  });

  return (
    <>
      {dataFair === 'select-chain' && (
        <div className="mx-auto max-w-[690px]">
          <SelectChain
            setDataFair={setDataFair}
            onBack={onBack}
            formData={formData}
            setFormData={setFormData}
          />
        </div>
      )}
      {dataFair === 'select-chain-wallet' && (
        <div className="mx-auto max-w-[690px]">
          <SelectChainWallet
            setDataFair={setDataFair}
            formData={formData}
            setFormData={setFormData}
            tAddress={tAddress}
          />
        </div>
      )}
      {dataFair === 'fairlaunch-information' && (
        <div className="mx-auto max-w-[690px]">
          <Information
            setDataFair={setDataFair}
            formData={formData}
            setFormData={setFormData}
            tAddress={tAddress}
          />
        </div>
      )}
      {dataFair === 'fairlaunch-timing' && (
        <div className="mx-auto w-full max-w-[690px]">
          <Timing
            setDataFair={setDataFair}
            setAfterCreateToken={setAfterCreateToken}
            formData={formData}
            setFormData={setFormData}
          />
        </div>
      )}
    </>
  );
};

export default FairLaunchPresaleFlow;
