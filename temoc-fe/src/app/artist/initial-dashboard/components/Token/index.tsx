'use client';
import React, { useState } from 'react';
import { Button } from '@/components/common';
import { MdArrowOutward } from 'react-icons/md';
import FairLaunchPresaleFlow from './FairLaunchPresaleFlow';
import AddLiquidityFlow from './AddLiquidityFlow';
import CreateToken from './CreateToken';
import Modal from '@/components/common/Modal';
import CreateTokenModal from '@/components/ui/CreateToken';
import TokenPreview from './TokenPreview';
import { tokenService } from '@/services/token.service';
import { useQuery } from '@tanstack/react-query';

type dataType = 'select-chain' | 'wallet-connect' | 'add-liquidity' | 'pay-fee';

type currentStepType = 'choose' | 'liquidity' | 'fair-launch';

export type dataFairType =
  | 'select-chain'
  | 'select-chain-wallet'
  | 'fairlaunch-information'
  | 'fairlaunch-timing';

interface pageProps {
  setAfterCreateToken: (value: true | false) => void;
}

const Token: React.FC<pageProps> = ({ setAfterCreateToken }) => {
  const [creating, setCreating] = useState(false);
  const [data, setData] = useState<dataType>('select-chain');
  const [tAddress, setTAddress] = useState('');
  const [dataFair, setDataFair] = useState<dataFairType>('select-chain');
  const [currentStep, setCurrentStep] = useState<currentStepType>('choose');

  const [, setTokenAddress] = useState<string | null>(null);
  const [, setLogo] = useState<string | null>(null);
  const [openModal, setOpenModal] = useState(false);
  // const handleCreateClick = () => {
  //   setCreating(true);
  //   setCurrentStep('choose'); // reset to Choose step
  // };
  const { data: tokenData } = useQuery({
    queryKey: ['token'],
    queryFn: () => tokenService.getToken(),
  });

  const token = tokenData?.data;
  return (
    <div>
      {!creating ? (
        <div className="flex w-full flex-wrap items-center justify-between gap-3">
          <h3 className="text-xl uppercase text-[#333333] sm:text-[22px]">
            Your Token
          </h3>
          {token?.length >= 1 ? null : (
            <div className="flex items-center gap-3">
              <Button
                className="group !text-xs font-semibold uppercase"
                onClick={() => {
                  setOpenModal(true);
                }}
              >
                Create a Token
                <MdArrowOutward className="text-sm text-white" />
              </Button>
            </div>
          )}
        </div>
      ) : currentStep === 'choose' ? (
        <CreateToken setCurrentStep={setCurrentStep} />
      ) : currentStep === 'liquidity' ? (
        <AddLiquidityFlow
          setOpenModal={setOpenModal}
          setAfterCreateToken={setAfterCreateToken}
          data={data}
          setData={setData}
          setCreating={setCreating}
          setCurrentStep={setCurrentStep}
          onBack={() => setCreating(false)}
        />
      ) : currentStep === 'fair-launch' ? (
        <FairLaunchPresaleFlow
          setAfterCreateToken={setAfterCreateToken}
          dataFair={dataFair}
          setDataFair={setDataFair}
          onBack={() => setCreating(false)}
          tAddress={tAddress}
        />
      ) : null}

      {!creating && (
        <TokenPreview
          setCreating={setCreating}
          setCurrentStep={setCurrentStep}
          setAfterCreateToken={setAfterCreateToken}
          setTAddress={setTAddress}
          setData={setData}
        />
      )}

      <Modal
        className="hideScrollbar !h-max !pb-6 sm:!max-w-[620px] sm:!px-5 md:!max-w-[730px] md:pb-0"
        show={openModal}
        hide={setOpenModal}
      >
        <CreateTokenModal
          setOpenModal={setOpenModal}
          setTokenAddress={setTokenAddress}
          setLogo={setLogo}
        />
      </Modal>
    </div>
  );
};

export default Token;
