'use client';
import Button from '@/components/common/Button';
import Arrow from '@/components/common/Icons/Arrow';
import NoCollection from '@/components/common/Icons/NoCollection';
import React, { useEffect, useRef, useState } from 'react';
import { MdArrowOutward } from 'react-icons/md';
import { AiTwotoneAudio } from 'react-icons/ai';
import { RxCross1 } from 'react-icons/rx';
import { useMutation } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';
import SongsDetails from './SongsDetails';

interface AudioFilesProps {
  onBackToAlbumDetail: () => void;
  onNextStep: () => void;
  album: any;
}

const AudioFile: React.FC<AudioFilesProps> = ({
  onBackToAlbumDetail,
  onNextStep,
  album,
}) => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const [recordedDuration, setRecordedDuration] = useState<number | null>(null);
  const [fileProgress] = useState<number>(5);
  const [isUploading] = useState(false);
  const [files, setFiles] = useState<
    { file: File; id: string; progress: number; duration?: number }[]
  >([]);
  const [recordfiles, setRecordFiles] = useState<any>([]);
  const [uploadComplete] = useState(false);

  const inputRef = useRef<HTMLInputElement | null>(null);

  const [isRecording, setIsRecording] = useState(false);
  const [seconds, setSeconds] = useState(0);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null,
  );
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isSongsDetails, setIsSongsDetails] = useState<boolean | null>(null);
  const [, setCard] = useState(false);
  const [, setLibraryCreated] = useState(false);

  const chunks = useRef<Blob[]>([]);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const trackMutation = useMutation({
    mutationFn: (data: any) => libraryService.createTracks(data),
    onSuccess: () => {
      toast.success('Track created successfully');
      onNextStep();
    },
    onError: (error: any) => {
      console.log('Error:', error);
      toast.error(error?.response?.data?.message);
    },
  });

  // const randomId = Math.floor(10000 + Math.random() * 90000);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles) return;

    Array.from(selectedFiles).forEach((file) => {
      const audio = document.createElement('audio');
      audio.src = URL.createObjectURL(file);

      audio.addEventListener('loadedmetadata', () => {
        const durationInMinutes = audio.duration / 60;
        const uniqueId = crypto.randomUUID();

        setFiles((prev) => {
          const newFiles = [
            ...prev,
            {
              file,
              id: uniqueId,
              progress: 0,
              duration: durationInMinutes,
            },
          ];
          simulateUploadProgress(newFiles.length - 1); // Simulate progress
          return newFiles;
        });
      });
    });
  };

  const simulateUploadProgress = (index: number) => {
    let progress = 0;
    const interval = setInterval(() => {
      if (progress < 100) {
        progress += 5;
        setFiles((prevFiles) => {
          const updatedFiles = [...prevFiles];
          updatedFiles[index] = {
            ...updatedFiles[index],
            progress,
          };
          return updatedFiles;
        });
      } else {
        clearInterval(interval);
      }
    }, 100);
  };

  // const handleFileRemove = () => {
  //   setFileName(null);
  //   setFileProgress(0);
  //   setIsUploading(false);
  //   setUploadComplete(false);
  //   if (inputRef.current) inputRef.current.value = '';
  // };

  useEffect(() => {
    if (isRecording) {
      timerRef.current = setInterval(() => {
        setSeconds((prev) => prev + 1);
      }, 1000);
    } else {
      if (timerRef.current) clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [isRecording]);

  const handleStart = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream);
    const audioContext = new AudioContext();
    const source = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048;

    analyserRef.current = analyser;
    source.connect(analyser);

    drawWaveform(analyser);

    chunks.current = [];

    recorder.ondataavailable = (e) => {
      if (e.data.size > 0) {
        chunks.current.push(e.data);
      }
    };

    recorder.onstop = () => {
      const blob = new Blob(chunks.current, { type: 'audio/mp3' });
      setAudioBlob(blob);
      setRecordedDuration(seconds); // ✅ save the recorded time
      // setSeconds(0); ← REMOVE this line
    };

    recorder.start();
    setMediaRecorder(recorder);
    setIsRecording(true);
  };

  const handleStop = () => {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      mediaRecorder.stop();
      mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }
    setIsRecording(false);
  };

  const drawWaveform = (analyser: AnalyserNode) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const canvasCtx = canvas.getContext('2d');
    const bufferLength = analyser.fftSize;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!canvasCtx) return;

      analyser.getByteTimeDomainData(dataArray);

      canvasCtx.fillStyle = '#ffffff';
      canvasCtx.fillRect(0, 0, canvas.width, canvas.height);

      canvasCtx.lineWidth = 2;
      canvasCtx.strokeStyle = '#4F46E5'; // primary color

      canvasCtx.beginPath();

      const sliceWidth = (canvas.width * 1.0) / bufferLength;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        const v = dataArray[i] / 128.0;
        const y = (v * canvas.height) / 2;

        if (i === 0) {
          canvasCtx.moveTo(x, y);
        } else {
          canvasCtx.lineTo(x, y);
        }

        x += sliceWidth;
      }

      canvasCtx.lineTo(canvas.width, canvas.height / 2);
      canvasCtx.stroke();

      animationIdRef.current = requestAnimationFrame(draw);
    };

    draw();
  };

  const formatTime = (sec: number) => {
    const m = Math.floor(sec / 60)
      .toString()
      .padStart(2, '0');
    const s = (sec % 60).toString().padStart(2, '0');
    return `${m}:${s}`;
  };

  const handleFileRemove = (index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const progress = Math.min((recordedDuration ?? seconds) / 60, 1) * 100;

  const handleSubmit = async () => {
    if (!files.length && !audioBlob) {
      toast.error('Please upload or record at least one audio file.');
      return;
    }

    const formData = new FormData();
    formData.append('albumId', album._id);
    formData.append('title', 'Audio File');

    // ✅ Append uploaded files from the `files` state
    files.forEach(({ file }) => {
      formData.append('audioFile', file);
    });

    // ✅ Also append recorded audio if available
    if (audioBlob) {
      const recordedFile = new File([audioBlob], 'recorded-audio.webm', {
        type: 'audio/mp3',
      });
      formData.append('audioFile', recordedFile);
    }

    trackMutation.mutate(formData);
  };

  useEffect(() => {
    if (audioBlob) {
      const recordedFile = new File([audioBlob], 'recorded-audio.webm', {
        type: 'audio/mp3',
      });
      setRecordFiles([recordedFile]);
    }
  }, [audioBlob]);

  console.log(files, 'files');
  console.log(recordfiles, 'recordfiles');
  return (
    <div>
      {isSongsDetails ? (
        <SongsDetails
          albumId={album._id}
          album={[...files, ...recordfiles]}
          setRecordFiles={setRecordFiles}
          setFiles={setFiles}
          onBackToAlbumDetail={onBackToAlbumDetail}
          onBack={() => {
            setIsSongsDetails(false);
          }}
          onSubmit={() => {
            handleSubmit();
            setLibraryCreated(true);
            setCard(true);
          }}
        />
      ) : (
        <>
          <h3 className="text-center text-2xl font-semibold uppercase text-[#333333] sm:text-[30px]">
            Upload your audio files
          </h3>

          <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
            <div className="rounded-[14px] border border-[#CECECE] p-5 transition hover:border-primary sm:p-[30px]">
              {files.length > 0 && (
                <div className="mb-2 space-y-2">
                  {files.map(({ file, duration }, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between gap-3 rounded-md border border-gray-400 p-1"
                    >
                      <p className="w-[80px] truncate text-sm text-[#333333]">
                        {file.name}
                      </p>
                      {isUploading ? (
                        <div className="relative h-1 flex-1 overflow-hidden rounded-full bg-[#CECECE]">
                          <div
                            className="h-1 rounded-full bg-primary transition-all duration-500"
                            style={{ width: `${fileProgress}%` }}
                          />
                        </div>
                      ) : (
                        uploadComplete && (
                          <p className="text-xs text-[#666666]">
                            {' '}
                            {duration
                              ? `${duration.toFixed(2)} min`
                              : 'Loading...'}
                          </p>
                        )
                      )}
                      <RxCross1
                        className="cursor-pointer text-sm text-[#666666] hover:text-primary"
                        onClick={() => handleFileRemove(index)}
                      />
                    </div>
                  ))}
                </div>
              )}
              <label className="flex w-full cursor-pointer flex-col items-center justify-center gap-2">
                <h3 className="text-center text-xs text-[#777777]">
                  Upload Audio File
                </h3>
                <NoCollection />
                <p className="text-xs text-[#333333]">
                  For best quality, use WAV, FLAC, AIFF, or ALAC. The maximum
                  file size is 4GB uncompressed.
                </p>

                <div onClick={() => inputRef.current?.click()}>
                  <Button className="group text-xs font-semibold">
                    CHOOSE FILE
                    <MdArrowOutward className="text-sm font-semibold text-white" />
                  </Button>
                </div>
                <p className="mt-2 text-xs text-[#333333]">
                  You can add multiple files
                </p>
                <input
                  type="file"
                  accept="audio/*"
                  ref={inputRef}
                  onChange={handleFileChange}
                  multiple
                  className="hidden"
                />
              </label>
            </div>

            {/* Record Section */}
            <div className="flex w-full flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-5 transition hover:border-primary sm:p-[30px]">
              <AiTwotoneAudio className="text-3xl text-[#333333]" />
              <p className="text-xl leading-none text-[#333333] sm:text-[22px]">
                Or record with a microphone
              </p>
              <p className="text-sm leading-none text-[#666666] sm:text-base">
                Upload recorded voice memos, updates, news, or intros to new
                releases.
              </p>

              <canvas
                ref={canvasRef}
                className="h-[80px] w-full rounded bg-black"
                style={{ display: isRecording ? 'block' : 'none' }}
                width={600}
                height={80}
              />

              <div className="mb-1 h-1 w-full rounded-full bg-[#CECECE]">
                <div
                  className="h-1 rounded-full bg-primary"
                  style={{
                    width: `${progress}%`,
                    transition: 'width 1s linear',
                  }}
                />
              </div>
              <p className="w-full text-end text-xs text-[#333333]">
                {formatTime(seconds)}
              </p>

              {isRecording ? (
                <Button
                  variant="outline"
                  className="group h-[40px] text-xs font-semibold"
                  onClick={handleStop}
                >
                  <span className="h-[10px] w-[10px] bg-primary group-hover:bg-white"></span>
                  STOP RECORDING
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="group h-[40px] text-xs font-semibold"
                  onClick={handleStart}
                >
                  <span className="h-[10px] w-[10px] rounded-full bg-primary group-hover:bg-white"></span>
                  START RECORDING
                </Button>
              )}
            </div>
          </div>

          <div className="m-auto mt-5 flex max-w-[690px] justify-between gap-2">
            <Button
              onClick={onBackToAlbumDetail}
              className="!w-[130px] !border-none !bg-[#999999] !font-bold sm:!h-[62px] sm:!w-[205px]"
            >
              <span className="mr-2 rotate-180">
                <Arrow />
              </span>
              BACK
            </Button>

            <Button
              className="!w-[150px] !font-bold sm:!h-[62px] sm:!w-[315px]"
              arrow
              onClick={() => setIsSongsDetails(true)}
            >
              NEXT STEP
            </Button>

            {/* {(audioBlob || fileName) && !isRecording ? (
          <Button
            className="!w-[150px] !font-bold sm:!h-[62px] sm:!w-[315px]"
            arrow
            onClick={handleSubmit}
            disabled={trackMutation.isPending}
            isLoading={trackMutation.isPending}
          >
            NEXT STEP
          </Button>
        ) : (
          <Button
            className="!sm:!h-[62px] !w-[150px] !font-bold sm:!w-[315px]"
            arrow
            onClick={handleSubmit}
            disabled={trackMutation.isPending}
            isLoading={trackMutation.isPending}
          >
            ADD MUSIC
          </Button>
        )} */}
          </div>
        </>
      )}

      <p className="mt-20 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </div>
  );
};

export default AudioFile;
