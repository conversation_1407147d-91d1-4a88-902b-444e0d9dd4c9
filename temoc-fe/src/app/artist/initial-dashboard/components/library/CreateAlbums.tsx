'use client';
import Button from '@/components/common/Button';
import { Input } from '@/components/common/Forms/Input';
import { Textarea } from '@/components/common/Forms/TextArea';
import Arrow from '@/components/common/Icons/Arrow';
import Upload from '@/components/common/Icons/Upload';
// import SwitchButton from '@/components/ui/SwitchButton';
import { libraryService } from '@/services/library.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';

interface AlbumDetailsProps {
  onBackToLibraryDetail: () => void;
  onAddMusic: () => void;
  updateAlbum: any;
  library: any;
  onAlbumCreated?: (album: any) => void;
}

const CreateAlbums = ({
  onBackToLibraryDetail,
  library,
  updateAlbum,
  onAlbumCreated,
}: AlbumDetailsProps) => {
  const [fileName, setFileName] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [isFree, setIsFree] = useState(false);
  const [requiresToken, setRequiresToken] = useState(false);
  // const [freeToListen, setFreeToListen] = useState(true);
  const [tokenAmount, setTokenAmount] = useState('');
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const queryClient = useQueryClient();
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const { register, handleSubmit, setValue } = useForm();

  const UpdateAlbumMutation = useMutation({
    mutationFn: (data: any) =>
      libraryService.updateAlbum(updateAlbum?._id, data),
    onSuccess: () => {
      toast.success('Album created successfully');
      queryClient.refetchQueries({ queryKey: ['albums'] });
      onBackToLibraryDetail();
    },
    onError: (error: any) => {
      console.log('Error:', error);

      toast.error(error?.response?.data?.message);
    },
  });

  const albumMutation = useMutation({
    mutationFn: (data: any) => libraryService.createAlbums(data),
    onSuccess: (response) => {
      toast.success('Album created successfully');
      queryClient.refetchQueries({ queryKey: ['albums'] });

      // Always go back to library detail first
      onBackToLibraryDetail();

      // Then call the callback with the created album data
      if (onAlbumCreated) {
        onAlbumCreated(response?.data?.album);
      }
    },
    onError: (error: any) => {
      console.log('Error:', error);

      toast.error(error?.response?.data?.message);
    },
  });

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setThumbnailFile(file);
      setFileName(file.name);
      setValue('thumbnail', file);

      const reader = new FileReader();
      reader.onloadend = () => {
        setThumbnailPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: any) => {
    const formData = new FormData();
    formData.append('libraryId', library?._id);
    formData.append('title', data.title);
    formData.append('description', data.description || '');
    formData.append('freeToListen', JSON.stringify(isFree));

    if (requiresToken) {
      formData.append('requiredTokenAmount', tokenAmount || '0');
    }

    if (thumbnailFile) {
      formData.append('thumbnail', thumbnailFile);
    }

    if (updateAlbum?._id) {
      UpdateAlbumMutation.mutate(formData);
    } else {
      if (!thumbnailFile) {
        toast.error('Please upload a thumbnail image');
        return;
      }
      albumMutation.mutate(formData);
    }
  };

  // const handleFreeToggle = (value: boolean) => {
  //   setFreeToListen(value);
  //   setRequiresToken(!value);

  //   if (value) {
  //     setTokenAmount('');
  //   }
  // };

  // const handleTokenToggle = (value: boolean) => {
  //   setRequiresToken(value);
  //   setFreeToListen(!value);

  //   if (!value) {
  //     setTokenAmount('');
  //   }
  // };

  useEffect(() => {
    if (updateAlbum) {
      setValue('title', updateAlbum?.title);
      setValue('description', updateAlbum?.description || '');
      setIsFree(updateAlbum?.freeToListen);
      setRequiresToken(!!updateAlbum?.requiredTokenAmount);
      // setFreeToListen(!updateAlbum?.requiredTokenAmount);
      setTokenAmount(updateAlbum?.requiredTokenAmount || '');

      // Show thumbnail preview if exists
      if (updateAlbum?.thumbnailUrl) {
        setThumbnailPreview(updateAlbum?.thumbnailUrl); // assumes the URL is stored here
      }
    }
  }, [updateAlbum, setValue]);
  return (
    <div>
      <h3 className="text-center text-2xl font-semibold uppercase text-[#333333] sm:text-[30px]">
        Album DETAILS
      </h3>
      <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
        <div>
          <p className="mb-1">Title</p>
          <Input placeholder="Title" name="title" register={register} />
        </div>
        <div>
          <p className="mb-1">Description</p>
          <Textarea
            register={register}
            name={'description'}
            placeholder="Description"
            className="h-[150px]"
          />
        </div>
        <label className="flex w-[220px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
          <h3 className="text truncate-center max-w-[170px] text-center text-xs text-[#777777]">
            Upload Thumbnail
          </h3>
          {thumbnailPreview ? (
            <img
              src={thumbnailPreview}
              alt="Thumbnail Preview"
              className="h-[100px] w-[100px] rounded object-cover"
            />
          ) : (
            <>
              <Upload />
              <div>
                <p className="text-center text-xs text-[#666666]">
                  {fileName || 'Upload Thumbnail Image'}
                </p>
                <p className="-mt-1 text-center text-[8px] text-[#666666]">
                  900px by 900px Recommended
                </p>
              </div>
            </>
          )}

          <input
            type="file"
            accept="image/*"
            ref={inputRef}
            onChange={handleThumbnailChange}
            className="hidden"
          />
        </label>

        {/* <div className="flex items-center justify-between gap-2">
          <p className="text-sm text-[#181818]">Free to Listen / Watch</p>
          <SwitchButton
            enable={freeToListen}
            onChange={handleFreeToggle}
            disabled={false}
            loading={false}
          />
        </div>
        <div className="flex items-center justify-between gap-2">
          <p className="text-sm text-[#181818]">Requires Additional Tokens</p>
          <SwitchButton
            enable={requiresToken}
            onChange={handleTokenToggle}
            disabled={false}
            loading={false}
          />
        </div> */}
        {requiresToken && (
          <Input
            placeholder="Enter required token amount"
            name="tokenAmount"
            register={register}
            type="number"
            onChange={(e: any) => setTokenAmount(e.target.value)}
          />
        )}
      </div>

      <div className="m-auto mt-5 flex max-w-[690px] justify-between gap-2">
        <Button
          className="!w-[130px] !border-none !bg-[#999999] !font-bold sm:!h-[62px] sm:!w-[205px]"
          onClick={onBackToLibraryDetail}
        >
          <span className="mr-2 rotate-180">
            <Arrow />
          </span>
          BACK
        </Button>
        <Button
          className="!w-[150px] !font-bold sm:!h-[62px] sm:!w-[315px]"
          arrow
          onClick={handleSubmit(onSubmit)}
          disabled={albumMutation.isPending || UpdateAlbumMutation.isPending}
          isLoading={albumMutation.isPending || UpdateAlbumMutation.isPending}
        >
          {updateAlbum ? 'UPDATE ALBUM' : 'CREATE ALBUM'}
        </Button>
      </div>

      <p className="mt-20 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </div>
  );
};

export default CreateAlbums;
