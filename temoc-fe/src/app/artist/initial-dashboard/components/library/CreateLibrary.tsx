'use client';
import Button from '@/components/common/Button';
import { Input } from '@/components/common/Forms/Input';
import SelectComponent from '@/components/common/Forms/Select';
import { Textarea } from '@/components/common/Forms/TextArea';
import Arrow from '@/components/common/Icons/Arrow';
// import NoCollection from '@/components/common/Icons/NoCollection';
import Upload from '@/components/common/Icons/Upload';
import ImageComponent from '@/components/common/ImageComponent';
import { libraryService } from '@/services/library.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';

const librarySchema = yup.object().shape({
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  collectionType: yup
    .object({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required('Collection type is required'),
  genre: yup
    .object({
      value: yup.string().required(),
      label: yup.string().required(),
    })
    .required('Genre is required'),
  subscriptionEnabled: yup.boolean().required(),
  thumbnail: yup.mixed().nullable(), // Optional, validated on file select
});

const CreateLibraryComp = ({
  setCreating,
  setLibraryCreated,
  onLibraryCreated,
}: any) => {
  const inputRef = useRef<HTMLInputElement | null>(null);

  const collectionOptions = [
    { value: 'Music', label: 'Music' },
    // { value: 'Videos', label: 'Videos' },
    // { value: 'Podcast', label: 'Podcast' },
  ];

  const genreOptions = [
    { value: 'Classical', label: 'Classical' },
    { value: 'Pop', label: 'Pop' },
    { value: 'Rock', label: 'Rock' },
    { value: 'Hip-Hop', label: 'Hip-Hop' },
    { value: 'R&B', label: 'R&B' },
    { value: 'Electronic', label: 'Electronic' },
    { value: 'Jazz', label: 'Jazz' },
    { value: 'Metal', label: 'Metal' },
    { value: 'Blues', label: 'Blues' },
    { value: 'Country', label: 'Country' },
    { value: 'Latin', label: 'Latin' },
    { value: 'Middle Eastern', label: 'Middle Eastern' },
    { value: 'Folk', label: 'Folk' },
    { value: 'House', label: 'House' },
    { value: 'Alternative', label: 'Alternative' },
    { value: 'Disco', label: 'Disco' },
    { value: 'Punk', label: 'Punk' },
    { value: 'Gospel', label: 'Gospel' },
    { value: 'Opera', label: 'Opera' },
  ];

  // const fileTypeOptions = [
  //   { value: 'Audio', label: 'Audio' },
  //   { value: 'Video', label: 'Video' },
  // ];

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(librarySchema),
    defaultValues: {
      collectionType: { value: 'Music', label: 'Music' }, // <-- Add this
      subscriptionEnabled: false,
    },
  });

  const [thumbnailName, setThumbnailName] = useState<string | null>(null);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const queryClient = useQueryClient();

  const libraryMutation = useMutation({
    mutationFn: (data: any) => libraryService.createLibraries(data),
    onSuccess: (response) => {
      toast.success('Library created successfully');
      queryClient.refetchQueries({ queryKey: ['All_library'] });
      setLibraryCreated(true);
      setCreating(false);

      // Call the callback with the created library data
      if (onLibraryCreated) {
        onLibraryCreated(response?.data?.library);
      }
    },
    onError: (error: any) => {
      console.log('Error:', error);

      toast.error(error?.response?.data?.message);
    },
  });

  const onSubmit = (data: any) => {
    const formData = new FormData();
    if (!thumbnailFile) {
      toast.error('Please upload a thumbnail image');
      return;
    }
    formData.append('title', data?.title);
    formData.append('description', data?.description);
    formData.append('type', data?.collectionType?.value);
    formData.append('genre', data?.genre?.value);
    formData.append('requiresSubscription', data?.subscriptionEnabled);
    if (thumbnailFile) {
      formData.append('thumbnail', thumbnailFile);
    }

    libraryMutation.mutate(formData);
  };

  const handleThumbnailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setThumbnailName(file.name);
      setThumbnailFile(file);
      setValue('thumbnail', file);
    }
  };

  const thumbnailPreview = thumbnailFile
    ? URL.createObjectURL(thumbnailFile)
    : '/assets/images/admin/avatar.png';
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="text-center text-2xl font-semibold text-[#333333] sm:text-[30px]">
        CREATE LIBRARY
      </h3>
      <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
        <div className="">
          <p className="mb-1">Title</p>
          <Input placeholder="Title" name="title" register={register} />
          {errors.title && (
            <p className="text-sm text-red-500">{errors.title.message}</p>
          )}
        </div>
        <div>
          <p className="mb-1">Description</p>
          <Textarea
            register={register}
            name={'description'}
            placeholder="Description"
            className="h-[150px]"
          />
          {errors.description && (
            <p className="text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>
        <div>
          <p className="mb-1">Type of Collection</p>
          <Controller
            control={control}
            name="collectionType"
            render={({ field }) => {
              return (
                <>
                  <SelectComponent
                    {...field}
                    options={collectionOptions}
                    selected={field.value}
                    onSelect={field.onChange}
                    placeholder="Type of Collection"
                    className="w-full"
                  />
                  {errors.collectionType && (
                    <p className="text-sm text-red-500">
                      {errors.collectionType.message}
                    </p>
                  )}
                </>
              );
            }}
          />
        </div>

        <div>
          <p className="mb-1">Type of Genres</p>
          <Controller
            control={control}
            name="genre"
            render={({ field }) => {
              return (
                <>
                  <SelectComponent
                    {...field}
                    options={genreOptions}
                    selected={field.value}
                    onSelect={field.onChange}
                    placeholder="Type of Genres"
                    className="w-full"
                  />
                  {errors.genre && (
                    <p className="text-sm text-red-500">
                      {errors.genre.message}
                    </p>
                  )}
                </>
              );
            }}
          />
        </div>

        {/* Uncomment if you want to add file type selection */}
        {/* <Controller
          control={control}
          name="fileType"
          render={({ field }) => (
            <SelectComponent
              {...field}
              options={fileTypeOptions}
              selected={field.value}
              onSelect={field.onChange}
              placeholder="File Type"
              className="w-full"
            />
          )}
        /> */}
        {/* <label className="flex w-full cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
          <h3 className="text-center text-xs text-[#777777]">
            Upload Audio File
          </h3>
          <NoCollection />
          <p className="text-sms text-center text-[#333333]">
            {fileName || 'No File Selected Yet'}
          </p>
          <input
            type="file"
            accept="audio/*"
            ref={inputRef}
            onChange={handleFileChange}
            className="hidden"
          />
        </label> */}
        <label className="flex w-[220px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
          <h3 className="max-w-[170px] truncate text-center text-xs text-[#777777]">
            {thumbnailName || 'Upload Thumbnail Image'}
          </h3>
          <Upload />
          <div>
            <p className="text-center text-xs text-[#666666]">
              {'Upload Thumbnail Image'}
            </p>
            <p className="-mt-1 text-center text-[8px] text-[#666666]">
              900px by 900px Recommended
            </p>
          </div>
          <input
            type="file"
            accept="image/*"
            ref={inputRef}
            onChange={handleThumbnailChange}
            className="hidden"
          />
        </label>
        {errors.thumbnail && (
          <p className="text-sm text-red-500">{errors.thumbnail.message}</p>
        )}
        {/* <div className="flex items-center justify-between gap-2">
          <Controller
            name="subscriptionEnabled"
            control={control}
            defaultValue={false}
            render={({ field }) => (
              <div className="flex items-center justify-between gap-2">
                <p className="text-sm text-[#181818]">
                  Available on subscription or requires additional payment to
                  access
                </p>
                <SwitchButton
                  enable={field.value}
                  onChange={field.onChange}
                  disabled={false}
                  loading={false}
                />
              </div>
            )}
          />
          {errors.subscriptionEnabled && (
            <p className="text-sm text-red-500">
              {errors.subscriptionEnabled.message}
            </p>
          )}
        </div> */}
      </div>
      {thumbnailFile && (
        <div className="m-auto mt-5 max-w-[690px] bg-white p-5">
          <p className="text-sm text-[#333333]">Preview</p>
          <div className="mt-5 flex flex-wrap gap-2 2xl:flex-nowrap">
            <ImageComponent
              src={thumbnailPreview}
              figClassName="h-[180px] w-[180px] flex-shrink-0 rounded-lg"
              fill
              alt=""
              priority
              className="cursor-pointer rounded-lg object-cover"
            />
            <div>
              <p className="text-base font-medium text-[#333333]">
                {' '}
                {watch('title') || 'Bruno Mars Music'}
              </p>
              <p className="mt-1 text-sm text-[#666666]">
                {watch('description') ||
                  'Discover exclusive content, music, and experiences curated by your favorite artist. Own a piece of their journey and unlock special rewards.'}
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="m-auto mt-5 flex max-w-[690px] justify-between gap-2">
        <Button
          className="!w-[130px] !border-none !bg-[#999999] !font-bold sm:!h-[62px] sm:!w-[205px]"
          onClick={() => setCreating(false)}
        >
          <span className="mr-2 rotate-180">
            <Arrow />
          </span>{' '}
          BACK
        </Button>
        <Button
          type="submit"
          className="!w-[170px] sm:!h-[62px] sm:!w-[315px]"
          arrow
          disabled={libraryMutation.isPending}
          isLoading={libraryMutation.isPending}
        >
          ADD COLLECTION
        </Button>
      </div>

      <p className="mt-20 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </form>
  );
};

export default CreateLibraryComp;
