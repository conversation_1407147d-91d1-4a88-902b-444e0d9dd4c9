'use client';
import React from 'react';
import { Button } from '@/components/common';
import Modal from '@/components/common/Modal';

interface CreateTrackPopupProps {
  show: boolean;
  onClose: () => void;
  onAddTrack: () => void;
  albumName: string;
}

const CreateTrackPopup = ({
  show,
  onClose,
  onAddTrack,
  albumName,
}: CreateTrackPopupProps) => {
  return (
    <Modal className="!max-w-[500px] !p-8" show={show} hide={onClose}>
      <div className="text-center">
        <h3 className="mb-4 text-2xl font-semibold text-[#333333]">
          🎵 Album Created Successfully!
        </h3>

        <p className="mb-2 text-lg text-[#666666]">
          Your album{' '}
          <span className="font-semibold text-primary">
            &quot;{albumName}&quot;
          </span>{' '}
          has been created.
        </p>

        <p className="mb-8 text-base text-[#666666]">
          Would you like to add tracks to this album now?
        </p>

        <div className="flex justify-center gap-4">
          <Button
            className="!h-12 !w-[140px] !border-none !bg-[#999999]"
            onClick={onClose}
          >
            LATER
          </Button>

          <Button className="h-12 !w-[180px]" arrow onClick={onAddTrack}>
            ADD TRACKS
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CreateTrackPopup;
