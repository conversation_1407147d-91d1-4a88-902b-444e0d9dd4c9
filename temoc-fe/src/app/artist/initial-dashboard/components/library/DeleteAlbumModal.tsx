import Modal from '@/components/common/Modal';
import { Button } from '@/components/common';
import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';

type DeleteAlbumModalProps = {
  show: boolean;
  onClose: any;
  albumId: any;
};

const DeleteAlbumModal = ({
  show,
  onClose,
  albumId,
}: DeleteAlbumModalProps) => {
  const queryClient = useQueryClient();
  const deleteAlbum = useMutation({
    mutationFn: (albumId: string) => libraryService.deleteAlbum(albumId),
    onSuccess: () => {
      toast.success('Album deleted successfully');
      queryClient.refetchQueries({ queryKey: ['albums'] });
      onClose(false); // Close the modal after successful deletion
    },
    onError: () => {
      toast.error('Failed to delete album');
    },
  });

  const handleDelete = () => {
    deleteAlbum.mutate(albumId);
  };
  return (
    <Modal show={show} hide={onClose} className="!h-max !max-w-[600px]">
      <div className="p-6 text-center">
        <h3 className="text-2xl font-semibold uppercase text-red-600">
          Delete Album
        </h3>
        <p className="mx-auto mt-4 w-[80%] text-gray-700">
          Are you sure you want to delete this album? This action cannot be
          undone.
        </p>
        <div className="mt-6 flex justify-center gap-4">
          <Button
            className="!w-[150px] !border-red-600 bg-red-600 text-white hover:bg-red-700"
            onClick={handleDelete}
          >
            Delete
          </Button>
          <Button variant="outline" onClick={onClose} className="!w-[150px]">
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteAlbumModal;
