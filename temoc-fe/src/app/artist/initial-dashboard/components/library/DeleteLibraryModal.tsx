import Modal from '@/components/common/Modal';
import { Button } from '@/components/common';
import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';

type DeleteAlbumModalProps = {
  show: boolean;
  onClose: any;
  libraryId: any;
};

const DeleteLibraryModal = ({
  show,
  onClose,
  libraryId,
}: DeleteAlbumModalProps) => {
  const queryClient = useQueryClient();
  const deleteAlbum = useMutation({
    mutationFn: (libraryId: string) => libraryService.deleteLibrary(libraryId),
    onSuccess: () => {
      toast.success('Library deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['All_library'] });
      onClose(false); // Close the modal after successful deletion
    },
    onError: () => {
      toast.error('Failed to delete library');
    },
  });

  const handleDelete = () => {
    deleteAlbum.mutate(libraryId);
  };
  return (
    <Modal show={show} hide={onClose} className="!h-max !max-w-[600px]">
      <div className="p-6 text-center">
        <h3 className="text-2xl font-semibold uppercase text-red-600">
          Delete Library
        </h3>
        <p className="mx-auto mt-4 max-w-[90%] text-gray-700">
          Are you sure you want to delete this Library? This action cannot be
          undone.
        </p>
        <div className="mt-6 flex justify-center gap-4">
          <Button
            className="!w-[150px] !border-red-600 bg-red-600 text-white hover:bg-red-700"
            onClick={handleDelete}
          >
            Delete
          </Button>
          <Button className="!w-[150px]" variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteLibraryModal;
