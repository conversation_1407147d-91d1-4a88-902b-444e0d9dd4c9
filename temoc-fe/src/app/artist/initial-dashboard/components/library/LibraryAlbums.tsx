'use client';
import { Button } from '@/components/common';
import NoCollection from '@/components/common/Icons/NoCollection';
import ImageComponent from '@/components/common/ImageComponent';
import AlbumCard from '@/components/ui/Card/AlbumCard';
import NoData from '@/components/ui/NoData';
import { libraryService } from '@/services/library.service';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { BiSolidEdit } from 'react-icons/bi';
import { MdDelete } from 'react-icons/md';
import Skeleton from 'react-loading-skeleton';
import DeleteAlbumModal from './DeleteAlbumModal';

// interface Album {
//   _id: string;
//   title: string;
//   thumbnail?: string;
//   description?: string;
// }

interface LibraryAlbumsProps {
  library: any;
  onBack: () => void;
  onAddAlbum: () => void;
  setSelectedAlbum: (album: any) => void;
  onAddSongs: () => void;
  setShowAudioFiles: () => void;
  setShowLibraryDetail: () => void;
}

const LibraryAlbums: React.FC<LibraryAlbumsProps> = ({
  library,
  onAddAlbum,
  setSelectedAlbum,
  setShowLibraryDetail,
}) => {
  const { data: allAlbums, isLoading } = useQuery({
    queryKey: ['albums', library?._id],
    queryFn: () => libraryService.getAlbumsByLibraryId(library?._id),
    enabled: !!library?._id,
  });

  const [showDelete, setShowDelete] = useState(false);
  const [albumId, setAlbumId] = useState();

  const albums = allAlbums?.data?.albums;
  return (
    <div className="sm:p-4">
      <div className="mt-5 flex flex-col justify-between gap-3 md:flex-row md:gap-5 xs:mt-3">
        <div className="flex gap-2">
          <ImageComponent
            src={library?.thumbnailUrl || '/assets/images/admin/avatar.png'}
            figClassName="h-10 w-10 flex-shrink-0 rounded-lg"
            fill
            alt=""
            priority
            className="rounded-full object-cover"
          />
          <div className="">
            <p className="text-[22px] font-medium text-[#333333]">
              {library?.title}
            </p>
            <p className="mt-1 text-sm text-[#666666] lg:max-w-[500px]">
              {library?.description ||
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-5 md:mt-2 md:gap-10 xs1:gap-2.5">
          <div>
            <p className="text-xs text-[#666666]"> Type:</p>
            <p className="text-sm text-primary">{library?.type}</p>
          </div>
          <div>
            <p className="text-xs text-[#666666]"> Genres:</p>
            <p className="text-sm text-primary">{library?.genre}</p>
          </div>
          <div>
            <p className="text-xs text-[#666666]">Albums:</p>
            <p className="text-sm text-primary">{albums?.length}</p>
          </div>

          <Button
            className="!h-10 !text-xs xs:ml-auto"
            arrow
            onClick={onAddAlbum}
          >
            Add Album
          </Button>
        </div>
      </div>
      {isLoading ? (
        // Loading state
        <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-5 xl:grid-cols-6">
          {Array(6)
            .fill(null)
            .map((_, idx) => (
              <Skeleton key={idx} height={200} borderRadius={8} />
            ))}
        </div>
      ) : albums.length > 0 ? (
        <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-5 xl:grid-cols-6">
          {albums.map((album: any) => (
            <div className="group relative" key={album._id}>
              <div
                className="relative cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  setShowLibraryDetail();
                  setSelectedAlbum(album);
                }}
              >
                <AlbumCard
                  imageSrc={album?.thumbnailUrl}
                  name={album.title}
                  profileSrc={library?.thumbnailUrl}
                  artistName={library?.title}
                />
              </div>
              <span
                className="absolute left-5 top-5 z-10 hidden h-6 w-6 items-center justify-center rounded-full bg-primary group-hover:flex"
                onClick={() => {
                  setShowDelete(true);
                  setAlbumId(album?._id);
                }}
              >
                <MdDelete
                  size={12}
                  className="block cursor-pointer text-black hover:text-white"
                />
              </span>
              <span
                className="absolute right-5 top-5 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-primary"
                onClick={() => {
                  setSelectedAlbum(album);
                  onAddAlbum();
                }}
              >
                <BiSolidEdit
                  size={12}
                  className="block cursor-pointer text-black hover:text-white"
                />
              </span>
            </div>
          ))}
        </div>
      ) : (
        <NoData
          icon={<NoCollection />}
          heading="No albums created"
          description="You haven’t created any albums yet. Albums help you group related tracks, making it easier for listeners to discover and enjoy your music."
        />
      )}
      <DeleteAlbumModal
        onClose={() => {
          setShowDelete(false);
        }}
        show={showDelete}
        albumId={albumId}
      />
    </div>
  );
};

export default LibraryAlbums;
