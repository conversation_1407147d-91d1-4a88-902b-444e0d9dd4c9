import { DeleteIcon } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';
import React, { useState } from 'react';
import DeleteLibraryModal from './DeleteLibraryModal';
import { useQuery } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';

interface LibraryCardProps {
  library: any;

  onClick: () => void;
}
const LibraryCard = ({ library, onClick }: LibraryCardProps) => {
  const [openModal, setOpenModal] = useState(false);
  const [libraryId, setLibraryId] = useState('');
  const { data: allAlbums } = useQuery({
    queryKey: ['albums', library?._id],
    queryFn: () => libraryService.getAlbumsByLibraryId(library?._id),
    enabled: !!library?._id,
  });

  const albums = allAlbums?.data?.albums;
  return (
    <div className="group relative rounded-[20px]">
      <span
        className="absolute right-5 top-5 z-20 hidden cursor-pointer group-hover:block"
        onClick={() => {
          setOpenModal(true);
          setLibraryId(library?._id);
        }}
      >
        <DeleteIcon />
      </span>
      <div
        className="m-auto w-full cursor-pointer rounded-[20px] bg-white p-5"
        onClick={onClick}
      >
        <div className="flex flex-col gap-5 xl:flex-row">
          <ImageComponent
            src={library?.thumbnailUrl || '/assets/images/admin/avatar.png'}
            figClassName="h-[120px] w-[120px] flex-shrink-0 rounded-lg"
            fill
            alt=""
            priority
            className="cursor-pointer rounded-lg object-cover"
          />
          <div>
            <p className="text-base font-medium text-[#333333]">
              {library?.title}
            </p>
            <p className="mt-1 text-xs text-[#666666]">
              {library?.description ||
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.'}
            </p>
            <div className="mt-2 flex items-center gap-10">
              <div>
                <p className="text-base text-[#666666] xs:text-sm"> Type:</p>
                <p className="text-base text-primary xs:text-sm">
                  {' '}
                  {library?.type}
                </p>
              </div>
              <div>
                <p className="text-base text-[#666666] xs:text-sm"> Genres:</p>
                <p className="text-base text-primary xs:text-sm">
                  {library?.genre}
                </p>
              </div>
              <div>
                <p className="text-base text-[#666666] xs:text-sm">Albums:</p>
                <p className="text-base text-primary xs:text-sm">
                  {albums?.length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <DeleteLibraryModal
        show={openModal}
        onClose={() => setOpenModal(false)}
        libraryId={libraryId}
      />
    </div>
  );
};

export default LibraryCard;
