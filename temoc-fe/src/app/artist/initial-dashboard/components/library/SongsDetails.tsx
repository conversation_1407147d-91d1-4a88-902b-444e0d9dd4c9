'use client';
import Button from '@/components/common/Button';
import { DeleteIcon, Play } from '@/components/common/Icons';
import Arrow from '@/components/common/Icons/Arrow';
import Modal from '@/components/common/Modal';
import React, { useState } from 'react';
import { BiSolidEdit } from 'react-icons/bi';
import DetailSongsModal from './DetailSongsModal';
import { Index } from 'viem';
import { useMutation } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { toast } from 'react-toastify';
type Props = {
  onBack: () => void;
  onSubmit: () => void;
  album?: any;
  albumId?: string;
  setRecordFiles?: React.Dispatch<React.SetStateAction<any>>;
  setFiles?: React.Dispatch<React.SetStateAction<any>>;
  onBackToAlbumDetail?: () => void;
};

const SongsDetails = ({
  onBack,
  onBackToAlbumDetail,
  album,
  albumId,
  setFiles,
  setRecordFiles,
}: Props) => {
  const [openModal, setOpenModal] = useState(false);
  const [selectedTrack, setSelectedTrack] = useState<any>(null);
  const [updateSelectedTrack, setupdateSelectedTrack] = useState<any>(null);

  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(
    null,
  );
  const [currentlyPlayingId, setCurrentlyPlayingId] = useState<string | null>(
    null,
  );
  const [updatedRecords, setUpdatedRecords] = useState<
    { id?: string; name: string; track: any }[]
  >([]);

  const isButtonDisabled =
    album.length !== updatedRecords.length ||
    album.length === 0 ||
    updatedRecords.length === 0;
  const handlePlayStop = (track: any) => {
    const trackId = track?.file?.name || track.name; // fallback to name if _id not present

    if (currentlyPlayingId === trackId) {
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
      setCurrentAudio(null);
      setCurrentlyPlayingId(null);
      return;
    }

    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
    }

    // ✅ Determine audio source based on whether track is a File or object with file
    const file = track instanceof File ? track : track.file;
    const audioSrc = file instanceof File ? URL.createObjectURL(file) : file;

    const audio = new Audio(audioSrc);
    audio.play().catch((err) => console.error('Play error:', err));
    setCurrentAudio(audio);
    setCurrentlyPlayingId(trackId);
  };

  const handleFileRemove = (targetId: any) => {
    if (setFiles) {
      setFiles((prevFiles: any[]) =>
        prevFiles.filter((file) => file.id !== targetId),
      );
    }
  };
  const removeRecordByTrackId = (idToRemove: string) => {
    setUpdatedRecords((prev) =>
      prev.filter((record) => record.track?._id !== idToRemove),
    );
  };

  const deleteTrack = useMutation({
    mutationFn: ({ trackId }: { trackId: string; id: string }) =>
      libraryService.deleteTrack(trackId),

    onSuccess: (_data, variables) => {
      toast.success('Track deleted successfully');

      const { id, trackId } = variables;

      removeRecordByTrackId(trackId); // example _id

      if (id) {
        handleFileRemove(id);
      } else {
        setRecordFiles?.([]);
      }
    },

    onError: () => {
      // toast.error('Failed to delete album');
    },
  });

  const handleDelete = (trackId: string, id: string) => {
    deleteTrack.mutate({ trackId, id });
  };
  return (
    <div>
      <h3 className="text-center text-2xl font-semibold text-[#333333] sm:text-[30px]">
        Songs Details
      </h3>
      <div className="mt-5 space-y-5 sm:mt-10">
        {album?.length === 0 ? (
          <>
            <p className="text-center text-lg font-semibold text-[#333333]">
              No songs available in this album.
            </p>
            <p className="text-center text-base text-[#666666]">
              Please add songs to the album.
            </p>
          </>
        ) : (
          album.map((item: any, index: Index) => (
            <div
              key={index}
              className="flex items-center justify-between rounded-md bg-white p-5 shadow-[0_7px_7px_rgba(0,0,0,0.1)]"
            >
              <div className="flex cursor-pointer items-center gap-1">
                {/* <More /> */}
                <div onClick={() => handlePlayStop(item)}>
                  {currentlyPlayingId === (item.name || item?.file?.name) ? (
                    <div className="flex h-[30px] w-[30px] flex-shrink-0 items-center justify-center rounded-full bg-primary text-sm text-white">
                      ■
                    </div> // Stop icon or symbol
                  ) : (
                    <Play />
                  )}
                </div>
                <input
                  placeholder="Song Name"
                  className="ml-2 hidden rounded-[10px] border-[#D2D2D2] font-display focus:!border-[#D2D2D2] focus-visible:ring-0 md:block xl:w-[600px]"
                  value={item?.file?.name || item.name}
                  readOnly
                  type="text"
                />
              </div>
              <div className="flex items-center gap-2">
                <p className="whitespace-nowrap text-base text-[#666666]">
                  {item?.title}
                </p>
                <p className="text-base text-[#333333]">{item?.duration}</p>
                {/* <span
                  onClick={() => {
                    setOpenModal(true);
                    setSelectedTrack(item);
                  }}
                >
                  <BiSolidEdit
                    size={22}
                    className="block cursor-pointer text-[#666666] hover:text-primary"
                  />
                </span>
                <span
                  onClick={() => {
                    if (item?.id) {
                      handleFileRemove(item?.id);
                    } else {
                      setRecordFiles?.([]);
                    }
                  }}
                  className="block cursor-pointer pb-0.5"
                >
                  <DeleteIcon />
                </span> */}
                {(() => {
                  const matchedRecord = updatedRecords.find(
                    (r) =>
                      (r.id && r.id === item.id) ||
                      (!item.id && r.name === (item?.file?.name || item.name)),
                  );

                  return matchedRecord ? (
                    <>
                      <span className="text-xl text-green-600">✅</span>

                      <span
                        onClick={() => {
                          setOpenModal(true);
                          setSelectedTrack(item); // Unupdated item
                          setupdateSelectedTrack(matchedRecord.track); // ✅ Set correct updated track
                        }}
                      >
                        <BiSolidEdit
                          size={22}
                          className="block cursor-pointer text-[#666666] hover:text-primary"
                        />
                      </span>

                      <span
                        onClick={() => {
                          handleDelete(matchedRecord.track?._id, item?.id);
                        }}
                        className="block cursor-pointer pb-0.5"
                      >
                        <DeleteIcon />
                      </span>
                    </>
                  ) : (
                    <>
                      <span
                        onClick={() => {
                          setOpenModal(true);
                          setSelectedTrack(item); // Unupdated item
                        }}
                      >
                        <BiSolidEdit
                          size={22}
                          className="block cursor-pointer text-[#666666] hover:text-primary"
                        />
                      </span>

                      <span
                        onClick={() => {
                          if (item?.id) {
                            handleFileRemove(item?.id);
                          } else {
                            setRecordFiles?.([]);
                          }
                        }}
                        className="block cursor-pointer pb-0.5"
                      >
                        <DeleteIcon />
                      </span>
                    </>
                  );
                })()}
              </div>
            </div>
          ))
        )}
      </div>
      <div className="mt-5 flex w-full justify-between gap-2 sm:mt-10">
        <Button
          className="!w-[130px] !border-none !bg-[#999999] !font-bold sm:!h-[62px] sm:!w-[205px]"
          onClick={onBack}
        >
          <span className="mr-2 rotate-180">
            <Arrow />
          </span>{' '}
          BACK
        </Button>
        <Button
          disabled={isButtonDisabled}
          className="!w-[150px] !font-bold sm:!h-[62px] sm:!w-[220px]"
          onClick={onBackToAlbumDetail}
        >
          SUBMIT
        </Button>
      </div>

      <Modal
        className="hideScrollbar !h-max !max-w-[760px] !pb-6 sm:!px-5 md:pb-0"
        show={openModal}
        hide={setOpenModal}
      >
        <DetailSongsModal
          album={album}
          handleFileRemove={handleFileRemove}
          setRecordFiles={setRecordFiles}
          onBack={onBackToAlbumDetail}
          albumId={albumId ?? ''}
          selectedTrack={selectedTrack}
          setOpenModal={setOpenModal}
          setUpdatedRecords={setUpdatedRecords}
          updateSelectedTrack={updateSelectedTrack}
        />
      </Modal>
    </div>
  );
};

export default SongsDetails;
