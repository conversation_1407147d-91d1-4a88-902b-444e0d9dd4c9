'use client';
import React, { useState } from 'react';
import NoCollection from '@/components/common/Icons/NoCollection';
import NoData from '@/components/ui/NoData';
import LibraryCard from './LibraryCard';
import CreateLibraryComp from './CreateLibrary';
import { Button } from '@/components/common';
import { MdArrowOutward } from 'react-icons/md';
import AudioFiles from './AudioFiles';
import SongsDetails from './SongsDetails';
import { useRouter, useSearchParams } from 'next/navigation';
import BreadCrumb from '@/components/ui/BreadCrumb';
import { useQuery } from '@tanstack/react-query';
import { libraryService } from '@/services/library.service';
import { Index } from 'viem';
import CreateAlbums from './CreateAlbums';
import LibraryAlbums from './LibraryAlbums';
import LibraryDetail from './LibraryDetail';
import CreateAlbumPopup from './CreateAlbumPopup';
import CreateTrackPopup from './CreateTrackPopup';
import Skeleton from 'react-loading-skeleton';

// import { toast } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';
// import Copy from '@/components/common/Icons/Copy';
// import ReferralCard from '@/components/ui/ReferralCard';

// const refrelText = 'https://example.com/referral?code...';

// const copyToClipboard = async () => {
//   try {
//     await navigator.clipboard.writeText(refrelText);
//     toast.success('Copied to clipboard!');
//   } catch {
//     toast.error('Failed to copy link');
//   }
// };

const Library = () => {
  const [creating, setCreating] = useState(false);
  const [, setLibraryCreated] = useState(false);
  const [selectedLibrary, setSelectedLibrary] = useState<null | any>(null);
  const [showAlbumDetail, setShowAlbumDetail] = useState(false);
  const [showAudioFiles, setShowAudioFiles] = useState(false);
  const [showSongDetails, setShowSongDetails] = useState(false);
  const [showLibraryDetail, setShowLibraryDetail] = useState(false);
  const [selectedAlbum, setSelectedAlbum] = useState<null | any>(null);
  const [, setCard] = useState(false);

  // New state for popups
  const [showCreateAlbumPopup, setShowCreateAlbumPopup] = useState(false);
  const [showCreateTrackPopup, setShowCreateTrackPopup] = useState(false);
  const [createdLibrary, setCreatedLibrary] = useState<any>(null);
  const [createdAlbum, setCreatedAlbum] = useState<any>(null);
  const handleCreateClick = () => {
    setCreating(true);
  };

  // Handler for when library is created - show popup to create album
  const handleLibraryCreated = (library: any) => {
    setCreatedLibrary(library);
    setShowCreateAlbumPopup(true);
  };

  // Handler for when album is created - show popup to add tracks
  const handleAlbumCreated = (album: any) => {
    setCreatedAlbum(album);
    // Make sure we're on the albums list view (selectedLibrary view)
    setShowAlbumDetail(false);
    setShowLibraryDetail(false);
    setShowAudioFiles(false);
    setShowSongDetails(false);
    // Show popup over the albums list
    setShowCreateTrackPopup(true);
  };

  // Handler for creating album from popup
  const handleCreateAlbumFromPopup = () => {
    setShowCreateAlbumPopup(false);
    setSelectedLibrary(createdLibrary);
    setShowAlbumDetail(true);
  };

  // Handler for adding tracks from popup
  const handleAddTracksFromPopup = () => {
    setShowCreateTrackPopup(false);
    setSelectedAlbum(createdAlbum);
    setSelectedLibrary(createdLibrary);
    setShowAudioFiles(true);
  };

  // Handler for closing popups
  const handleCloseCreateAlbumPopup = () => {
    setShowCreateAlbumPopup(false);
    setCreatedLibrary(null);
  };

  const handleCloseCreateTrackPopup = () => {
    setShowCreateTrackPopup(false);
    setCreatedAlbum(null);
    // Keep the library selected to show albums list
    // setCreatedLibrary(null); // Don't clear this yet
  };

  const router = useRouter();
  const searchParams = useSearchParams();

  const handleHomeClick = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('tab', 'Home');
    params.delete('afterCreateToken');
    router.push(`?${params.toString()}`);
  };

  const { data, isError, isLoading, error } = useQuery({
    queryKey: ['All_library'],
    queryFn: libraryService.getAllLibraries,
  });

  const libraries = data?.data?.libraries;

  return (
    <div>
      <BreadCrumb
        items={[
          {
            label: 'Home',
            onClick: handleHomeClick,
          },
          {
            label: 'Library',
            onClick: () => {
              setCreating(false);
              setSelectedLibrary(false);
              setShowAlbumDetail(false);
              setShowAudioFiles(false);
              setShowSongDetails(false);
              setShowLibraryDetail(false);
            },
            isActive:
              !creating &&
              !selectedLibrary &&
              !showAlbumDetail &&
              !showAudioFiles &&
              !showSongDetails,
          },
          ...(selectedLibrary ||
          showAlbumDetail ||
          showAudioFiles ||
          showSongDetails
            ? [
                {
                  label: 'Album',
                  onClick: () => {
                    setShowAlbumDetail(true);
                    setShowAudioFiles(false);
                    setShowSongDetails(false);
                  },
                  isActive:
                    selectedLibrary &&
                    !showAlbumDetail &&
                    !showAudioFiles &&
                    !showSongDetails,
                },
              ]
            : []),
          ...(showAlbumDetail
            ? [{ label: 'Create Album', isActive: true }]
            : showAudioFiles
              ? [{ label: 'Create Album', isActive: true }]
              : showSongDetails
                ? [{ label: 'Upload Music', isActive: true }]
                : creating
                  ? [{ label: 'Create Library', isActive: true }]
                  : []),
        ]}
      />

      {!creating && !selectedLibrary && (
        <div className="mt-3 flex w-full flex-wrap items-center justify-between gap-3">
          <h3 className="text-xl uppercase text-[#333333] sm:text-[22px]">
            Library
          </h3>
          <div className="flex items-center gap-5">
            <Button
              className="group !text-xs font-semibold uppercase"
              onClick={handleCreateClick}
            >
              Create a Library
              <MdArrowOutward className="text-sm text-white" />
            </Button>
            {/* <div className="block w-full max-w-[315px] rounded-[10px] border border-[#CECECE] bg-white px-4 py-2.5">
                <p className="text-[8px] font-normal leading-[150%] text-[#777777]">
                  Referral Link
                </p>
                <div className="flex w-full items-center justify-between">
                  <p className="text-xs font-normal leading-none text-[#181818]">
                    {refrelText}
                  </p>
                  <div
                    className="cursor-pointer pt-1.5"
                    onClick={copyToClipboard}
                  >
                    <Copy />
                  </div>
                </div>
              </div> */}
          </div>
        </div>
      )}
      {creating ? (
        <CreateLibraryComp
          setCreating={setCreating}
          setLibraryCreated={setLibraryCreated}
          onLibraryCreated={handleLibraryCreated}
        />
      ) : showSongDetails ? (
        <SongsDetails
          album={selectedAlbum}
          onBack={() => {
            setShowLibraryDetail(true);
            setShowAudioFiles(true);
          }}
          onSubmit={() => {
            setShowSongDetails(false);
            setLibraryCreated(true);
            setCard(true);
          }}
        />
      ) : showAudioFiles ? (
        <AudioFiles
          onBackToAlbumDetail={() => {
            setShowAudioFiles(false);
            setShowLibraryDetail(true);
            // setShowLibraryDetail(true);
            setShowLibraryDetail(true);
          }}
          album={selectedAlbum}
          onNextStep={() => {
            setShowAudioFiles(false);
            // Navigate to the album's track view (LibraryDetail)
            setShowLibraryDetail(true);
          }}
        />
      ) : showAlbumDetail ? (
        <CreateAlbums
          updateAlbum={selectedAlbum}
          library={selectedLibrary}
          onBackToLibraryDetail={() => setShowAlbumDetail(false)}
          onAddMusic={() => {
            setShowAlbumDetail(false);
            setShowAudioFiles(true);
          }}
          onAlbumCreated={handleAlbumCreated}
        />
      ) : showLibraryDetail ? (
        <LibraryDetail
          album={selectedAlbum}
          library={selectedLibrary}
          onAddAlbum={() => setShowAlbumDetail(true)}
          // card={card}
          onAddSongs={() => {
            setShowAudioFiles(true);
          }}
        />
      ) : selectedLibrary ? (
        <LibraryAlbums
          setSelectedAlbum={setSelectedAlbum}
          library={selectedLibrary}
          setShowAudioFiles={() => {
            setShowSongDetails(true);
          }}
          onAddAlbum={() => {
            setShowAlbumDetail(true);
            setSelectedAlbum(null);
          }}
          onBack={() => {
            setSelectedLibrary(null);
            setShowAlbumDetail(false);
            setShowAudioFiles(false);
            setShowSongDetails(false);
          }}
          onAddSongs={() => {
            setShowAudioFiles(true);
          }}
          setShowLibraryDetail={() => {
            setShowLibraryDetail(true);
          }}
        />
      ) : isLoading ? (
        <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-5 xl:grid-cols-6">
          {Array(6)
            .fill(null)
            .map((_, idx) => (
              <Skeleton key={idx} height={200} borderRadius={8} />
            ))}
        </div>
      ) : isError ? (
        <NoData
          icon={<NoCollection />}
          heading="Failed to fetch libraries"
          description={error?.message || 'An unexpected error occurred.'}
        />
      ) : (libraries?.length ?? 0) === 0 ? (
        <NoData
          icon={<NoCollection />}
          heading="No collections yet"
          description="Collections help organize your content and make it easier to explore."
        />
      ) : (
        <div>
          <div className="mt-5 grid items-center gap-5 sm:grid-cols-2 2xl:grid-cols-3">
            {libraries?.map((item: any, i: Index) => (
              <LibraryCard
                library={item}
                key={i}
                onClick={() => setSelectedLibrary(item)} // set the full library object
              />
            ))}
          </div>
          <p className="mt-20 text-center text-sm text-[#666666] sm:mt-40">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      )}

      {/* Popup for creating album after library creation */}
      <CreateAlbumPopup
        show={showCreateAlbumPopup}
        onClose={handleCloseCreateAlbumPopup}
        onCreateAlbum={handleCreateAlbumFromPopup}
        libraryName={createdLibrary?.title || ''}
      />

      {/* Popup for adding tracks after album creation */}
      <CreateTrackPopup
        show={showCreateTrackPopup}
        onClose={handleCloseCreateTrackPopup}
        onAddTrack={handleAddTracksFromPopup}
        albumName={createdAlbum?.title || ''}
      />
    </div>
  );
};

export default Library;
