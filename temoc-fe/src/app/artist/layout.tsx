'use client';
import { ReactNode, useState } from 'react';
import Header from '../app/components/Header';

export default function MainLayout({ children }: { children: ReactNode }) {
  const [, setSidebarOpen] = useState(true);

  return (
    <div className="">
      <Header setSidebarOpen={setSidebarOpen} artist />
      <main className="">
        <div className="hideScrollbar h-[calc(100vh-104px)] overflow-y-auto px-4 sm:px-6 lg:px-20">
          {children}
        </div>
      </main>
    </div>
  );
}
