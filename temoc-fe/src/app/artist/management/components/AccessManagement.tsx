'use client';
import { Input } from '@/components/common/Forms/Input';
import SelectComponent from '@/components/common/Forms/Select';
import { Textarea } from '@/components/common/Forms/TextArea';
import React, { useState } from 'react';
import SwitchButton from '@/components/ui/SwitchButton';
import { Button } from '@/components/common';
import { HiOutlinePlusCircle } from 'react-icons/hi';

const collectionOptions = [
  { value: 'ALL', label: 'All' },
  { value: 'Album 1', label: 'Album 1' },
  { value: 'Album 2', label: 'Album 2' },
  { value: 'Album 3', label: 'Album 3' },
];
interface Iprops {
  removebg?: boolean;
}
const AccessManagement = ({ removebg }: Iprops) => {
  const [selectedCollection, setSelectedCollection] = useState<{
    value: string;
    label: string;
  } | null>(null);
  const [enabled, setEnabled] = useState(false);
  return (
    <div>
      <div className="mt-12 pb-5 sm:px-12">
        <div className="mx-auto mt-2.5 flex w-full max-w-[690px] flex-col items-center">
          <h3 className="text-2xl font-semibold uppercase sm:text-[30px]">
            Access Management
          </h3>
          <label
            className={`mt-5 flex w-full cursor-pointer flex-col items-center justify-center gap-2 ${removebg ? '' : 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]'} bg-white p-5 hover:border-primary`}
          >
            <span className="flex w-full items-center justify-between">
              <h3 className="text-sm text-[#181818]">Enable Gated Access</h3>
              <SwitchButton
                enable={enabled}
                onChange={setEnabled}
                disabled={false}
                loading={false}
              />
            </span>

            <Input
              name="Minimum Tokens Required"
              placeholder="Minimum Tokens Required"
              className=""
            />
          </label>
          <label
            className={`mt-5 flex w-full cursor-pointer flex-col justify-center gap-2 bg-white p-5 ${removebg ? '' : 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]'} hover:border-primary`}
          >
            <h3 className="text-sm text-[#181818]">Access Tiers</h3>
            <div className="space-y-3.5">
              <Input name="Tier Name" placeholder="Tier Name" className="" />
              <Input
                name="Token Threshold"
                type="number"
                placeholder="Token Threshold"
                className=""
              />
              <Textarea
                name="Description"
                placeholder="Description / Benefits"
                className="h-[150px] placeholder:pt-2"
              />
            </div>
            <SelectComponent
              options={collectionOptions}
              selected={selectedCollection}
              onSelect={setSelectedCollection}
              placeholder="Accessible Content"
              className="mt-3.5 w-full"
            />
            <Button
              className="mt-5 !h-12 w-full !border-[#666666] !text-[#666666] hover:!border-primary hover:!text-white sm:!h-[60px] sm:!w-[217px]"
              variant="outline"
            >
              ADD TIER <HiOutlinePlusCircle />
            </Button>
          </label>
          <div className="mt-5 flex w-full flex-wrap justify-between gap-4">
            <Button className="!h-12 w-full !border-none !bg-[#999999] !text-white sm:!h-[62px] sm:!w-[200px]">
              CANCEL
            </Button>
            <Button className="!h-12 w-full sm:!h-[62px] sm:!w-[200px]">
              SAVE
            </Button>
          </div>
        </div>
        {removebg ? (
          ''
        ) : (
          <p className="mt-12 text-center text-sm text-[#666666]">
            © 2025 TEMOC • All Rights Reserved
          </p>
        )}
      </div>
    </div>
  );
};

export default AccessManagement;
