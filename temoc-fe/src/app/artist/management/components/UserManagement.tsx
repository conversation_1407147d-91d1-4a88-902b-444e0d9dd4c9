'use client';
import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import TokenTable from '@/components/ui/TokenTable';
import React, { useState } from 'react';
import BlockedUsers from './BlockedUsers';
import Select from '@/components/ui/Select';

const collectionOptions = [
  { value: 'Ranked By', label: 'Ranked By' },
  { value: 'Album 1', label: 'Album 1' },
  { value: 'Album 2', label: 'Album 2' },
  { value: 'Album 3', label: 'Album 3' },
];
const collectionOptionss = [
  { value: 'Last 1 Days', label: 'Last 1 Days' },
  { value: 'Last 24 Hours', label: 'Last 24 Hours' },
  { value: 'Last 7 Days', label: 'Last 7 Days' },
  { value: 'Last 1 Month', label: 'Last 1 Month' },
];

const columns = [
  { key: 'rank', label: 'Rank' },
  { key: 'user', label: 'User' },
  { key: 'Username', label: 'Username' },
  { key: 'amount', label: 'Amount' },
  { key: 'actions', label: 'Actions' },
];

const data = [
  {
    rank: '1',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '2',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan2.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '3',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan3.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '4',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan4.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '5',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan5.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '6',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan1.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '7',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan2.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '8',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan3.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '9',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan4.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
  {
    rank: '10',
    user: (
      <div className="flex flex-col items-center gap-2 sm:flex-row">
        <ImageComponent
          src="/assets/images/trading/fan5.svg"
          height={30}
          width={30}
        />
        <p className="text-base text-[#333333]">Andy Haskin</p>
      </div>
    ),
    Username: 'crypto_fan_452',
    amount: '200 Andy Haskin Token',
    actions: (
      <div>
        <Button className="!h-[30px] !w-[245px] !text-sm" variant="outline">
          BLOCK
        </Button>
      </div>
    ),
  },
];

const UserManagement = () => {
  const [selectedCollection, setSelectedCollection] = useState(
    collectionOptions[0],
  );
  const [selectedTime, setSelectedTime] = useState(collectionOptionss[1]);
  const [showBlockedUsers, setShowBlockedUsers] = useState(false);
  return (
    <div className="mt-12 pb-5 sm:px-12">
      {showBlockedUsers ? (
        <BlockedUsers />
      ) : (
        <div className="mx-auto mt-2.5 flex w-full flex-col items-center">
          <h3 className="text-2xl font-semibold uppercase sm:text-[30px]">
            {/* {Fans ? 'Fans' : 'User Management'} */}
            User Management
          </h3>
          <div className="mt-5 w-full rounded-2xl p-[30px] shadow-[0_4px_10px_rgba(0,0,0,0.08)]">
            <div className="flex flex-wrap justify-between">
              <div className="flex flex-wrap items-center gap-4">
                <input
                  placeholder="Search by name / username"
                  className="h-[38px] w-full rounded-xl border-[#CECECE] placeholder:text-xs sm:w-[250px]"
                />
                <Select
                  options={collectionOptions}
                  selected={selectedCollection}
                  onSelect={setSelectedCollection}
                  placeholder="Ranked By"
                  className="flex !h-9 w-full items-center !border-[#CECECE] sm:!w-[200px]"
                />
                <Select
                  options={collectionOptionss}
                  selected={selectedTime}
                  onSelect={setSelectedTime}
                  placeholder="Last 24 Hours"
                  className="flex !h-9 w-full items-center !border-[#CECECE] sm:!w-[200px]"
                />
              </div>
              <Button
                onClick={() => setShowBlockedUsers(true)}
                className="mt-5 !h-12 w-full !border-[#666666] !text-sm !text-[#666666] hover:!border-primary hover:!text-white sm:!h-[38px] sm:!w-[170px]"
                variant="outline"
              >
                BLOCKED USERS
              </Button>
            </div>

            <div className="mt-5">
              <TokenTable columns={columns} data={data} />
            </div>
          </div>
        </div>
      )}
      <p className="mt-12 text-center text-sm text-[#666666]">
        © 2025 TEMOC • All Rights Reserved
      </p>
    </div>
  );
};

export default UserManagement;
