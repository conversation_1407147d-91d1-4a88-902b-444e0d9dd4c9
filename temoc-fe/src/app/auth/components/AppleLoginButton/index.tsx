// import AppleLogin from 'react-apple-login';
// import { FaApple } from 'react-icons/fa';

// const AppleLoginButton = ({ title }: { title?: string }) => {
//   const handleSuccess = (response: any) => {
//     console.log('Apple login successful:', response);
//     // Handle the success (you can send the data to your backend or use it as needed)
//   };

//   //   const handleFailure = (error: any) => {
//   //     console.log('Apple login failed:', error);
//   //     // Handle failure case (e.g., show error message)
//   //   };

//   return (
//     <div className="flex w-full justify-center">
//       <AppleLogin
//         clientId="your-client-id" // Your Apple client ID
//         redirectURI="your-redirect-uri" // Your redirect URI
//         callback={handleSuccess} // `callback` will receive the response on success
//         render={(props) => (
//           <button
//             {...props}
//             className="flex w-full items-center justify-center gap-2 rounded-full border border-primary px-6 py-4 text-base font-bold uppercase text-black hover:bg-primary/10"
//           >
//             <span className="mt-1 leading-none">{title || 'LOGIN WITH'}</span>
//             <FaApple size={28} className="text-black" />
//           </button>
//         )}
//       />
//     </div>
//   );
// };

// export default AppleLoginButton;

import AppleSignin from 'react-apple-signin-auth';
import { FaApple } from 'react-icons/fa';

const AppleLoginButton = ({ title }: { title?: string }) => {
  const handleSuccess = (response: any) => {
    console.log('Apple login successful:', response);
    // Send response to backend or proceed with your auth flow
  };

  const handleError = (error: any) => {
    console.error('Apple login failed:', error);
    // Handle failure case (e.g., show error message)
  };

  return (
    <div className="flex w-full justify-center">
      <AppleSignin
        authOptions={{
          clientId: 'your-client-id', // Replace with your Apple Client ID
          scope: 'name email',
          redirectURI: 'your-redirect-uri', // Replace with your valid redirect URI
          state: 'state',
          nonce: 'nonce',
          usePopup: true,
        }}
        uiType="dark" // Add the required uiType property (e.g., 'dark' or 'light')
        onSuccess={handleSuccess}
        onError={handleError}
        render={({ onClick }: { onClick: () => void }) => (
          <button
            onClick={onClick}
            className="flex w-full items-center justify-center gap-2 rounded-full border border-primary px-6 py-4 text-base font-bold uppercase text-black hover:bg-primary/10"
          >
            <span className="mt-1 leading-none">{title || 'LOGIN WITH'}</span>
            <FaApple size={28} className="text-black" />
          </button>
        )}
      />
    </div>
  );
};

export default AppleLoginButton;
