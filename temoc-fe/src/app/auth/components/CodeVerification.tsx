'use client';
import { Button } from '@/components/common';
import { authService } from '@/services/auth.service';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import Pin<PERSON>ield from 'react-pin-field';
import { toast } from 'react-toastify';
import CountdownComponent from '@/components/ui/CountdownComponent';

interface IProps {
  email: string;
}

const CodeVerification = ({ email }: IProps) => {
  const [pin, setPin] = useState('');
  const [isFilled, setIsFilled] = useState(false);
  const [showResend, setShowResend] = useState<boolean>(false);
  // const { login } = useAuth();
  const router = useRouter();

  const handlePinChange = (value: any) => {
    setPin(value);
    setIsFilled(value.length > 0);
  };

  const codeVerifyMutation = useMutation({
    mutationFn: authService.verifyEmail,
    onSuccess: (data) => {
      const codeVerifyData = data?.data?.data;
      console.log(codeVerifyData, 'codeVerifyData===>', data, 'data===>');
      if (codeVerifyData.isVerified === true) {
        toast.success('Email verified successfully');
        // login(codeVerifyData.token);
        router.push('/app');
      } else {
        toast.error(codeVerifyData?.message);
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message);
    },
  });

  const onSubmit = async () => {
    try {
      const data = {
        email,
        code: pin,
      };
      await codeVerifyMutation.mutateAsync(data);
    } catch (error) {
      console.error('Error', error);
    }
  };

  const resendCode = useMutation({
    mutationFn: authService.resendCode,
    onSuccess: (data) => {
      const resendCodeData = data?.data?.data;
      console.log(resendCodeData, 'resendCodeData');
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  const handleCountdownComplete = () => {
    setShowResend(true);
  };

  const resetCountdown = async () => {
    setShowResend(false);
    const data = { email };
    await resendCode.mutateAsync(data);
  };

  const countdown = useMemo(() => {
    return (
      <CountdownComponent
        onComplete={handleCountdownComplete}
        className="mt-4 h-7 justify-center"
      />
    );
  }, []);

  return (
    <div className="relative z-20 flex flex-col items-center justify-center">
      <h2 className="h1 text-center text-primary">Enter Verification Code</h2>
      <p className="mt-10 max-w-[350px] text-center text-black dark:text-white sm:mt-20">
        Please enter the 6-digit code that was sent to your email: {email}
      </p>

      {!showResend && countdown}

      {showResend && (
        <p className="mt-5">
          Didn&apos;t receive the code?{' '}
          <span
            onClick={resetCountdown}
            className="cursor-pointer border-b border-primary pb-[2px] text-primary"
          >
            Click to resend.
          </span>
        </p>
      )}
      <div className="mx-auto mt-10 grid grid-cols-6 gap-3">
        <PinField
          type="tel"
          length={6}
          onComplete={(p) => {
            if (/^\d{6}$/.test(p)) {
              setPin(p);
            } else {
              toast.error('Please enter a valid 6-digit code.');
            }
          }}
          onChange={(value) => {
            if (/^[0-9]*$/.test(value)) {
              handlePinChange(value);
            }
          }} // Trigger onChange when input changes
          className={`h-[65px] w-[65px] overflow-y-auto rounded-md border-4 bg-white text-center text-2xl font-bold !text-black md:rounded-xl xs:h-[45px] xs:w-[45px] xs:p-1.5 ${
            isFilled ? 'border-primary' : 'dark:border-[#D0D0D0]'
          }`} // Conditional border color based on input
        />
      </div>
      <Button
        type="submit"
        disabled={pin.length < 5}
        onClick={onSubmit}
        isLoading={codeVerifyMutation.isPending}
        className="mt-10 w-full"
      >
        Submit
      </Button>
    </div>
  );
};

export default CodeVerification;
