import React from 'react';
import FacebookLogin from '@greatsumini/react-facebook-login';
import { FaFacebook } from 'react-icons/fa';

const CustomButton = ({ onClick, title }: any) => (
  <button
    onClick={onClick}
    className="flex w-full items-center justify-center gap-2 rounded-full border border-primary px-6 py-4 text-base font-bold uppercase text-black hover:bg-primary/10"
  >
    <span>{title || 'LOGIN WITH'} </span>
    <FaFacebook size={20} className="text-[#3B5998]" />
  </button>
);

const FacebookLoginButton = ({ title }: { title?: string }) => {
  return (
    <div className="flex w-full justify-center">
      <FacebookLogin
        appId="1088597931155576" // Replace with your Facebook App ID
        onSuccess={(response) => {
          console.log('Login Success!', response);
        }}
        onFail={(error) => {
          console.log('Login Failed!', error);
        }}
        onProfileSuccess={(response) => {
          console.log('Get Profile Success!', response);
        }}
        render={({ onClick, logout }) => (
          <CustomButton
            onClick={onClick}
            onLogoutClick={logout}
            title={title}
          />
        )}
      />
    </div>
  );
};

export default FacebookLoginButton;
