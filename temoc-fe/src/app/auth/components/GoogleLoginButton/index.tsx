import { useGoogleLogin } from '@react-oauth/google';
import { FaGoogle } from 'react-icons/fa'; // Import Google Icon from react-icons

const GoogleLoginButton = ({ title }: { title?: string }) => {
  const login = useGoogleLogin({
    onSuccess: (tokenResponse) =>
      console.log('Google Login Success:', tokenResponse),
    onError: (error) => console.error('Google Login Failed:', error),
  });

  return (
    <button
      onClick={() => login()}
      className="flex w-full items-center justify-center gap-2 rounded-full border border-primary px-6 py-4 text-base font-bold uppercase text-black hover:bg-primary/10"
    >
      <span>{title || 'LOGIN WITH'}</span>
      <FaGoogle size={20} className="text-[#0085F7]" />
    </button>
  );
};

export default GoogleLoginButton;
