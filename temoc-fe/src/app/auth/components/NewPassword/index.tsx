'use client';
import React, { useState, useMemo } from 'react';
import { Button } from '@/components/common/index';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ResetPasswordSchema } from '@/utils/schema';
import InputError from '@/components/common/Forms/InputError';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/common/Forms/Input';
import { useMutation } from '@tanstack/react-query';
import { authService } from '@/services/auth.service';
import { toast } from 'react-toastify';
import CountdownComponent from '@/components/ui/CountdownComponent';

interface IProps {
  email: string;
}

const NewPassword = ({ email }: IProps) => {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(ResetPasswordSchema),
  });

  const [showResend, setShowResend] = useState<boolean>(false);
  const [showPasswordField, setShowPasswordField] = useState<boolean>(false);

  // Reset password mutation
  const resetMutation = useMutation({
    mutationFn: authService.resetPassword,
    onSuccess: (data) => {
      const resetData = data?.data?.data;
      toast.success(resetData?.message);
      router.push('/auth/sign-in');
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  // Handle form submission
  const onSubmit = async () => {
    try {
      const data = {
        code: getValues('code'),
        email,
        password: getValues('password'),
        confirmPassword: getValues('confirmPassword'),
      };
      await resetMutation.mutateAsync(data);
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  // Resend code mutation
  const resendCode = useMutation({
    mutationFn: authService.forgotPassword,
    onSuccess: (data) => {
      toast.success(data?.data?.data?.message);
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  // Handle countdown completion
  const handleCountdownComplete = () => {
    setShowResend(true);
  };

  // Handle resend code and reset countdown
  const resetCountdown = async () => {
    setShowResend(false);
    const data = { email };
    await resendCode.mutateAsync(data);
  };

  // Memoized Countdown component to prevent unnecessary re-renders
  const countdown = useMemo(() => {
    return (
      <CountdownComponent
        onComplete={handleCountdownComplete}
        className="mt-4 h-7 justify-center"
      />
    );
  }, [showResend]); // Only re-render when `showResend` changes

  return (
    <div className="relative">
      <form onSubmit={handleSubmit(onSubmit)} className="relative z-20">
        <div className="mb-10 flex flex-col items-center">
          <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
            Set New Password
          </h1>
        </div>
        <div className="mt-4">
          <Input
            placeholder="Enter OTP"
            type="number"
            name="code"
            register={register}
          />
          <InputError error={errors.code?.message} />
        </div>

        {/* Memoized Countdown Timer */}
        {!showResend && countdown}

        {/* Show resend link when countdown is complete */}
        {showResend && (
          <p className="mt-4 h-7">
            Didn&apos;t receive the code?{' '}
            <span
              onClick={resetCountdown}
              className="cursor-pointer border-b border-primary pb-[2px] text-primary"
            >
              Click to resend.
            </span>
          </p>
        )}

        <div className="mt-4">
          <Input
            placeholder="Password"
            type="password"
            name="password"
            passwordOverride={true}
            showPasswordField={showPasswordField}
            setShowPasswordField={setShowPasswordField}
            register={register}
          />
          <InputError error={errors.password?.message} />
        </div>
        <div className="mt-4">
          <Input
            placeholder="Confirm Password"
            name="confirmPassword"
            type="password"
            passwordOverride={true}
            showPasswordField={showPasswordField}
            setShowPasswordField={setShowPasswordField}
            register={register}
          />
          <InputError error={errors.confirmPassword?.message} />
        </div>

        <Button
          type="submit"
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          isLoading={resetMutation.isPending}
          disabled={resetMutation.isPending}
        >
          CHANGE PASSWORD
        </Button>
      </form>
    </div>
  );
};

export default NewPassword;
