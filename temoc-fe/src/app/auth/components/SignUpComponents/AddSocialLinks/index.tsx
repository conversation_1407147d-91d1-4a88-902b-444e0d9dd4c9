import React from 'react';
import { Button } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import BackButton from '../../BackButton';
import SocialLinks from '@/components/ui/SocialLinks';
import Link from 'next/link';

interface IProps {
  handleClick: (step: string) => void;
}

const AddSocialLinks = ({ handleClick }: IProps) => {
  return (
    <div className="mx-auto flex w-full flex-col pt-7 lg:w-[468px]">
      <BackButton onClick={() => handleClick('PickHandle')} />
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto mb-12"
        className="object-contain"
        alt=""
      />
      {/* <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        ADD YOUR SOCIAL LINKS
      </h1>
      <p className="mt-5 text-center text-base xs:text-sm">
        Help people find you wherever you are. Connect your other accounts to
        show them on your page. We&apos;ll never post on your behalf.
      </p> */}
      <SocialLinks />

      <Link href="/artist/initial-dashboard">
        <Button
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          type="submit"
        >
          Continue
        </Button>
      </Link>
      <Link href="/artist/initial-dashboard" className="mt-3 block w-full">
        <button className="flex w-full items-center justify-center gap-1 rounded-full border-[1.5px] border-[#666666] px-14 py-4 text-base font-semibold text-[#666666] hover:bg-gray-100 focus:outline-none">
          SKIP
        </button>
      </Link>
    </div>
  );
};

export default AddSocialLinks;
