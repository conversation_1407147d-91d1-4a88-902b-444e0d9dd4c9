import React, { useState, useEffect } from 'react';
import { Button } from '@/components/common';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { UserNameSchema } from '@/utils/schema'; // Import the schema you created
import ImageComponent from '@/components/common/ImageComponent';
import { FormDataProps } from '@/app/auth/sign-up/page';
import BackButton from '../../BackButton';

interface IFormData {
  username: string;
}

interface IPickHandleProps {
  handleClick: (component: string) => void;
  formData: FormDataProps;
  setFormData: React.Dispatch<React.SetStateAction<FormDataProps>>;
}

const PickHandle = ({
  handleClick,
  formData,
  setFormData,
}: IPickHandleProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<IFormData>({
    resolver: yupResolver(UserNameSchema),
  });

  const [selectedHandle, setSelectedHandle] = useState('@johndoe'); // Track selected suggested name
  const [suggestedNames, setSuggestedNames] = useState<string[]>([]); // List of generated suggestions

  const username = watch('username', '@johndoe');

  // Generate suggested names based on username input
  const generateSuggestedNames = (baseUsername: string) => {
    const suffixes = ['123', '_fan', '_official', '01'];
    return suffixes.map((suffix) => `${baseUsername}${suffix}`);
  };

  // UseEffect to update suggestions based on the username input
  useEffect(() => {
    if (username.length > 0) {
      const suggestions = generateSuggestedNames(username);
      setSuggestedNames(suggestions);
    }
  }, [username]);

  const onSubmit: SubmitHandler<IFormData> = async (data) => {
    try {
      setFormData((prev) => ({ ...prev, userName: data.username }));
      // console.log('Username:', data);
      handleClick('AddSocialLinks');
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  console.log(formData, 'formData==>+username');

  return (
    <div className="mx-auto flex w-full flex-col pt-7 lg:w-[468px]">
      <BackButton onClick={() => handleClick('SelectName')} />
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto"
        className="object-contain"
        alt=""
      />
      <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        PICK YOUR HANDLE
      </h1>
      <p className="mt-5 text-center text-base xs:text-sm">
        This is how others find and tag you. It is totally unique to you &
        cannot be changed later.
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="my-7">
          <Input
            placeholder="Username"
            name="username"
            register={register}
            autoFocus
          />
          <InputError error={errors.username?.message} />
        </div>

        {/* Suggested Names */}
        <div className="mb-5">
          <label className="mb-2 block text-sm font-semibold">
            Suggested Name
          </label>
          <div className="flex flex-col space-y-3">
            {suggestedNames.map((handle) => (
              <div key={handle} className="flex items-center">
                <input
                  type="radio"
                  id={handle}
                  name="handle"
                  value={handle}
                  checked={selectedHandle === handle}
                  onChange={() => setSelectedHandle(handle)}
                  className="mr-2"
                />
                <label htmlFor={handle} className="text-base">
                  {handle}
                </label>
              </div>
            ))}
          </div>
        </div>

        <Button
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          type="submit"
        >
          Continue
        </Button>
      </form>
    </div>
  );
};

export default PickHandle;
