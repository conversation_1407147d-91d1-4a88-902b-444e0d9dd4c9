import { FormDataProps } from '@/app/auth/sign-up/page';
import { Button } from '@/components/common';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import ImageComponent from '@/components/common/ImageComponent';
import { NameSchema } from '@/utils/schema';
import { yupResolver } from '@hookform/resolvers/yup';
import React from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import BackButton from '../../BackButton';
import { useRouter } from 'next/navigation';
interface IFormData {
  firstName: string;
  lastName: string;
}

interface ISelectNameProps {
  handleClick: (component: string) => void;
  formData: FormDataProps;
  setFormData: React.Dispatch<React.SetStateAction<FormDataProps>>;
}

const SelectName = ({
  handleClick,
  // formData,
  setFormData,
}: ISelectNameProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<IFormData>({
    resolver: yupResolver(NameSchema),
  });

  const onSubmit: SubmitHandler<IFormData> = async (data) => {
    try {
      const fullName = `${data.firstName} ${data.lastName}`;
      console.log(fullName, 'fullName');
      setFormData((prev) => ({ ...prev, fullName }));
      handleClick('PickHandle');
      handleClick('PickHandle');
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  // console.log(formData, 'formData==>');
  const router = useRouter();
  return (
    <div className="flex w-full flex-col pt-7 lg:w-[468px]">
      <BackButton onClick={() => router.back()} />
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto"
        className="object-contain"
        alt=""
      />
      <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        SELECT YOUR NAME
      </h1>
      <p className="mt-5 text-center text-base xs:text-sm">
        What should we call you?
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-7 w-full">
          <Input
            placeholder="First Name"
            name="firstName"
            register={register}
          />
          <InputError error={errors.firstName?.message} />
        </div>
        <div className="mt-7 w-full">
          <Input placeholder="Last Name" name="lastName" register={register} />
          <InputError error={errors.lastName?.message} />
        </div>
        <Button
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          type="submit"
        >
          Continue
        </Button>
      </form>
    </div>
  );
};

export default SelectName;
