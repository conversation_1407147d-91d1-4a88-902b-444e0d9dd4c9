'use client';
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/common';
import { Input } from '@/components/common/Forms/Input';
import { SubmitHandler, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { SelectPasswordSchema } from '@/utils/schema'; // Import the schema you created
import ImageComponent from '@/components/common/ImageComponent';
import { CheckArrow } from '@/components/common/Icons';
import { FormDataProps } from '@/app/auth/sign-up/page';
import BackButton from '../../BackButton';
import { useMutation } from '@tanstack/react-query';
import { authService } from '@/services/auth.service';
// import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';

interface IFormData {
  password: string;
  confirmPassword: string;
}

interface SelectPasswordProps {
  handleClick: (step: string) => void;
  formData: FormDataProps;
  setFormData: React.Dispatch<React.SetStateAction<FormDataProps>>;
}

const SelectPassword = ({
  handleClick,
  formData,
  setFormData,
}: SelectPasswordProps) => {
  const { register, handleSubmit, watch } = useForm<IFormData>({
    resolver: yupResolver(SelectPasswordSchema),
  });

  const [showPasswordField, setShowPasswordField] = useState<boolean>(false);
  const [rules, setRules] = useState({
    hasNumber: false,
    hasMinLength: false,
    passwordsMatch: false,
    hardToGuess: false,
  });

  // const { login } = useAuth();
  const router = useRouter();
  const password = watch('password');
  const confirmPassword = watch('confirmPassword');

  useEffect(() => {
    setRules({
      hasNumber: /\d/.test(password),
      hasMinLength: password?.length >= 8,
      passwordsMatch: password === confirmPassword,
      hardToGuess: /[A-Z]/.test(password) && /[!@#$%^&*]/.test(password),
    });
  }, [password, confirmPassword]);

  const signUpMutation = useMutation({
    mutationFn: authService.signup,
    onSuccess: (data) => {
      const signUpData = data?.data?.data;
      if (signUpData.user) {
        // login(signUpData.access_token);
        router.push('/app');
        toast.success('Login to your account');
      } else {
        toast.error(signUpData?.message);
      }
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  const onSubmit: SubmitHandler<IFormData> = async (data) => {
    try {
      setFormData((prev) => ({
        ...prev,
        password: data.password,
        confirmPassword: data.confirmPassword,
      }));
      await signUpMutation.mutateAsync(formData);
      console.log('password:', data);
      handleClick('AddSocialLinks');
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  console.log(formData, 'formData==>password');

  return (
    <div className="mx-auto flex w-full flex-col pt-7 lg:w-[468px]">
      <BackButton onClick={() => handleClick('PickHandle')} />
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto"
        className="object-contain"
        alt=""
      />
      <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        SELECT YOUR PASSWORD
      </h1>
      <p className="mt-5 text-center text-base xs:text-sm">
        Create a password that&apos;s secure and easy to remember.
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="my-3">
          <Input
            placeholder="Password"
            type="password"
            passwordOverride={true}
            showPasswordField={showPasswordField}
            setShowPasswordField={setShowPasswordField}
            name="password"
            register={register}
            tabIndex={3}
          />
        </div>
        <Input
          placeholder="Confirm Password"
          type="password"
          passwordOverride={true}
          showPasswordField={showPasswordField}
          setShowPasswordField={setShowPasswordField}
          name="confirmPassword"
          register={register}
          tabIndex={4}
        />

        <ul className="mt-4 space-y-2 text-sm">
          <li
            className={` ${rules.hasNumber ? 'text-primary' : 'text-gray-400'} flex items-center gap-1`}
          >
            <CheckArrow />{' '}
            <span
              className={`${rules.hasNumber ? 'text-black' : 'text-gray-400'}`}
            >
              Must contain numbers
            </span>
          </li>
          <li
            className={`${rules.hasMinLength ? 'text-primary' : 'text-gray-400'} flex items-center gap-1`}
          >
            <CheckArrow />{' '}
            <span
              className={`${rules.hasMinLength ? 'text-black' : 'text-gray-400'}`}
            >
              At least 8 characters
            </span>
          </li>
          <li
            className={`${rules.passwordsMatch ? 'text-primary' : 'text-gray-400'} flex items-center gap-1`}
          >
            <CheckArrow />
            <span
              className={`${rules.passwordsMatch ? 'text-black' : 'text-gray-400'}`}
            >
              {' '}
              Passwords match
            </span>
          </li>
          <li
            className={`${rules.hardToGuess ? 'text-primary' : 'text-gray-400'} flex items-center gap-1`}
          >
            <CheckArrow />{' '}
            <span
              className={`${rules.hardToGuess ? 'text-black' : 'text-gray-400'}`}
            >
              Hard to guess (uppercase + symbol)
            </span>
          </li>
        </ul>

        <p className="mt-7 text-xs">
          By clicking continue, you state you have read and agree to TEMOC&apos;{' '}
          <span className="text-primary">Terms of Service</span> and{' '}
          <span className="text-primary">Privacy Policy</span>.
        </p>

        <Button
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          disabled={!Object.values(rules).every(Boolean)}
          type="submit"
        >
          Continue
        </Button>
      </form>
    </div>
  );
};

export default SelectPassword;
