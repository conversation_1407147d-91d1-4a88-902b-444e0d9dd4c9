'use client';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ForgotSchema } from '@/utils/schema';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { Button } from '@/components/common';
import { authService } from '@/services/auth.service';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { useState } from 'react';
import Container from '@/components/common/Container';
import NewPassword from '../components/NewPassword';
import ImageComponent from '@/components/common/ImageComponent';
import BackButton from '../components/BackButton';

const ForgotPassword = () => {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(ForgotSchema),
  });

  const [screen, setScreen] = useState(1);

  const forgotMutation = useMutation({
    mutationFn: authService.forgotPassword,
    onSuccess: (data) => {
      console.log(data, 'forgot');
      toast.success(data?.data?.data?.message);
      setScreen(2);
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  const onSubmit = async () => {
    try {
      const data = {
        email: getValues('email'),
      };
      await forgotMutation.mutateAsync(data);
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  return (
    <div className="relative">
      <Container className="relative flex min-h-screen items-start justify-center pb-[100px] pt-[100px] sm:pt-[140px]">
        <BackButton />
        <div className="w-full sm:w-[540px]">
          <ImageComponent
            src="/assets/images/logo.svg"
            fill
            figClassName="w-[203px] h-[71px] mx-auto"
            className="object-contain"
            alt=""
          />
          {screen == 1 ? (
            <form onSubmit={handleSubmit(onSubmit)}>
              <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
                Confirm Your Email
              </h1>

              <div className="relative my-8">
                <Input
                  placeholder="Email Address"
                  name="email"
                  register={register}
                />
                <InputError error={errors.email?.message} />
              </div>

              <Button
                className="w-full !py-5 !font-bold uppercase"
                arrow={true}
                type="submit"
                isLoading={forgotMutation.isPending}
                disabled={forgotMutation.isPending}
              >
                SEND ME CODE
              </Button>
            </form>
          ) : (
            <NewPassword email={getValues('email')} />
          )}
        </div>
      </Container>
      <p className="absolute bottom-2 w-full text-center text-xs text-white">
        © Copyright {new Date().getFullYear() - 1} - {new Date().getFullYear()}
        , All Rights Reserved by Fantasy Showdown
      </p>
    </div>
  );
};

export default ForgotPassword;
