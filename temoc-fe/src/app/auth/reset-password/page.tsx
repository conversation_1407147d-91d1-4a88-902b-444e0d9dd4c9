'use client';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { ResetPasswordSchema } from '@/utils/schema';
import ImageComponent from '@/components/common/ImageComponent';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { Button } from '@/components/common';

interface IAuth {
  password: string;
  confirmPassword: string;
}

const ResetPassword = () => {
  const {
    register,
    handleSubmit,
    getValues,
    formState: { errors },
  } = useForm({ resolver: yupResolver(ResetPasswordSchema) });

  const onSubmit = () => {
    const data: IAuth = {
      password: getValues('password'),
      confirmPassword: getValues('confirmPassword'),
    };
    console.log(data, 'data');
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex w-full flex-col px-5 pt-[60px] lg:w-2/5 xl:px-32"
    >
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto"
        className="object-contain"
        alt=""
      />
      <h1 className="mt-20 text-center text-2xl font-semibold sm:text-[40px]">
        Create New Password
      </h1>
      <div className="mt-14">
        {/* <Label className="text-sm text-white">New Password</Label>   */}
        <Input
          placeholder="New Password*"
          type="password"
          name="password"
          register={register}
          size="sm"
        />
        <InputError error={errors.password?.message} />
      </div>
      <div className="mt-5">
        {/* <Label className="text-sm text-white">Re-Enter Password</Label> */}
        <Input
          placeholder="Confirm New Password*"
          type="password"
          name="confirmPassword"
          register={register}
          size="sm"
        />
        <InputError error={errors.confirmPassword?.message} />
      </div>
      <Button type="submit" className="mt-8">
        Create New Password
      </Button>
      <div className="flex w-full justify-center">
        <p className="fixed bottom-5 text-base font-normal text-[#ADADAD]">
          Copyright © 2025 All Rights Reserved.
        </p>
      </div>
    </form>
  );
};

export default ResetPassword;
