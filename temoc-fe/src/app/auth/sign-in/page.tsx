'use client';
import Link from 'next/link';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoginSchema } from '@/utils/schema';
import ImageComponent from '@/components/common/ImageComponent';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { Button } from '@/components/common';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { authService } from '@/services/auth.service';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
// import { useAuth } from '@/hooks/useAuth';
import Slider from '../components/AuthSlider';
import {
  AppleLoginButton,
  FacebookLoginButton,
  GoogleLoginButton,
} from '../components';

interface FormData {
  email: string;
  password: string;
}

const SignIn = () => {
  const {
    handleSubmit,
    register,
    formState: { errors },
  } = useForm<FormData>({
    resolver: yupResolver(LoginSchema),
  });
  const router = useRouter();
  // const { login } = useAuth();

  const loginMutation = useMutation({
    mutationFn: authService.login,
    onSuccess: () => {
      // Always redirect to explore page - all users start as fans
      router.push('/app/explore');
      toast.success('Login Successful!');
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      await loginMutation.mutateAsync(data);
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  return (
    <div className="flex w-full flex-col xl:flex-row">
      {/* Left section (Form Section) */}
      <div className="mx-auto flex h-screen w-full flex-col items-center justify-between gap-5 overflow-auto bg-white px-5 pb-5 pt-10 sm:w-[70%] lg:w-[60%] lg:px-20">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="flex w-full flex-col pt-7 lg:w-[430px]"
        >
          <ImageComponent
            src="/assets/images/logo.svg"
            fill
            figClassName="w-[50px] h-[50px] mx-auto"
            className="object-contain"
            alt=""
          />
          <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
            Welcome Back, <span className="text-primary">Artist</span>
          </h1>
          <p className="mt-5 text-center text-base xs:text-sm">
            Log in to manage your artist profile, interact with fans, track your
            earnings, and grow your community.
          </p>
          <div className="mt-7 w-full">
            <Input
              placeholder="Email Address"
              name="email"
              type="email"
              register={register}
            />
            <InputError error={errors.email?.message} />
          </div>
          <div className="mt-7 w-full">
            <Input
              placeholder="Password"
              name="password"
              type="password"
              register={register}
            />
            <InputError error={errors.password?.message} />
          </div>

          <div className="flex justify-end">
            <Link
              href="/auth/forgot-password"
              className="inline cursor-pointer text-base font-normal text-primary underline xs:text-sm"
            >
              Forgot password?
            </Link>
          </div>
          <Button
            isLoading={loginMutation.isPending}
            disabled={loginMutation.isPending}
            className="mt-10 w-full !py-4 !font-bold"
            type="submit"
            arrow={true}
          >
            LOGIN NOW
          </Button>

          <div className="my-4 flex items-center justify-center space-x-4">
            <hr className="flex-grow border-t border-gray-300" />
            <span className="text-lg font-semibold text-orange-500">OR</span>
            <hr className="flex-grow border-t border-gray-300" />
          </div>
          <div className="flex flex-col items-center gap-4">
            <GoogleLoginButton />
            <AppleLoginButton />
            <FacebookLoginButton />
          </div>
          <p className="mx-auto mt-5 text-base xs:text-sm">
            I don’t have an account.{' '}
            <Link href="/auth/sign-up">
              <u className="text-primary">Signup Here</u>
            </Link>
          </p>
        </form>
        <div className="flex w-full justify-center">
          <p className="text-base font-normal text-[#ADADAD] xs:text-sm">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      </div>

      {/* Right section (Slider Section) */}
      <div className="mt-10 hidden w-full lg:mt-0 lg:w-[40%] xl:block">
        <Slider />
      </div>
    </div>
  );
};

export default SignIn;
