'use client';
import Link from 'next/link';
import { yupResolver } from '@hookform/resolvers/yup';
import { SignUpSchema } from '@/utils/schema';
import ImageComponent from '@/components/common/ImageComponent';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { Button } from '@/components/common';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useState } from 'react';
import Slider from '../components/AuthSlider';
import {
  AppleLoginButton,
  FacebookLoginButton,
  GoogleLoginButton,
} from '../components';
import { AiOutlineUser } from 'react-icons/ai';
import {
  AddSocialLinks,
  PickHandle,
  SelectName,
  SelectPassword,
} from '../components/SignUpComponents';

interface IFormData {
  email: string;
}

export interface FormDataProps {
  email: string;
  fullName?: string;
  userName?: string;
  password?: string;
  confirmPassword?: string;
}

const SignUp = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<IFormData>({
    resolver: yupResolver(SignUpSchema),
  });

  const [selectedComponent, setSelectedComponent] = useState<string>('');
  const [formData, setFormData] = useState<FormDataProps>({ email: '' });

  const onSubmit: SubmitHandler<IFormData> = async (data) => {
    try {
      setFormData({ email: data.email });
      handleClick('SelectName');
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };

  const handleClick = (component: string) => {
    setSelectedComponent(component);
  };

  return (
    <div className="flex w-full flex-col xl:flex-row">
      <div className="mx-auto flex h-screen w-full flex-col items-center justify-between gap-10 overflow-auto bg-white px-5 pb-5 pt-10 sm:w-[70%] lg:w-[60%] lg:px-20">
        {(() => {
          switch (selectedComponent) {
            case 'SelectName':
              return (
                <SelectName
                  handleClick={handleClick}
                  formData={formData}
                  setFormData={setFormData}
                />
              );
            case 'PickHandle':
              return (
                <PickHandle
                  handleClick={handleClick}
                  formData={formData}
                  setFormData={setFormData}
                />
              );
            case 'SelectPassword':
              return (
                <SelectPassword
                  handleClick={handleClick}
                  formData={formData}
                  setFormData={setFormData}
                />
              );
            case 'AddSocialLinks':
              return <AddSocialLinks handleClick={handleClick} />;
            default:
              return (
                <div className="flex w-full flex-col pt-7 lg:w-[468px]">
                  <ImageComponent
                    src="/assets/images/logo.svg"
                    fill
                    figClassName="w-[203px] h-[71px] mx-auto"
                    className="object-contain"
                    alt=""
                  />
                  <h1 className="mt-12 text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
                    Join the Creator Revolution
                  </h1>
                  <p className="mt-5 text-center text-base xs:text-sm">
                    Set up your artist profile, launch your own token, and build
                    a fan-powered economy — all in one place.
                  </p>

                  <form onSubmit={handleSubmit(onSubmit)}>
                    <div className="my-7">
                      <Input
                        placeholder="Email"
                        name="email"
                        register={register}
                      />
                      <InputError error={errors.email?.message} />
                    </div>
                    <Button
                      className="w-full !py-5 !font-bold"
                      type="submit"
                      arrow={true}
                    >
                      SIGNUP NOW
                    </Button>
                  </form>
                  <div className="my-4 flex items-center justify-center space-x-4">
                    <hr className="flex-grow border-t border-gray-300" />
                    <span className="text-lg font-semibold text-orange-500">
                      OR
                    </span>
                    <hr className="flex-grow border-t border-gray-300" />
                  </div>
                  <div className="flex flex-col items-center gap-4">
                    <GoogleLoginButton title="SIGNUP WITH" />
                    <AppleLoginButton title="SIGNUP WITH" />
                    <FacebookLoginButton title="SIGNUP WITH" />
                  </div>
                  <p className="mx-auto mt-3 text-base">
                    I already have an Account.{' '}
                    <Link href="/auth/sign-in">
                      <u className="text-primary">Signin Now.</u>
                    </Link>
                  </p>
                </div>
              );
          }
        })()}

        <div className="flex flex-col justify-center gap-10">
          <button className="flex items-center justify-center gap-1 rounded-full border-[1.5px] border-[#666666] px-14 py-4 text-base font-semibold text-[#666666] hover:bg-gray-100 focus:outline-none">
            <span>SIGNUP AS A FAN</span>
            <AiOutlineUser size={20} className="text-[#666666]" />
          </button>
          <p className="text-base font-normal text-[#ADADAD] xs:text-sm">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      </div>
      <div className="relative mt-10 hidden w-full lg:mt-0 lg:w-[40%] xl:block">
        <Slider />
      </div>
    </div>
  );
};

export default SignUp;
