import React from 'react';
import Link from 'next/link';
import { IoMdArrowRoundBack } from 'react-icons/io';
import Container from '@/components/common/Container';
import CodeVerification from '../components/CodeVerification';

const VerifyEmail = (params: any) => {
  return (
    <div className="relative">
      <Container className="relative flex min-h-screen items-center justify-center pb-14 pt-20 sm:py-10">
        <Link
          href="/app"
          className="absolute left-4 top-4 flex items-center sm:top-7"
        >
          <IoMdArrowRoundBack className="text-blue h-7 w-7" />
          <h3 className="h3 !text-blue pl-3">Home</h3>
        </Link>
        <div className="w-full sm:w-[540px]">
          <CodeVerification email={params.searchParams.email} />
        </div>
      </Container>
    </div>
  );
};

export default VerifyEmail;
