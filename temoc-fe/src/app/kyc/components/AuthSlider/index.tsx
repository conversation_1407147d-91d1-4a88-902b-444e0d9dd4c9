import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import { Pagination, Autoplay } from 'swiper/modules';

const Slider = () => {
  return (
    <Swiper
      modules={[Pagination, Autoplay]}
      spaceBetween={50}
      slidesPerView={1}
      className="AtSocialSliderSignup h-full"
      pagination={{ clickable: true }}
      loop
      autoplay={{
        delay: 2500,
        disableOnInteraction: false,
      }}
    >
      <SwiperSlide>
        <div
          className="h-screen w-full bg-cover bg-center"
          style={{
            backgroundImage: `url('/assets/images/auth/1.png')`,
          }}
        >
          <div className="absolute bottom-12 left-8 max-w-[430px] text-white 2xl:left-12 2xl:max-w-[420px]">
            <h3 className="text-3xl font-extralight">GET STARTED</h3>
            <p className="text-5xl font-extralight">Join and Verify</p>
            <p className="mt-2 text-lg font-normal leading-tight">
              Sign up, create your artist profile, upload your details, and
              complete KYC to begin your journey.
            </p>
          </div>
        </div>
      </SwiperSlide>
      <SwiperSlide>
        <div
          className="h-screen w-full bg-black bg-cover bg-center"
          style={{
            backgroundImage: `url('/assets/images/auth/2.png')`,
          }}
        >
          <div className="absolute bottom-12 left-8 max-w-[450px] text-white 2xl:left-12 2xl:max-w-[500px]">
            <h3 className="text-3xl font-extralight">Choose Your Path</h3>
            <p className="text-5xl font-extralight">Launch Your Token</p>
            <p className="mt-2 text-lg font-normal leading-tight">
              Create your artist token with either Liquidity Pool or Fair Launch
              Presale options — customize name, symbol, and supply.
            </p>
          </div>
        </div>
      </SwiperSlide>
      <SwiperSlide>
        <div
          className="h-screen w-full bg-black bg-cover bg-center"
          style={{
            backgroundImage: `url('/assets/images/auth/3.png')`,
          }}
        >
          <div className="absolute bottom-12 left-8 max-w-[450px] text-white 2xl:left-12">
            <h3 className="text-3xl font-extralight">Build & Engage</h3>
            <p className="text-5xl font-extralight">Your Community</p>
            <p className="mt-2 text-lg font-normal leading-tight">
              Set up exclusive gated content, interact with fans, upload your
              media, and track your revenue in one place.
            </p>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
  );
};

export default Slider;
