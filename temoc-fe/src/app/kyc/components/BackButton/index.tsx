import { BackArrow } from '@/components/common/Icons';
import React from 'react';

interface BackButtonProps {
  onClick?: any;
}

const BackButton = ({ onClick }: BackButtonProps) => {
  return (
    <button
      onClick={onClick}
      className="absolute left-2.5 top-2.5 flex items-center rounded-full border border-[#CECECE] px-7 py-4"
    >
      <BackArrow />
      <span className="pl-3 text-xs font-medium text-primary sm:text-sm lg:text-base">
        Back
      </span>
    </button>
  );
};

export default BackButton;
