import React, { useState } from 'react';
import Facebook from '@/components/common/Icons/Facebook';
import Insta from '@/components/common/Icons/Insta';
import Youtube from '@/components/common/Icons/Youtube';
import X from '@/components/common/Icons/X';
import TikTok from '@/components/common/Icons/TikTok';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import { Input } from '@/components/common/Forms/Input';
import { BiSolidEdit } from 'react-icons/bi';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { MdDelete } from 'react-icons/md';
import { Button } from '@/components/common';

const socialPlatforms = [
  { name: 'facebook', icon: <Facebook /> },
  { name: 'instagram', icon: <Insta /> },
  { name: 'youtube', icon: <Youtube /> },
  { name: 'twitter', icon: <X /> },
  { name: 'tiktok', icon: <TikTok /> },
];

export interface IStepProps {
  nextStep: () => void;
}

const KycSocialLinks = ({ nextStep }: IStepProps) => {
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [newLink, setNewLink] = useState('');
  const [isValid, setIsValid] = useState(true);

  const queryClient = useQueryClient();

  const { data: socialLinksData } = useQuery({
    queryKey: ['kyc-links'],
    queryFn: () => userService.getKycSocialLinks(),
  });

  const links = socialLinksData?.data?.socialLinks || [];

  const isValidProfileUrl = (url: string, platform: string) => {
    const patterns: Record<string, RegExp> = {
      facebook:
        /^(https?:\/\/)?(www\.)?facebook\.com\/(@?[A-Za-z0-9._-]+)\/?$/i,
      instagram:
        /^(https?:\/\/)?(www\.)?instagram\.com\/(@?[A-Za-z0-9._-]+)\/?$/i,
      youtube: /^(https?:\/\/)?(www\.)?youtube\.com\/(@?[A-Za-z0-9._-]+)\/?$/i,
      twitter:
        /^(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/(@?[A-Za-z0-9._-]+)\/?$/i,
      tiktok: /^(https?:\/\/)?(www\.)?tiktok\.com\/(@?[A-Za-z0-9._-]+)\/?$/i,
    };
    return patterns[platform]?.test(url);
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    platform: string,
  ) => {
    const value = e.target.value.trim();
    setNewLink(value);
    setIsValid(value !== '' && isValidProfileUrl(value, platform));
  };

  const { mutate: addLink, isPending: addLinkLoading } = useMutation({
    mutationFn: (data: { platform: string; url: string }) =>
      userService.addArtistStepThree(data),
    onSuccess: () => {
      toast.success('Link added successfully.');
      queryClient.invalidateQueries({ queryKey: ['kyc-links'] });
      setEditIndex(null);
      setNewLink('');
    },
  });

  const { mutate: updateLink, isPending: updateLinkLoading } = useMutation({
    mutationFn: (data: { id: string; url: string; platform: string }) =>
      userService.updateKycSocialLink(data.id, {
        platform: data.platform,
        url: data.url,
      }),
    onSuccess: () => {
      toast.success('Link updated successfully.');
      queryClient.invalidateQueries({ queryKey: ['kyc-links'] });
      setEditIndex(null);
      setNewLink('');
    },
  });

  const { mutate: deleteLink } = useMutation({
    mutationFn: (id: string) => userService.deleteKycSocialLink(id),
    onSuccess: () => {
      toast.success('Link deleted successfully.');
      queryClient.invalidateQueries({ queryKey: ['kyc-links'] });
    },
  });

  const handleSave = (platform: string, existing: boolean, id?: string) => {
    if (!isValidProfileUrl(newLink, platform)) {
      toast.error(`Please enter a valid ${platform} profile link.`);
      return;
    }
    if (existing && id) {
      updateLink({ id, platform, url: newLink });
    } else {
      addLink({ platform, url: newLink });
    }
  };

  const completeMutation = useMutation({
    mutationFn: userService.completeKycStepThree,
    onSuccess: (data) => {
      toast.success(data?.data?.message);
      queryClient.invalidateQueries({ queryKey: ['get-progress'] });
      nextStep();
    },
  });

  const handleComplete = async () => {
    try {
      await completeMutation.mutateAsync();
    } catch (error) {
      console.error('Error', error);
    }
  };

  return (
    <div>
      <h3 className="text-center text-2xl sm:text-3xl">
        ADD YOUR SOCIAL LINKS
      </h3>
      <p className="mt-5 text-center text-base text-gray-600">
        Help people find you wherever you are. Connect your other accounts to
        show them on your page. We&apos;ll never post on your behalf.
      </p>
      <ul className="mt-8 space-y-4">
        {socialPlatforms.map((platform, index) => {
          const savedLink = links.find(
            (l: any) => l.platform === platform.name,
          );
          const savedId = savedLink?._id;

          return (
            <li
              key={platform.name}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <span className="h-5 w-5">{platform.icon}</span>
                <span className="capitalize">{platform.name}</span>
              </div>

              <div className="flex items-center gap-2">
                {editIndex === index ? (
                  <>
                    <Input
                      name="link"
                      value={newLink}
                      onChange={(e) => handleChange(e, platform.name)}
                      placeholder="Enter profile link..."
                      className={`!py-1 !text-base ${!isValid ? 'border-red-500' : ''}`}
                    />
                    <Button
                      onClick={() =>
                        handleSave(platform.name, !!savedLink, savedId)
                      }
                      isLoading={addLinkLoading || updateLinkLoading}
                      disabled={addLinkLoading || updateLinkLoading}
                      className="!rounded-md bg-primary !px-3 !py-1 !text-sm text-white"
                    >
                      Save
                    </Button>
                  </>
                ) : savedLink ? (
                  <>
                    <BiSolidEdit
                      onClick={() => {
                        setEditIndex(index);
                        setNewLink(savedLink.url);
                      }}
                      size={18}
                      className="cursor-pointer text-gray-600 hover:text-primary"
                    />
                    <MdDelete
                      onClick={() => deleteLink(savedId ?? '')}
                      className="h-5 w-5 cursor-pointer text-red-600"
                    />
                  </>
                ) : (
                  <button
                    onClick={() => {
                      setEditIndex(index);
                      setNewLink('');
                    }}
                    className="h-[27px] w-[89px] rounded-full border border-gray-600 text-xs font-semibold text-gray-600"
                  >
                    CONNECT
                  </button>
                )}
              </div>
            </li>
          );
        })}
      </ul>
      <Button
        className="mt-7 w-full !py-5 !font-bold uppercase"
        arrow={true}
        disabled={completeMutation.isPending || (links?.length ?? 0) < 3}
        isLoading={completeMutation.isPending}
        onClick={handleComplete}
        type="submit"
      >
        Save & Continue
      </Button>
    </div>
  );
};

export default KycSocialLinks;
