'use client';
import Button from '@/components/common/Button';
import NoCollection from '@/components/common/Icons/NoCollection';
import React, { useRef, useState } from 'react';
import { MdArrowOutward } from 'react-icons/md';
import { AiTwotoneAudio } from 'react-icons/ai';
import { RxCross1 } from 'react-icons/rx';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { userService } from '@/services/user.service';
import { useRouter } from 'next/navigation';

const AudioRecording = () => {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationIdRef = useRef<number | null>(null);

  const inputRef = useRef<HTMLInputElement | null>(null);

  const [files, setFiles] = useState<
    { file: File; id: string; progress: number; duration?: number }[]
  >([]);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(
    null,
  );
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [recordedAudioURL, setRecordedAudioURL] = useState<string | null>(null);

  const chunks = useRef<Blob[]>([]);
  const router = useRouter();
  const queryClient = useQueryClient();

  const MAX_AUDIO_SIZE = 20 * 1024 * 1024;

  const trackMutation = useMutation({
    mutationFn: (data: any) => userService.addArtistStepFour(data),
    onSuccess: (data) => {
      toast.success(data?.data?.message);
      submitStepsMutation.mutate();
      router.push('/artist/initial-dashboard?tab=Home');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message);
    },
  });

  const submitStepsMutation = useMutation({
    mutationFn: () => userService.submitArtistApplication(),
    onSuccess: (data) => {
      toast.success(data?.data?.message);
      // Invalidate both auth-user and current-view to refresh user data
      queryClient.invalidateQueries({ queryKey: ['auth-user'] });
      queryClient.invalidateQueries({ queryKey: ['current-view'] });

      // Show success message about application submission
      toast.info(
        'Your application has been submitted for review. You will be notified once approved.',
      );

      // Redirect to explore page
      router.push('/artist/initial-dashboard?tab=Home');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || files.length >= 1) {
      toast.error('You can only upload one audio file.');
      return;
    }

    const file = selectedFiles[0];
    if (file.size > MAX_AUDIO_SIZE) {
      toast.error(`${file.name} exceeds the 20MB limit.`);
      return;
    }

    const audio = document.createElement('audio');
    audio.src = URL.createObjectURL(file);
    audio.addEventListener('loadedmetadata', () => {
      const durationInMinutes = audio.duration / 60;
      const uniqueId = crypto.randomUUID();
      setFiles([
        {
          file,
          id: uniqueId,
          progress: 0,
          duration: durationInMinutes,
        },
      ]);
      simulateUploadProgress(0);
    });
  };

  const simulateUploadProgress = (index: number) => {
    let progress = 0;
    const interval = setInterval(() => {
      if (progress < 100) {
        progress += 5;
        setFiles((prevFiles) => {
          const updatedFiles = [...prevFiles];
          updatedFiles[index] = {
            ...updatedFiles[index],
            progress,
          };
          return updatedFiles;
        });
      } else {
        clearInterval(interval);
      }
    }, 100);
  };

  const handleStart = async () => {
    if (files.length > 0) {
      toast.error('Please remove uploaded file before recording.');
      return;
    }

    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream, { mimeType: 'audio/webm' });

    const audioContext = new AudioContext();
    const source = audioContext.createMediaStreamSource(stream);
    const analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048;

    analyserRef.current = analyser;
    source.connect(analyser);
    drawWaveform(analyser);

    chunks.current = [];

    recorder.ondataavailable = (e) => {
      if (e.data.size > 0) chunks.current.push(e.data);
    };

    recorder.onstop = () => {
      const blob = new Blob(chunks.current, { type: 'audio/webm' });
      setAudioBlob(blob);
      setRecordedAudioURL(URL.createObjectURL(blob));
    };

    recorder.start();
    setMediaRecorder(recorder);
    setIsRecording(true);
  };

  const handleStop = () => {
    if (mediaRecorder && mediaRecorder.state !== 'inactive') {
      mediaRecorder.stop();
      mediaRecorder.stream.getTracks().forEach((track) => track.stop());
    }
    setIsRecording(false);
  };

  const drawWaveform = (analyser: AnalyserNode) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const bufferLength = analyser.fftSize;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!ctx) return;

      analyser.getByteTimeDomainData(dataArray);
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.lineWidth = 2;
      ctx.strokeStyle = '#4F46E5';
      ctx.beginPath();

      const sliceWidth = (canvas.width * 1.0) / bufferLength;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        const v = dataArray[i] / 128.0;
        const y = (v * canvas.height) / 2;
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
        x += sliceWidth;
      }

      ctx.lineTo(canvas.width, canvas.height / 2);
      ctx.stroke();

      animationIdRef.current = requestAnimationFrame(draw);
    };

    draw();
  };

  const handleSubmit = async () => {
    if (!files.length && !audioBlob) {
      toast.error('Please upload or record at least one audio file.');
      return;
    }

    const formData = new FormData();
    files.forEach(({ file }) => formData.append('file', file));

    if (audioBlob) {
      const recordedFile = new File([audioBlob], 'recorded-audio.mp3', {
        type: 'audio/mpeg', // ensures compatibility
      });
      formData.append('file', recordedFile);
    }

    trackMutation.mutate(formData);
  };

  return (
    <div>
      <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
        {/* Upload Section */}
        <div className="rounded-[14px] border border-[#CECECE] p-5 sm:p-[30px]">
          {files.length > 0 &&
            files.map(({ file, duration }, index) => (
              <div
                key={index}
                className="mb-2 flex items-center justify-between gap-3 rounded-md border border-gray-400 p-1"
              >
                <p className="w-[80px] truncate text-sm text-[#333333]">
                  {file.name}
                </p>
                <RxCross1
                  className="cursor-pointer text-sm text-[#666666] hover:text-primary"
                  onClick={() =>
                    setFiles((prev) => prev.filter((_, i) => i !== index))
                  }
                />
                <p className="text-xs text-[#666666]">
                  {duration ? `${duration.toFixed(2)} min` : 'Loading...'}
                </p>
              </div>
            ))}

          <label className="flex w-full cursor-pointer flex-col items-center gap-2">
            <h3 className="text-center text-xs text-[#777777]">
              Upload Audio File
            </h3>
            <NoCollection />
            <p className="text-xs text-[#333333]">
              Use WAV, FLAC, AIFF, or ALAC.{' '}
              <strong>Max file size: 20MB.</strong>
            </p>
            <div onClick={() => inputRef.current?.click()}>
              <Button
                className="group text-xs font-semibold"
                disabled={files.length >= 1 || !!audioBlob}
              >
                CHOOSE FILE <MdArrowOutward className="text-sm text-white" />
              </Button>
            </div>
            <p className="mt-2 text-xs text-[#333333]">
              You can only upload <strong>one audio</strong>
            </p>
            <input
              type="file"
              accept="audio/*"
              ref={inputRef}
              onChange={handleFileChange}
              className="hidden"
              disabled={files.length >= 1 || !!audioBlob}
            />
          </label>
        </div>

        {/* Record Section */}
        <div className="flex flex-col items-center gap-2 rounded-[14px] border border-[#CECECE] p-5 sm:p-[30px]">
          <AiTwotoneAudio className="text-3xl text-[#333333]" />
          <p className="text-xl text-[#333333] sm:text-[22px]">
            Or record with a microphone
          </p>
          <p className="mb-2 text-sm text-[#666666]">
            You can only <strong>upload or record one audio</strong>
          </p>

          <canvas
            ref={canvasRef}
            className="h-[62px] w-full rounded bg-black"
            style={{ display: isRecording ? 'block' : 'none' }}
            width={600}
            height={54}
          />

          {recordedAudioURL && !isRecording && (
            <audio
              controls
              src={recordedAudioURL}
              className="mb-2 h-[54px] w-full"
            />
          )}

          {isRecording ? (
            <Button
              variant="outline"
              className="group h-[40px] text-xs font-semibold"
              onClick={handleStop}
            >
              <span className="h-[10px] w-[10px] bg-primary group-hover:bg-white"></span>{' '}
              STOP RECORDING
            </Button>
          ) : (
            <Button
              variant="outline"
              className="group h-[40px] text-xs font-semibold"
              onClick={handleStart}
              disabled={files.length > 0}
            >
              <span className="h-[10px] w-[10px] rounded-full bg-primary group-hover:bg-white"></span>{' '}
              START RECORDING
            </Button>
          )}
        </div>
      </div>

      <Button
        className="mt-4 w-full !font-bold sm:!h-[62px]"
        arrow
        onClick={handleSubmit}
        disabled={trackMutation.isPending || submitStepsMutation.isPending}
        isLoading={trackMutation.isPending || submitStepsMutation.isPending}
      >
        Submit
      </Button>
    </div>
  );
};

export default AudioRecording;
