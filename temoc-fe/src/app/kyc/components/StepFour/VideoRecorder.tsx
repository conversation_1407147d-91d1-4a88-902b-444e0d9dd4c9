'use client';
import React, { useEffect, useRef, useState } from 'react';
import Button from '@/components/common/Button';
import NoCollection from '@/components/common/Icons/NoCollection';
import { MdArrowOutward } from 'react-icons/md';
import { RxCross1 } from 'react-icons/rx';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { userService } from '@/services/user.service';
import { useRouter } from 'next/navigation';

const VideoRecording = () => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunks = useRef<Blob[]>([]);

  const [files, setFiles] = useState<
    { file: File; id: string; progress: number; url: string }[]
  >([]);
  const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
  const [recordedURL, setRecordedURL] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [seconds, setSeconds] = useState(0);

  const router = useRouter();
  const queryClient = useQueryClient();
  const MAX_FILE_SIZE = 100 * 1024 * 1024;

  const trackMutation = useMutation({
    mutationFn: (data: any) => userService.addArtistStepFour(data),
    onSuccess: (data) => {
      toast.success(data?.data?.message);
      submitStepsMutation.mutate();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message);
    },
  });

  const submitStepsMutation = useMutation({
    mutationFn: () => userService.submitArtistApplication(),
    onSuccess: (data) => {
      toast.success(data?.data?.message);
      // Invalidate both auth-user and current-view to refresh user data
      queryClient.invalidateQueries({ queryKey: ['auth-user'] });
      queryClient.invalidateQueries({ queryKey: ['current-view'] });

      // Show success message about application submission
      toast.info(
        'Your application has been submitted for review. You will be notified once approved.',
      );

      // Redirect to explore page
      router.push('/artist/initial-dashboard?tab=Home');
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message);
    },
  });

  useEffect(() => {
    if (isRecording) {
      const interval = setInterval(() => setSeconds((prev) => prev + 1), 1000);
      return () => clearInterval(interval);
    }
  }, [isRecording]);

  useEffect(() => {
    if (videoRef.current) {
      if (stream) {
        videoRef.current.srcObject = stream;
      } else if (recordedURL) {
        videoRef.current.srcObject = null;
        videoRef.current.src = recordedURL;
        videoRef.current.load();
      } else {
        videoRef.current.srcObject = null;
        videoRef.current.src = '';
      }
    }
  }, [stream, recordedURL]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selected = e.target.files;
    if (!selected) return;

    if (files.length >= 1 || recordedBlob) {
      toast.error('You can only upload OR record one video.');
      return;
    }

    const file = selected[0];
    if (file.size > MAX_FILE_SIZE) {
      toast.error(`${file.name} exceeds the 100MB limit.`);
      return;
    }

    const id = crypto.randomUUID();
    const url = URL.createObjectURL(file);
    setFiles([{ file, id, progress: 0, url }]);
    simulateUploadProgress(id);
  };

  const simulateUploadProgress = (id: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      if (progress >= 100) return clearInterval(interval);
      progress += 5;
      setFiles((prev) =>
        prev.map((f) => (f.id === id ? { ...f, progress } : f)),
      );
    }, 100);
  };

  const handleFileRemove = (id: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== id));
  };

  const startRecording = async () => {
    if (files.length > 0) {
      toast.error('Please remove uploaded video before recording.');
      return;
    }

    setRecordedBlob(null);
    setRecordedURL(null);

    const mediaStream = await navigator.mediaDevices.getUserMedia({
      video: true,
      audio: true,
    });

    setStream(mediaStream);
    recordedChunks.current = [];

    let mimeType = 'video/webm';
    if (MediaRecorder.isTypeSupported('video/webm; codecs=vp9'))
      mimeType = 'video/webm; codecs=vp9';
    else if (MediaRecorder.isTypeSupported('video/webm; codecs=vp8'))
      mimeType = 'video/webm; codecs=vp8';

    const recorder = new MediaRecorder(mediaStream, { mimeType });
    mediaRecorderRef.current = recorder;

    recorder.ondataavailable = (e) => {
      if (e.data.size > 0) recordedChunks.current.push(e.data);
    };

    recorder.onstop = () => {
      const blob = new Blob(recordedChunks.current, { type: mimeType });
      const url = URL.createObjectURL(blob);
      setRecordedBlob(blob);
      setRecordedURL(url);
      setStream(null);
    };

    recorder.start();
    setIsRecording(true);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    stream?.getTracks().forEach((t) => t.stop());
    setIsRecording(false);
  };

  const formatTime = (sec: number) =>
    `${Math.floor(sec / 60)
      .toString()
      .padStart(2, '0')}:${(sec % 60).toString().padStart(2, '0')}`;

  const handleSubmit = () => {
    if (!files.length && !recordedBlob) {
      toast.error('Please upload or record a video before submitting.');
      return;
    }

    const formData = new FormData();
    files.forEach(({ file }) => formData.append('file', file));

    if (recordedBlob) {
      const recordedFile = new File([recordedBlob], 'recorded-video.mp4', {
        type: 'video/mp4',
      });
      formData.append('file', recordedFile);
    }

    trackMutation.mutate(formData);
  };

  const progress = Math.min(seconds / 60, 1) * 100;

  return (
    <div>
      <div className="m-auto mt-5 max-w-[690px] space-y-4 bg-white p-5">
        {/* Upload Section */}
        <div className="rounded-[14px] border border-[#CECECE] p-5">
          {files.length > 0 && (
            <div className="mb-2 space-y-2">
              {files.map(({ id, progress, url }) => (
                <div
                  key={id}
                  className="flex items-center justify-between gap-3 rounded-md border border-gray-400 p-1"
                >
                  <video src={url} className="h-[50px] w-[80px]" controls />
                  <div className="mx-2 flex-1">
                    <div className="h-1 rounded-full bg-[#CECECE]">
                      <div
                        className="h-1 rounded-full bg-primary"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>
                  <RxCross1
                    className="cursor-pointer text-sm text-[#666666] hover:text-primary"
                    onClick={() => handleFileRemove(id)}
                  />
                </div>
              ))}
            </div>
          )}
          <label className="flex w-full cursor-pointer flex-col items-center gap-2">
            <h3 className="text-xs text-[#777777]">Upload Video File</h3>
            <NoCollection />
            <p className="text-xs text-[#333333]">
              Use MP4, WEBM, or MOV. <strong>Max size: 100MB</strong>
            </p>
            <div onClick={() => inputRef.current?.click()}>
              <Button
                className="group text-xs font-semibold"
                disabled={files.length >= 1 || !!recordedBlob}
              >
                CHOOSE FILE <MdArrowOutward className="text-sm text-white" />
              </Button>
            </div>
            <p className="mt-2 text-xs text-[#333333]">
              You can either upload or record <strong>one video</strong>
            </p>
            <input
              type="file"
              accept="video/*"
              ref={inputRef}
              onChange={handleFileChange}
              disabled={files.length >= 1 || !!recordedBlob}
              className="hidden"
            />
          </label>
        </div>

        {/* Record Section */}
        <div className="flex flex-col items-center gap-2 rounded-[14px] border border-[#CECECE] p-5">
          <p className="text-xl text-[#333333]">Or record a video</p>
          <video
            ref={videoRef}
            src={recordedURL || undefined}
            autoPlay={!!stream}
            muted={!!stream}
            controls={!stream}
            className="h-40 w-full rounded-md"
          />
          <div className="mb-1 h-1 w-full rounded-full bg-[#CECECE]">
            <div
              className="h-1 rounded-full bg-primary"
              style={{ width: `${progress}%`, transition: 'width 1s linear' }}
            />
          </div>
          <p className="w-full text-end text-xs text-[#333333]">
            {formatTime(seconds)}
          </p>

          {isRecording ? (
            <Button
              variant="outline"
              className="group h-[40px] text-xs font-semibold"
              onClick={stopRecording}
            >
              <span className="h-[10px] w-[10px] bg-primary group-hover:bg-white" />{' '}
              STOP RECORDING
            </Button>
          ) : (
            <Button
              variant="outline"
              className="group h-[40px] text-xs font-semibold"
              onClick={startRecording}
              disabled={files.length > 0}
            >
              <span className="h-[10px] w-[10px] rounded-full bg-primary group-hover:bg-white" />{' '}
              START RECORDING
            </Button>
          )}

          {files.length > 0 && (
            <p className="mt-2 text-xs text-red-500">
              Remove uploaded video to enable recording.
            </p>
          )}
        </div>
      </div>

      <Button
        className="mt-4 w-full !font-bold sm:!h-[62px]"
        arrow
        onClick={handleSubmit}
        disabled={trackMutation.isPending || submitStepsMutation.isPending}
        isLoading={trackMutation.isPending || submitStepsMutation.isPending}
      >
        Submit
      </Button>
    </div>
  );
};

export default VideoRecording;
