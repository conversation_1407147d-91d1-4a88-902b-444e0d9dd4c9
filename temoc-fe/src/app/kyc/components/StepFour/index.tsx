import React, { useState } from 'react';
import BackButton from '../BackButton';
import { IStepProps } from '../StepTwo';
// import { AiTwotoneAudio } from 'react-icons/ai';
import SelectComponent from '@/components/common/Forms/Select';
import VideoRecorder from './VideoRecorder';
import AudioRecording from './AudioRecording';

const Types = [
  { value: 'Audio', label: 'Audio' },
  { value: 'Video', label: 'Video' },
];

const StepFour = ({ prevStep }: IStepProps) => {
  const [selectType, setSelectType] = useState<{
    value: string;
    label: string;
  } | null>(Types[0]);

  return (
    <div className="mx-auto flex w-full flex-col pt-7 lg:w-[468px]">
      <BackButton onClick={prevStep} />
      <h1 className="text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        Verify Your Identity
      </h1>
      <div className="mt-7">
        <SelectComponent
          options={Types}
          selected={selectType}
          onSelect={setSelectType}
          placeholder={
            <div className="flex items-center gap-1">Select Type</div>
          }
        />
      </div>
      {selectType?.value == 'Video' ? <VideoRecorder /> : <AudioRecording />}
    </div>
  );
};

export default StepFour;
