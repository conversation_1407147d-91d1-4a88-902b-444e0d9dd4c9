'use client';
import { yupResolver } from '@hookform/resolvers/yup';
import { NameSchema } from '@/utils/schema';
import { Input } from '@/components/common/Forms/Input';
import InputError from '@/components/common/Forms/InputError';
import { Button } from '@/components/common';
import { SubmitHandler, useForm } from 'react-hook-form';
import React, { useEffect, useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { userService } from '@/services/user.service';

interface IFormData {
  firstName: string;
  lastName: string;
}

const StepOne = ({ nextStep }: any) => {
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<IFormData>({
    resolver: yupResolver(NameSchema),
  });
  const [loading, setLoading] = useState(true);

  const { user } = useAuth();
  const currentStep = user?.currentArtistFormStep;
  const queryClient = useQueryClient();

  const StepOneMutation = useMutation({
    mutationFn: userService.addArtistStepOne,
    onSuccess: () => {},
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });

  useEffect(() => {
    if (currentStep !== undefined) {
      setLoading(false);
    }
  }, [currentStep]);

  useEffect(() => {
    if (user) {
      const initialValues = {
        firstName: user.firstName || '',
        lastName: user.lastName || '',
      };
      setValue('firstName', initialValues.firstName);
      setValue('lastName', initialValues.lastName);
    }
  }, [user, setValue]);

  const onSubmit: SubmitHandler<IFormData> = async (data) => {
    try {
      await StepOneMutation.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ['get-progress'] });
      nextStep();
    } catch (error) {
      console.error('Error during form submission', error);
    }
  };
  return (
    <div className="flex w-full flex-col lg:w-[468px]">
      <h1 className="text-center text-2xl font-semibold uppercase text-black md:text-3xl xs:text-xl">
        SELECT YOUR NAME
      </h1>
      <p className="mt-5 text-center text-base xs:text-sm">
        What should we call you?
      </p>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-7 w-full">
          <Input
            placeholder="First Name"
            name="firstName"
            register={register}
          />
          <InputError error={errors.firstName?.message} />
        </div>
        <div className="mt-7 w-full">
          <Input placeholder="Last Name" name="lastName" register={register} />
          <InputError error={errors.lastName?.message} />
        </div>
        <Button
          className="mt-7 w-full !py-5 !font-bold uppercase"
          arrow={true}
          type="submit"
          isLoading={StepOneMutation.isPending}
          disabled={StepOneMutation.isPending || currentStep == 5 || loading}
        >
          Save & Continue
        </Button>
      </form>
    </div>
  );
};

export default StepOne;
