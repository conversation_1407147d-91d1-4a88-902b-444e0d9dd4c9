import { CheckBox } from '@/components/common/Icons';
import ImageComponent from '@/components/common/ImageComponent';

export default function Stepper({ currentStep }: { currentStep: number }) {
  return (
    <div className="flex w-full flex-col">
      <ImageComponent
        src="/assets/images/logo.svg"
        fill
        figClassName="w-[203px] h-[71px] mx-auto"
        className="object-contain"
        alt=""
      />
      <div className="mt-12 flex justify-center">
        {[1, 2, 3, 4].map((step) => (
          <div key={step} className="flex items-center">
            {step < currentStep ? (
              <CheckBox />
            ) : (
              <div
                className={`relative flex h-10 w-10 items-center justify-center rounded-full font-bold text-white ${step <= currentStep ? 'bg-orange-500' : 'bg-gray-300'}`}
              >
                {step}
              </div>
            )}

            {step < 4 && (
              <div
                className={`h-1 w-16 ${step <= currentStep ? 'bg-orange-500' : 'bg-gray-300'}`}
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
