'use client';
import React, { useEffect, useState } from 'react';
import Slider from './components/AuthSlider';
import Stepper from './components/Stepper';
import StepOne from './components/StepOne';
import StepTwo from './components/StepTwo';
import StepThree from './components/StepThree';
import StepFour from './components/StepFour';
import { ArtistProgressRes } from '@/types/user.interface';
import { userService } from '@/services/user.service';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

export interface FormDataProps {
  firstName?: string;
  lastName?: string;
  userName?: string;
  password?: string;
  confirmPassword?: string;
}

const Kyc = () => {
  const { user } = useAuth();
  const { data } = useQuery<ArtistProgressRes>({
    queryKey: ['get-progress'],
    queryFn: () => userService.getArtistApplicationProgress(),
    enabled: !!user?._id,
  });

  const router = useRouter();
  const currentStep = user?.currentArtistFormStep;
  const ArtistProgress = data?.data?.progress;

  const [step, setStep] = useState(1);
  useEffect(() => {
    if (currentStep == 5) {
      router.push('/app/explore');
      return;
    }
    if (ArtistProgress?.currentStep) {
      setStep(ArtistProgress.currentStep);
    }
  }, [currentStep, ArtistProgress, router]);

  const nextStep = () => setStep(step + 1);
  const prevStep = () => setStep(step - 1);

  return (
    <div className="flex w-full flex-col xl:flex-row">
      <div className="hideScrollbar mx-auto flex h-screen w-full flex-col items-center gap-12 overflow-auto bg-white px-5 pb-5 pt-10 sm:w-[70%] lg:w-[60%] lg:px-20">
        <Stepper currentStep={step} />
        {(step === 1 || step === 5) && <StepOne nextStep={nextStep} />}
        {step === 2 && <StepTwo nextStep={nextStep} prevStep={prevStep} />}
        {step === 3 && <StepThree nextStep={nextStep} prevStep={prevStep} />}
        {step === 4 && <StepFour nextStep={nextStep} prevStep={prevStep} />}
        <div className="flex flex-col justify-center gap-10">
          <p className="text-base font-normal text-[#ADADAD] xs:text-sm">
            © 2025 TEMOC • All Rights Reserved
          </p>
        </div>
      </div>
      <div className="relative mt-10 hidden w-full lg:mt-0 lg:w-[40%] xl:block">
        <Slider />
      </div>
    </div>
  );
};

export default Kyc;
