'use client';
import React from 'react';
import 'aos/dist/aos.css';
import './styles/globals.css';
import localFont from 'next/font/local';
import 'react-toastify/dist/ReactToastify.css';
import AppProvider from '@/providers/AppProvider';
import { ToastContainer } from 'react-toastify';
import { cn } from '@/lib/utils';
import { DM_Sans } from 'next/font/google';
import 'react-loading-skeleton/dist/skeleton.css';

const DMSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-dmsans',
});

const DreamAvenue = localFont({
  src: [
    {
      path: '../../public/assets/fonts/Dream.woff2',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-dreamavenue',
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        suppressHydrationWarning={true}
        className={cn(
          'bg-whitepco min-h-screen font-display antialiased',
          DMSans.variable,
          DreamAvenue.variable,
        )}
      >
        <AppProvider>
          <ToastContainer position="top-center" />
          <div className="sm:overflow-x-hidden">{children}</div>
        </AppProvider>
      </body>
    </html>
  );
}
