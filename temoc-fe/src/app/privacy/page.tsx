'use client';
import { Container } from '@/components/common';
import { Footer } from '@/components/ui';
import Header from '@/components/ui/Header';
import React, { useEffect, useRef, useState } from 'react';

const Index = () => {
  const [ActiveTab, setActiveTab] = useState(0);
  const mainRef = useRef(null);
  const aboutRef = useRef(null);
  // const buyRef = useRef(null);
  const exploreRef = useRef(null);
  // const tokenomicsRef = useRef(null);
  // const roadmapRef = useRef(null);
  const Navigation = [
    {
      name: 'Home',
      href: `/`,
      id: 'main',
      ref: mainRef,
    },
    {
      name: 'Discover',
      href: `/#discover`,
      id: 'about',
      ref: aboutRef,
    },
    {
      name: 'About',
      href: `/#about`,
      id: 'explore',
      ref: exploreRef,
    },
  ];

  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5, // Adjust this value as needed
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = Navigation.findIndex(
            (nav) => nav.ref.current === entry.target,
          );
          if (index !== -1) {
            setActiveTab(index);
          }
        }
      });
    }, options);

    // Observe each section
    Navigation.forEach((nav) => {
      if (nav.ref.current) {
        observer.observe(nav.ref.current);
      }
    });

    return () => {
      // Cleanup the observer on unmount
      observer.disconnect();
    };
  }, []);

  return (
    <>
      <meta name="robots" content="noindex" />
      <meta name="title" content="Privacy Policy | TEMOC" />
      <title>Privacy Policy | TEMOC</title>
      <meta
        name="description"
        content="Read the official TEMOC Privacy Policy to understand how we collect, use, and protect your personal information"
      />
      <meta
        name="keywords"
        content="musician tokenization, support independent musicians, exclusive fan content, blockchain music platform, creator tokens, invest in musicians, fan engagement, TEMOC token, music tokenization, digital art tokens, web3 musicians, musician funding, Crypto music Tokens, Crypto Tokens"
      />
      <link rel="canonical" href="https://www.temoc.io/privacy/" />
      <meta property="og:locale" content="en_US" />
      <meta property="og:title" content="Privacy Policy | TEMOC" />
      <meta
        property="og:description"
        content="Read the official TEMOC Privacy Policy to understand how we collect, use, and protect your personal information"
      />
      <meta property="og:type" content="website" />
      <meta property="og:url" content="https://www.temoc.io/privacy/" />
      <meta
        property="og:image"
        //   content="https://www.temoc.io/privacy/assets/images/home-creasoft.png"
      />
      <meta name="twitter:url" content="https://www.temoc.io/privacy/" />
      <meta name="twitter:title" content="Privacy Policy | TEMOC" />
      <meta
        name="twitter:description"
        content="Read the official TEMOC Privacy Policy to understand how we collect, use, and protect your personal information "
      />
      {/* <meta
    name="twitter:image"
    content="https://www.temoc.io/privacy/assets/images/home-creasoft.png"
  /> */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:image:alt" content="TEMOC" />

      <div className="py-12 sm:py-16 xl:py-[100px]" id="privacy">
        <Header activeTab={ActiveTab} setActiveTab={setActiveTab} />
        <Container className="space-y-3 lg:px-[53px] xs:!pl-7">
          <h1 className="text-3xl sm:text-[72px]">Privacy Policy</h1>
          <h3 className="mt-[30px] text-base font-bold text-[#212428] sm:text-xl">
            Agreement
          </h3>
          <h3 className="mt-5 text-xl text-[#212428]">
            Last Updated: April 04,2025
          </h3>
          <p className="mt-2 text-base sm:text-xl">
            At TEMOC, we are committed to protecting your privacy and ensuring
            transparency in how your information is collected and used. This
            Privacy Policy outlines how we handle your personal data when you
            visit our platform, create an account, interact with musicians, or
            purchase musician tokens.
          </p>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            1. Information We Collect
          </h3>
          <p className="text-xs sm:text-lg">
            <span className="font-semibold">Personal Information:</span> Name,
            email address, wallet address, and any other details provided during
            registration.
          </p>
          <p className="text-xs sm:text-lg">
            <span className="font-semibold"> Usage Data:</span> Pages visited,
            content accessed, IP address, browser type, device information, and
            time spent on the site.
          </p>
          <p className="text-xs sm:text-lg">
            <span className="font-semibold"> Transaction Data:</span> Pages
            visited, Token purchases, interactions with musicians, and other
            blockchain activity.
          </p>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            2. How We Use Your Information
          </h3>

          <ul className="list-disc text-xs sm:text-lg">
            <li>To provide and maintain the TEMOC platform.</li>
            <li>To facilitate musician-fan interactions and transactions.</li>
            <li>
              To send updates, newsletters, and promotional content (you can
              opt-out anytime).
            </li>
            <li>To improve user experience and platform features.</li>
            <li>To ensure compliance with legal and regulatory obligations.</li>
          </ul>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            3. Sharing Your Information
          </h3>

          <ul className="list-disc text-xs sm:text-lg">
            <li>
              Service providers who help operate the platform (e.g., hosting,
              analytics).
            </li>
            <li>
              Legal authorities, when required to comply with the law or protect
              our rights.
            </li>
            <li>With your explicit consent, when necessary.</li>
          </ul>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            4. Security
          </h3>
          <p className="text-xs sm:text-lg">
            We implement industry-standard security measures to protect your
            data. However, no system is entirely secure. We encourage users to
            protect their wallets and personal information.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            5. Your Rights
          </h3>
          <ul className="list-disc text-xs sm:text-lg">
            <li>Access the data we hold about you.</li>
            <li>Request correction or deletion of your information.</li>
            <li>With your explicit consent, when necessary.</li>
          </ul>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            {' '}
            6. Cookies
          </h3>
          <p className="text-xs sm:text-lg">
            TEMOC uses cookies to enhance your browsing experience. You can
            modify your browser settings to reject cookies, but some features
            may not function properly.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            7. Changes to This Policy
          </h3>
          <p className="text-xs sm:text-lg">
            We may update this Privacy Policy occasionally. The revised version
            will be posted on this page with a new effective date.
          </p>
        </Container>
      </div>
      <Footer />
    </>
  );
};

export default Index;
