@layer base {
  :root {
    --fluid-min-width: 320;
    --fluid-max-width: 1440;
    --fluid-screen: 100vw;
    --fluid-bp: calc(
      (var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) /
        (var(--fluid-max-width) - var(--fluid-min-width))
    );
  }
  @media screen and (min-width: 1440px) {
    :root {
      --fluid-screen: calc(var(--fluid-max-width) * 1px);
    }
  }
  :root {
    --f-0-min: 14;
    --f-0-max: 22;
    --step-0: calc(
      ((var(--f-0-min) / 16) * 1rem) + (var(--f-0-max) - var(--f-0-min)) *
        var(--fluid-bp)
    );

    --f-1-min: 16;
    --f-1-max: 26;
    --step-1: calc(
      ((var(--f-1-min) / 16) * 1rem) + (var(--f-1-max) - var(--f-1-min)) *
        var(--fluid-bp)
    );

    --f-2-min: 18;
    --f-2-max: 32;
    --step-2: calc(
      ((var(--f-2-min) / 16) * 1rem) + (var(--f-2-max) - var(--f-2-min)) *
        var(--fluid-bp)
    );

    --f-3-min: 22;
    --f-3-max: 38;
    --step-3: calc(
      ((var(--f-3-min) / 16) * 1rem) + (var(--f-3-max) - var(--f-3-min)) *
        var(--fluid-bp)
    );

    --f-4-min: 26;
    --f-4-max: 48;
    --step-4: calc(
      ((var(--f-4-min) / 16) * 1rem) + (var(--f-4-max) - var(--f-4-min)) *
        var(--fluid-bp)
    );

    --f-5-min: 30;
    --f-5-max: 60;
    --step-5: calc(
      ((var(--f-5-min) / 16) * 1rem) + (var(--f-5-max) - var(--f-5-min)) *
        var(--fluid-bp)
    );
  }
  .h1 {
    font-size: var(--step-5);
    @apply font-bold;
  }
  .h2 {
    font-size: var(--step-4);
    @apply font-bold;
  }
  .h3 {
    font-size: var(--step-3);
    @apply font-bold;
  }
  .h4 {
    font-size: var(--step-2);
  }
  .h5 {
    font-size: var(--step-1);
  }
  .h6 {
    font-size: var(--step-0);
  }
}
