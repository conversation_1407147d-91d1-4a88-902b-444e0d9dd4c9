'use client';
import { Container } from '@/components/common';
import { Footer } from '@/components/ui';
import Header from '@/components/ui/Header';
import React, { useEffect, useRef, useState } from 'react';

const Index = () => {
  const [activeTab, setActiveTab] = useState(0);
  const mainRef = useRef(null);
  const aboutRef = useRef(null);
  const exploreRef = useRef(null);

  useEffect(() => {
    const navItems = [
      {
        name: 'Home',
        href: `/`,
        id: 'main',
        ref: mainRef,
      },
      {
        name: 'Discover',
        href: `/#discover`,
        id: 'about',
        ref: aboutRef,
      },
      {
        name: 'About',
        href: `/#about`,
        id: 'explore',
        ref: exploreRef,
      },
    ];

    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5,
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = navItems.findIndex(
            (nav) => nav.ref.current === entry.target,
          );
          if (index !== -1) {
            setActiveTab(index);
          }
        }
      });
    }, options);

    navItems.forEach((nav) => {
      if (nav.ref.current) {
        observer.observe(nav.ref.current);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <>
      <meta name="robots" content="noindex" />
      <meta name="title" content="Terms &amp; Conditions | TEMOC" />
      <title>Terms &amp; Conditions | TEMOC</title>
      <meta
        name="description"
        content="Review the TEMOC Terms &amp; Conditions outlining the rules, guidelines, and your agreement for using the TEMOC platform."
      />
      <meta
        name="keywords"
        content="musician tokenization, support independent musicians, exclusive fan content, blockchain music platform, creator tokens, invest in musicians, fan engagement, TEMOC token, music tokenization, digital art tokens, web3 musicians, musician funding, Crypto music Tokens, Crypto Tokens"
      />
      <link rel="canonical" href="https://www.temoc.io/terms-and-condition" />
      <meta property="og:locale" content="en_US" />
      <meta property="og:title" content="Terms &amp; Conditions | TEMOC" />
      <meta
        property="og:description"
        content="Review the TEMOC Terms &amp; Conditions outlining the rules, guidelines, and your agreement for using the TEMOC platform."
      />
      <meta property="og:type" content="website" />
      <meta
        property="og:url"
        content="https://www.temoc.io/terms-and-condition"
      />
      <meta
        property="og:image"
        content="https://www.temoc.io/assets/images/link-preview.png"
      />
      <meta
        name="twitter:url"
        content="https://www.temoc.io/terms-and-condition"
      />
      <meta name="twitter:title" content="Terms &amp; Conditions | TEMOC" />
      <meta
        name="twitter:description"
        content="Review the TEMOC Terms &amp; Conditions outlining the rules, guidelines, and your agreement for using the TEMOC platform."
      />
      <meta
        name="twitter:image"
        content="https://www.temoc.io/assets/images/link-preview.png"
      />
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:image:alt" content="TEMOC" />

      <div className="py-12 sm:py-16 xl:py-[100px]" id="terms-and-conditions">
        <Header activeTab={activeTab} setActiveTab={setActiveTab} />
        <Container className="space-y-3 lg:px-[53px] xs:!pl-7">
          <h1 className="text-3xl sm:text-[72px]">Terms &amp; Conditions</h1>
          <h3 className="mt-[30px] text-base font-bold text-[#212428] sm:text-xl">
            Agreement
          </h3>
          <h3 className="mt-5 text-xl text-[#212428]">
            Last Updated: April 04, 2025
          </h3>
          <p className="mt-2 text-base sm:text-xl">
            Welcome to TEMOC! These Terms &amp; Conditions govern your use of
            our platform and services. By accessing or using TEMOC, you agree to
            these Terms. If you do not agree, please do not use the platform.
          </p>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            1. Platform Overview
          </h3>
          <p className="text-xs sm:text-lg">
            TEMOC is a decentralized platform that allows independent musicians
            to tokenize their brand and enables fans to purchase musician
            tokens, gain exclusive content access, and participate in the
            musician&apos;s journey.
          </p>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            2. User Eligibility
          </h3>
          <p className="text-xs sm:text-lg">
            Users must be at least 18 years old or the legal age in their
            jurisdiction to use this platform.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            3. Account Registration
          </h3>
          <p className="text-xs sm:text-lg">
            You must create an account and provide accurate, complete
            information. You are responsible for securing your account and
            wallet. TEMOC is not liable for loss resulting from unauthorized
            account access.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            4. Musician Tokens
          </h3>
          <ul className="list-disc text-xs sm:text-lg">
            <li>Tokens are created and managed by musicians.</li>
            <li>
              Fans purchasing tokens receive access to exclusive content and
              experiences.
            </li>
            <li>
              Tokens do not represent equity or ownership in the musician&apos;s
              business or the platform.
            </li>
            <li>
              Token value may fluctuate based on demand and musician growth;
              there is no guarantee of value increase.
            </li>
          </ul>

          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            5. Content and Ownership
          </h3>
          <ul className="list-disc text-xs sm:text-lg">
            <li>Musicians retain full ownership of their work.</li>
            <li>
              Fans are granted access rights but not ownership of the content.
            </li>
            <li>All platform content is protected under copyright law.</li>
          </ul>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            6. Prohibited Conduct
          </h3>
          <ul className="list-disc text-xs sm:text-lg">
            <li>Use the platform for illegal or fraudulent purposes.</li>
            <li>Violate intellectual property rights.</li>
            <li>Upload harmful or offensive content.</li>
            <li>Attempt to breach platform security.</li>
          </ul>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            7. Disclaimers
          </h3>
          <ul className="list-disc text-xs sm:text-lg">
            <li>
              TEMOC provides a marketplace for musician-fan interaction but does
              not guarantee token value or musician success.
            </li>
            <li>
              All token purchases are at the user&apos;s risk. Do your own
              research (DYOR) before investing.
            </li>
          </ul>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            8. Limitation of Liability
          </h3>
          <p className="text-xs sm:text-lg">
            TEMOC is not liable for any loss or damage arising from your use of
            the platform, including but not limited to token value fluctuation,
            unauthorized access, or service interruptions.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            9. Termination
          </h3>
          <p className="text-xs sm:text-lg">
            TEMOC reserves the right to suspend or terminate any account that
            violates these Terms or is involved in harmful activity.
          </p>
          <h3 className="mt-3 text-base font-semibold text-[#212428] sm:text-xl">
            10. Governing Law
          </h3>
          <p className="text-xs sm:text-lg">
            These Terms are governed by the laws of [Insert Jurisdiction].
            Disputes arising from these Terms shall be resolved through
            arbitration or courts in the same jurisdiction.
          </p>
        </Container>
      </div>
      <Footer />
    </>
  );
};

export default Index;
