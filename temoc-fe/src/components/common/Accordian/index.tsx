import React from 'react';
import { Disclosure } from '@headlessui/react';
import { IoIosArrowDown } from 'react-icons/io';

interface Iprops {
  children: React.ReactNode;
  title: string;
}

const Accordion = ({ children, title }: Iprops) => {
  return (
    <Disclosure
      as="div"
      defaultOpen={true}
      key=""
      className="mb-3 overflow-hidden rounded-lg border-2"
    >
      {({ open }) => (
        <>
          <Disclosure.Button className="flex w-full items-center justify-between bg-gray-200 p-4">
            <p className="text-lg text-black">{title}</p>
            <IoIosArrowDown
              className={`${
                open ? 'rotate-180 transform' : ''
              } h-6 w-6 text-black`}
            />
          </Disclosure.Button>
          <Disclosure.Panel as="div" className="p-4">
            {children}
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
};

export default Accordion;
