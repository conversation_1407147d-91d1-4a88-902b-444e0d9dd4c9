import { useRef, useState, useEffect } from 'react';
import { Play, Pause, Download } from 'lucide-react';

interface AudioPlayerProps {
  src: string;
}

export default function AudioPlayer({ src }: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      setProgress(audio.currentTime);
      setDuration(audio.duration || 0);
    };

    audio.addEventListener('timeupdate', updateProgress);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
    };
  }, []);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (audio.paused) {
      audio.play();
      setIsPlaying(true);
    } else {
      audio.pause();
      setIsPlaying(false);
    }
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) return '00:00';
    const mins = Math.floor(time / 60)
      .toString()
      .padStart(2, '0');
    const secs = Math.floor(time % 60)
      .toString()
      .padStart(2, '0');
    return `${mins}:${secs}`;
  };

  return (
    <div className="flex w-full max-w-md items-center space-x-4 rounded-lg border bg-white p-4 shadow-sm">
      <audio ref={audioRef} src={src} preload="metadata" />

      <button onClick={togglePlay} className="rounded-full bg-gray-100 p-2">
        {isPlaying ? <Pause size={20} /> : <Play size={20} />}
      </button>

      <div className="relative h-1 flex-grow overflow-hidden rounded bg-gray-300">
        <div
          className="absolute h-full bg-blue-500"
          style={{ width: `${(progress / duration) * 100 || 0}%` }}
        />
      </div>

      <span className="w-12 text-right font-mono text-sm">
        {formatTime(progress)}
      </span>

      <a href={src} download className="rounded-full bg-gray-100 p-2">
        <Download size={20} />
      </a>
    </div>
  );
}
