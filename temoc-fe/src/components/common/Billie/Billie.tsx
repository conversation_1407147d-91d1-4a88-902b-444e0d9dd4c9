import React from 'react';
import ImageComponent from '../ImageComponent';
import Insta from '../Icons/Insta';
import Youtube from '../Icons/Youtube';
import Spotify from '../Icons/Spotify';
import Facebook from '../Icons/Facebook';
import X from '../Icons/X';
import TikTok from '../Icons/TikTok';
import Tick from '../Icons/Tick';
import Button from '../Button';

const list = [
  {
    list: 'Unlock premium music, exclusive videos & backstage moments',
  },
  {
    list: 'Be first in line for live event invites and drops',
  },
  {
    list: 'Earn special rewards as a founding supporter',
  },
  {
    list: 'This is your chance to be part of something from Day 1.',
  },
];

const Billie = () => {
  return (
    <div className="flex flex-col items-center px-2">
      <ImageComponent
        src="/assets/images/billie.svg"
        height={160}
        width={160}
      />
      <p className="text-xl font-normal"><PERSON></p>
      <div className="mt-2.5 flex gap-1.5">
        <Insta height="24px" width="24px" />
        <Youtube height="24px" width="24px" />
        <Spotify height="24px" width="24px" />
        <Facebook height="24px" width="24px" />
        <X height="24px" width="24px" />
        <TikTok height="24px" width="24px" />
      </div>
      <p className="mt-5 text-center text-xs font-medium text-[#666666]">
        Billie is launching something big &amp; you&rsquo;re invited to be a
        founding supporter. This content is token-gated and will be unlocked
        once the fan-powered presale hits its goal or time runs out.
      </p>
      <h3 className="mt-[30px] w-full text-base font-medium text-[#333333]">
        Hold Billie Coin early to:
      </h3>
      <ul className="mt-5 space-y-2">
        {list.map((items, index) => {
          return (
            <li
              key={index}
              className="flex items-center gap-1.5 text-xs font-normal sm:text-sm"
            >
              <Tick />
              {items.list}
            </li>
          );
        })}
      </ul>
      <Button className="mt-[30px] !h-[42px] !w-[173px] uppercase">
        Join presale
      </Button>
    </div>
  );
};

export default Billie;
