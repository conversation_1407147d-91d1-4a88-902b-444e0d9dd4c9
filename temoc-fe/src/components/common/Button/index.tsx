import clsx from 'clsx';
import { ImSpinner9 } from 'react-icons/im';
import {
  BsProps,
  IButtonProps,
  SsProps,
  VsProps,
} from '@/interfaces/button.interface';
import Arrow from '../Icons/Arrow';

const baseStyles: BsProps = {
  solid:
    'inline-flex justify-center items-center rounded-full border-2 whitespace-nowrap leading-none font-medium disabled:cursor-not-allowed disabled:text-black',
  outline:
    'inline-flex justify-center items-center outline-none rounded-full whitespace-nowrap leading-none font-medium border-2 disabled:cursor-not-allowed disabled:text-black',
};

const variantStyles: VsProps = {
  solid: {
    primary:
      'bg-primary text-white active:text-[#010F1C]/80 border-primary hover:border-primary disabled:opacity-30 disabled:hover:bg-primary disabled:text-black hover:bg-primary  hover:text-white',
  },
  outline: {
    primary:
      'border-primary hover:text-black text-primary hover:text-white hover:bg-primary active:border-primary/20 active:bg-primary/10 active:text-primary/70 disabled:text-black disabled:opacity-40 disabled:hover:border-primary disabled:hover:bg-transparent',
  },
};

const sizeStyles: SsProps = {
  sm: 'px-2.5 py-1 text-xs',
  md: 'px-5 py-3.5 sm:text-base xs:text-xs',
  lg: 'px-7 py-2 text-sm xs:text-xs',
};
const Button = ({
  variant = 'solid',
  color = 'primary',
  size = 'md',
  className,
  type,
  disabled,
  isLoading,
  children,
  loaderClass,
  arrow,
  ...props
}: IButtonProps) => {
  return (
    <button
      className={clsx(
        baseStyles[variant],
        variantStyles[variant][color],
        sizeStyles[size],
        className,
        'group flex items-center justify-center gap-1.5 transition-all duration-200 ease-out',
        isLoading &&
          'relative !cursor-wait !text-transparent hover:!text-transparent',
      )}
      type={type || 'button'}
      disabled={disabled}
      {...props}
    >
      {isLoading && (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 capitalize text-black">
          <ImSpinner9 className={clsx('animate-spin text-sm', loaderClass)} />
        </div>
      )}
      {children}
      {arrow && !isLoading && (
        <div className="inline-block transition-all duration-200 ease-out group-hover:ml-2">
          <Arrow />
        </div>
      )}
    </button>
  );
};
export default Button;
