'use client';

import React, { useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/swiper-bundle.css';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';
import CardSkeleton from '@/components/ui/Skelton/CardSkelton';
import { IoIosArrowForward, IoIosArrowBack } from 'react-icons/io';

interface CommonSliderProps {
  children: React.ReactNode[];
  delay?: any;
}

const CommonSlider: React.FC<CommonSliderProps> = ({ children, delay }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setMounted(true);
    }, 300); // slight delay for smoother transition

    return () => clearTimeout(timeout);
  }, []);

  if (!mounted) {
    return (
      <div className="hideScrollbar flex gap-3 overflow-auto px-2">
        {[...Array(7)].map((_, index) => (
          <CardSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <div>
      <Swiper
        modules={[Pagination, Autoplay, Navigation]}
        style={{ position: 'unset' }}
        className="AtSocialSlider1 AtSocialSlider2"
        autoplay={{
          delay: delay ? 3000 : 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        // pagination={{
        //   clickable: true,
        //   type: 'bullets',
        //   renderBullet(index: any, className: string) {
        //     return '<span class="' + className + '"></span>';
        //   },
        // }}
        navigation={{
          nextEl: '.custom-swiper-button-next',
          prevEl: '.custom-swiper-button-prev',
        }}
        spaceBetween={20}
        loop={true}
        direction="horizontal"
        breakpoints={{
          320: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          600: {
            slidesPerView: 2.5,
            spaceBetween: 10,
          },
          760: {
            slidesPerView: 3,
            spaceBetween: 15,
          },
          1100: {
            slidesPerView: 4,
            spaceBetween: 20,
          },
          1400: {
            slidesPerView: 5,
            spaceBetween: 20,
          },

          1600: {
            slidesPerView: 6,
            spaceBetween: 20,
          },
        }}
      >
        <div className="">
          {children.map((child, index) => (
            <SwiperSlide key={index}>{child}</SwiperSlide>
          ))}
        </div>
      </Swiper>
      {children.length > 2 && (
        <div className="mt-6 flex justify-center gap-4 sm:hidden md:mt-8 lg:mt-12">
          <button className="custom-swiper-button-prev flex h-8 w-8 items-center justify-center rounded-full border text-black">
            <IoIosArrowBack size={20} />
          </button>
          <button className="custom-swiper-button-next flex h-8 w-8 items-center justify-center rounded-full border text-black">
            <IoIosArrowForward size={20} />
          </button>
        </div>
      )}
    </div>
  );
};

export default CommonSlider;
