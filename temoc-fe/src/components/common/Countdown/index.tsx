'use client';
import React from 'react';
import Countdown, { CountdownRenderProps } from 'react-countdown';

interface CountdownProps {
  date: Date;
}

const CountdownComponent: React.FC<CountdownProps> = ({ date }) => {
  const addLeadingZeros = (value: number) => {
    if (value.toString().length < 2) {
      return `0${value}`;
    }
    return value.toString();
  };

  const renderer = ({
    days,
    hours,
    minutes,
    seconds,
  }: CountdownRenderProps) => {
    'flex flex-col items-center bg-white bd justify-center h-[72px] xs:w-full w-[76px] border border-primary rounded-lg';
    const Styling =
      'flex-col items-center border border-borderColor bg-white rounded-lg xs:!w-[70px] w-[100px] h-[68px] flex justify-center';
    return (
      <div className="mt-5 flex flex-row gap-2">
        <div className={Styling}>
          <div className="text-xl font-bold">{addLeadingZeros(days)}</div>
          <p className="text-sm font-extralight">Days</p>
        </div>
        {/* <span className="mt-10 block text-2xl">:</span> */}
        <div className={Styling}>
          <div className="text-xl font-bold">{addLeadingZeros(hours)}</div>
          <p className="text-sm font-extralight">Hours</p>
        </div>
        {/* <span className="mt-10 block text-3xl">:</span> */}
        <div className={Styling}>
          <div className="text-xl font-bold">{addLeadingZeros(minutes)}</div>
          <p className="text-sm font-extralight">Mins</p>
        </div>
        {/* <span className="mt-10 block text-3xl">:</span> */}
        <div className={Styling}>
          <div className="text-xl font-bold">{addLeadingZeros(seconds)}</div>
          <p className="text-sm font-extralight">Seconds</p>
        </div>
      </div>
    );
  };

  return <Countdown date={date} renderer={renderer} />;
};

export default CountdownComponent;
