import React from 'react';
import clsx from 'clsx';

interface IProps {
  label: string;
  className?: React.ReactNode;
  type?: string;
  disabled?: boolean;
  id?: string;
  name?: string;
  value?: any;
  checked?: boolean;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  checkClass?: string;
  labelClass?: string;
}

const FormCheck = ({
  label,
  className,
  type,
  disabled,
  id,
  name,
  value,
  checked,
  onChange,
  checkClass,
  labelClass,
}: IProps) => {
  return (
    <div className={clsx(className, 'mb-1 flex items-center gap-2')}>
      <input
        type={type ? type : 'checkbox'}
        className={clsx(
          checkClass,
          'focus:shadow-outline h-3.5 w-3.5 cursor-pointer rounded-[3px] bg-transparent text-primary focus:outline-none focus:ring-0 focus:ring-offset-0 disabled:cursor-not-allowed',
          type == 'radio' && '!rounded-full',
        )}
        name={name}
        id={`default-${id}`}
        disabled={disabled}
        onChange={onChange}
        value={value}
        checked={checked}
      />
      <label
        htmlFor={`default-${id}`}
        className={`${labelClass} inline-block cursor-pointer text-base text-[#181818]`}
      >
        {label}
      </label>
    </div>
  );
};

export default FormCheck;
