'use client';
import React, { useState, useEffect } from 'react';
import { UseFormRegister } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface SizeProps {
  sm: string;
  lg: string;
  md: string;
}

const sizeStyles: SizeProps = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-5 font-bold text-base',
  lg: 'px-5 py-4 text-lg',
};

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  register?: UseFormRegister<any>;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  AddIcon?: React.ReactNode;
  passwordOverride?: boolean;
  showPasswordField?: boolean;
  setShowPasswordField?: (show: boolean) => void;
  focusedLabelClass?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      register,
      type,
      name,
      size = 'md',
      AddIcon,
      passwordOverride: passwordOverride,
      showPasswordField,
      setShowPasswordField,
      focusedLabelClass,
      ...props
    },
    ref,
  ) => {
    const [isPasswordVisible, setPasswordVisible] = useState(showPasswordField);
    const [isFocused, setIsFocused] = useState(false);

    const togglePasswordVisibility = () => {
      if (passwordOverride) {
        if (setShowPasswordField) {
          setShowPasswordField(!showPasswordField);
        }
        setPasswordVisible(!showPasswordField);
        return;
      }
      setPasswordVisible(!isPasswordVisible);
    };

    useEffect(() => {
      if (passwordOverride) setPasswordVisible(showPasswordField);
    }, [showPasswordField, passwordOverride]);

    const inputType = type === 'password' && isPasswordVisible ? 'text' : type;
    const [inputValue, setInputValue] = useState('');
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
      props.onChange?.(e); // call original onChange if provided
    };
    return (
      <div className="relative w-full">
        {AddIcon && (
          <div className="absolute left-4 top-1/2 flex -translate-y-1/2 items-center justify-center overflow-hidden rounded-l-md">
            {AddIcon}
          </div>
        )}
        <input
          id={name}
          type={inputType}
          className={cn(
            'relative flex w-full rounded-[14px] border border-[#CECECE] bg-transparent text-black placeholder-transparent focus:border-primary focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50',
            className,
            sizeStyles[size],
            AddIcon && 'pl-12',
            isFocused || inputValue ? 'pb-3 pt-7' : 'py-5',
          )}
          // {...(register ? { ...register(name) } : {})}
          ref={ref}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onChange={handleChange}
          {...(register !== undefined && { ...register(name) })}
          {...props}
        />
        <label
          htmlFor={name}
          className={cn(
            'absolute left-5 top-1/2 -translate-y-1/2 transform text-base text-[#777777] transition-all duration-300',
            isFocused || inputValue ? `top-5 text-sm` : '',
            focusedLabelClass,
          )}
        >
          {props.placeholder}
        </label>
        {type === 'password' && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-0 flex items-center px-3 focus:outline-none"
          >
            {isPasswordVisible ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-6 w-6 text-gray-500"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth="1.5"
                stroke="currentColor"
                className="h-6 w-6 text-gray-500"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"
                />
              </svg>
            )}
          </button>
        )}
      </div>
    );
  },
);

Input.displayName = 'Input';

export { Input };
