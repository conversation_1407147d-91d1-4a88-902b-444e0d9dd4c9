import React from 'react';

interface RadioButtonProps {
  label: React.ReactNode;
  value: string;
  name?: string;
  checked?: boolean;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
}

const RadioButton: React.FC<RadioButtonProps> = ({
  label,
  value,
  name,
  checked,
  onChange,
  disabled = false,
  error,
}) => {
  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange) onChange(event.target.value);
  };

  return (
    <div className="flex items-center space-x-2">
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        disabled={disabled}
        onChange={handleChange} // Pass event here
        className={`h-4 w-4 border border-[#CECECE] text-primary shadow-none outline-none ring-0 focus:border-none focus:shadow-none focus:outline-none focus:ring-transparent ${
          disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
        }`}
      />
      <label
        className={`text-xs sm:text-sm ${disabled ? 'text-black-300/50' : 'text-black-300'}`}
      >
        {label}
      </label>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

export default RadioButton;
