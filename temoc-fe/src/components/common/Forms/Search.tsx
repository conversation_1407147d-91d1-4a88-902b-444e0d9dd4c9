import clsx from 'clsx';

interface IProps {
  className?: string;
}

const Search = ({ className }: IProps) => {
  return (
    <div className="relative">
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        className="absolute left-3 top-1/2 -translate-y-1/2"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_748_830)">
          <path
            d="M15.8045 14.8619L11.8252 10.8825C12.9096 9.55625 13.4428 7.86392 13.3144 6.15556C13.1861 4.44721 12.406 2.85354 11.1356 1.7042C9.86516 0.554862 8.20158 -0.0622172 6.48895 -0.019396C4.77632 0.0234252 3.14566 0.72287 1.93426 1.93426C0.72287 3.14566 0.0234252 4.77632 -0.019396 6.48895C-0.0622172 8.20158 0.554862 9.86516 1.7042 11.1356C2.85354 12.406 4.44721 13.1861 6.15556 13.3144C7.86392 13.4428 9.55625 12.9096 10.8825 11.8252L14.8619 15.8045C14.9876 15.926 15.156 15.9932 15.3308 15.9916C15.5056 15.9901 15.6728 15.92 15.7964 15.7964C15.92 15.6728 15.9901 15.5056 15.9916 15.3308C15.9932 15.156 15.926 14.9876 15.8045 14.8619ZM6.66652 11.9999C5.61169 11.9999 4.58054 11.6871 3.70348 11.101C2.82642 10.515 2.14283 9.68204 1.73916 8.7075C1.3355 7.73296 1.22988 6.6606 1.43567 5.62604C1.64145 4.59147 2.14941 3.64117 2.89529 2.89529C3.64117 2.14941 4.59147 1.64145 5.62604 1.43567C6.6606 1.22988 7.73296 1.3355 8.7075 1.73916C9.68204 2.14283 10.515 2.82642 11.101 3.70348C11.6871 4.58054 11.9999 5.61169 11.9999 6.66652C11.9983 8.08052 11.4359 9.43615 10.436 10.436C9.43615 11.4359 8.08052 11.9983 6.66652 11.9999Z"
            fill="#666666"
          />
        </g>
        <defs>
          <clipPath id="clip0_748_830">
            <rect width="16" height="16" fill="#666666" />
          </clipPath>
        </defs>
      </svg>

      <input
        // type="search"
        id="default-search"
        className={clsx(
          className,
          'borer mx-auto block w-full rounded-full border border-transparent bg-[#E8E8E8] py-4 pl-9 pr-12 text-xs font-semibold text-[#333333] outline-none placeholder:font-semibold placeholder:text-[#333333] focus:border-none focus:border-primary disabled:cursor-not-allowed disabled:bg-[#e9ecef]',
        )}
        placeholder="Search "
        required
      />
      <div className="absolute right-3 top-1/2 -translate-y-1/2 border-l border-[#999999]">
        <svg
          className="ml-3"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M14.4904 11.4235C15.9204 11.4235 17.0837 12.581 17.0837 14.0035C17.0837 15.426 15.9204 16.5835 14.4904 16.5835C13.0596 16.5835 11.8954 15.426 11.8954 14.0035C11.8954 12.581 13.0596 11.4235 14.4904 11.4235ZM14.4904 12.6735C13.7487 12.6735 13.1454 13.2702 13.1454 14.0035C13.1454 14.7377 13.7487 15.3335 14.4904 15.3335C15.2312 15.3335 15.8337 14.7377 15.8337 14.0035C15.8337 13.2702 15.2312 12.6735 14.4904 12.6735ZM8.40074 13.4108C8.74574 13.4108 9.02574 13.6908 9.02574 14.0358C9.02574 14.3808 8.74574 14.6608 8.40074 14.6608H3.1499C2.8049 14.6608 2.5249 14.3808 2.5249 14.0358C2.5249 13.6908 2.8049 13.4108 3.1499 13.4108H8.40074ZM5.09466 3.3335C6.52549 3.3335 7.68882 4.49183 7.68882 5.91433C7.68882 7.33683 6.52549 8.4935 5.09466 8.4935C3.66465 8.4935 2.50049 7.33683 2.50049 5.91433C2.50049 4.49183 3.66465 3.3335 5.09466 3.3335ZM5.09466 4.5835C4.35382 4.5835 3.75049 5.18016 3.75049 5.91433C3.75049 6.64766 4.35382 7.2435 5.09466 7.2435C5.83632 7.2435 6.43882 6.64766 6.43882 5.91433C6.43882 5.18016 5.83632 4.5835 5.09466 4.5835ZM15.9931 5.33383C16.3381 5.33383 16.6181 5.61383 16.6181 5.95883C16.6181 6.30383 16.3381 6.58383 15.9931 6.58383H10.7431C10.3981 6.58383 10.1181 6.30383 10.1181 5.95883C10.1181 5.61383 10.3981 5.33383 10.7431 5.33383H15.9931Z"
            fill="#333333"
          />
        </svg>
      </div>
    </div>
  );
};

export default Search;
