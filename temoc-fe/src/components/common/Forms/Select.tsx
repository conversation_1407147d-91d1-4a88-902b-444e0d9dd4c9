'use client';

import React, { useState } from 'react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { cn } from '@/lib/utils';
import Copy from '../Icons/Copy';

type Option = {
  value: string;
  label: any;
};

type CustomDropdownProps = {
  options: Option[];
  placeholder?: any;
  selected: Option | null;
  onSelect: (option: Option) => void;
  className?: string;
  copy?: any;
};

const SelectComponent: React.FC<CustomDropdownProps> = ({
  options,
  placeholder = 'Select an option',
  selected,
  copy,
  onSelect,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [, setIsFocused] = useState(false);

  const handleSelect = (option: Option) => {
    onSelect(option);
    setIsOpen(false);
  };

  return (
    <div className={cn('relative w-full', className)}>
      <button
        type="button"
        onClick={() => setIsOpen((prev) => !prev)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={cn(
          'relative z-20 flex w-full items-center justify-between rounded-md border border-[#CECECE] bg-transparent text-left text-[#181818] placeholder-transparent focus:border-primary focus:outline-none focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50',
          selected ? 'py-4 pb-3 pl-5 pr-2 text-sm' : 'py-4 pl-5 pr-2',
        )}
      >
        <span className="text-base text-[#0A0E14]">
          {selected?.label || placeholder}
        </span>

        <div className="flex items-center gap-1">
          <ChevronDownIcon className="h-5 w-8 text-[#181818]" />
          {copy ? (
            <div className="flex cursor-pointer items-center gap-2 rounded-lg border border-[#CECECE] p-1 text-xs">
              COPY TO ALL <Copy />
            </div>
          ) : (
            ''
          )}
        </div>
      </button>
      {/* <label
        className={cn(
          'pointer-events-none absolute left-5 top-1/2 -translate-y-1/2 transform text-base text-[#777777] transition-all duration-300',
          (isFocused || selected) && 'top-5 text-sm',
        )}
      >
        {placeholder}
      </label> */}

      {isOpen && (
        <ul className="absolute z-30 mt-1 max-h-60 w-full overflow-y-auto rounded-[8px] border border-[#D7DAE0] bg-white shadow-lg">
          {options.map((option, index) => (
            <li
              key={option.value}
              onMouseDown={() => handleSelect(option)} // Prevent blur
              className={cn(
                'cursor-pointer px-4 py-2 text-base hover:bg-blue-100',
                option.value === selected?.value && 'text-primary',
                index !== options.length - 1 && 'border-b border-[#E5E7EB]', // light gray border except last
              )}
            >
              {option.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default SelectComponent;
