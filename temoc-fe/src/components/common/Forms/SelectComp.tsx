import { Fragment } from 'react';
import { Listbox, Transition } from '@headlessui/react';
import { IoIosArrowDown } from 'react-icons/io';

interface Iprops {
  selected: any;
  setSelected: any;
  Data?: any;
  iconClass?: string;
  placeholder?: any;
  btnClass?: any;
  dropDownClass?: any;
  innerDropdownClass?: string;
  activeClass?: string;
}
export default function SelectComp({
  Data,
  iconClass,
  placeholder,
  selected,
  dropDownClass,
  setSelected,
  btnClass,
  innerDropdownClass,
  activeClass,
}: Iprops) {
  return (
    <Listbox as="div" value={selected} onChange={setSelected}>
      <div className="relative">
        <Listbox.Button
          className={`${btnClass} relative flex w-full items-center justify-between rounded-md border border-[#CECECE] bg-white py-3 pl-4 pr-14 text-left text-xl focus:outline-none sm:max-w-[150px]`}
        >
          <span
            className={`block truncate text-xs text-black-300`}
            title={selected?.name || placeholder}
          >
            {selected?.name || placeholder}
          </span>
          <span className="pointer-events-none absolute inset-y-1/2 right-5 flex items-center">
            <i
              className={`${iconClass ? iconClass : ''} text-xs text-black-300`}
              aria-hidden="true"
            >
              <IoIosArrowDown />
            </i>
          </span>
        </Listbox.Button>

        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options
            className={`absolute z-[9999] mt-1 max-h-[20.5rem] w-full overflow-auto rounded-xl border border-[#CECECE] bg-white text-xl focus:outline-none sm:max-w-[150px] ${dropDownClass}`}
          >
            {Data.map((item: any, Idx: any) => {
              return (
                <Listbox.Option
                  key={Idx}
                  className={({ active }: any) =>
                    `relative z-20 cursor-pointer select-none px-2 py-3 text-xs text-black-300 ${
                      active ? `${activeClass}` : ''
                    } ${innerDropdownClass} ${Idx === Data.length - 1 ? '' : 'border-b border-[#CECECE]'}`
                  }
                  value={item}
                >
                  <div className="truncate">{item.name}</div>
                </Listbox.Option>
              );
            })}
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
}
