'use client';
import React, { useState } from 'react';
import { UseFormRegister } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface SizeProps {
  sm: string;
  lg: string;
  md: string;
}

const sizeStyles: SizeProps = {
  sm: 'px-3 py-2 text-sm',
  md: 'px-5 font-normal text-base',
  lg: 'px-5 py-4 text-lg',
};

export interface TextareaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'> {
  register?: UseFormRegister<any>;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  AddIcon?: React.ReactNode;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, register, name, size = 'md', AddIcon, ...props }, ref) => {
    const [, setIsFocused] = useState(false);

    return (
      <div className="relative w-full">
        {AddIcon && (
          <div className="absolute left-4 top-4 flex items-center justify-center overflow-hidden rounded-l-md">
            {AddIcon}
          </div>
        )}
        <textarea
          className={cn(
            'relative z-20 flex w-full resize-none rounded-[14px] border border-[#CECECE] bg-transparent py-5 text-black placeholder-transparent placeholder:text-[#777777] focus:border-primary focus-visible:ring-0 disabled:cursor-not-allowed disabled:opacity-50',
            className,
            sizeStyles[size],
            AddIcon && 'pl-12',
            // isFocused || props.value ? 'pb-3 pt-7' : 'py-5',
          )}
          rows={6}
          placeholder={props.placeholder}
          ref={ref}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...(register !== undefined && { ...register(name) })}
          {...props}
        />
        {/* <label
          htmlFor={name}
          className={cn(
            'absolute left-5 top-1/2 -translate-y-1/2 transform text-base text-[#777777] transition-all duration-300',
            isFocused || props.value ? 'top-5 text-sm' : '',
          )}
        >
          {props.placeholder}
        </label> */}
      </div>
    );
  },
);

Textarea.displayName = 'Textarea';

export { Textarea };
