import React from 'react';

const CreateArtistProfile = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_3633)">
        <path
          d="M8 8.5C7.40666 8.5 6.82664 8.32405 6.33329 7.99441C5.83994 7.66477 5.45543 7.19623 5.22836 6.64805C5.0013 6.09987 4.94189 5.49667 5.05765 4.91473C5.1734 4.33279 5.45912 3.79824 5.87868 3.37868C6.29824 2.95912 6.83279 2.6734 7.41473 2.55765C7.99667 2.44189 8.59987 2.5013 9.14805 2.72836C9.69623 2.95543 10.1648 3.33994 10.4944 3.83329C10.8241 4.32664 11 4.90666 11 5.5C11 6.29565 10.6839 7.05871 10.1213 7.62132C9.55871 8.18393 8.79565 8.5 8 8.5ZM8 3.5C7.60444 3.5 7.21776 3.6173 6.88886 3.83706C6.55996 4.05682 6.30362 4.36918 6.15224 4.73463C6.00087 5.10009 5.96126 5.50222 6.03843 5.89018C6.1156 6.27814 6.30608 6.63451 6.58579 6.91422C6.86549 7.19392 7.22186 7.3844 7.60982 7.46157C7.99778 7.53874 8.39992 7.49914 8.76537 7.34776C9.13082 7.19639 9.44318 6.94004 9.66294 6.61114C9.8827 6.28224 10 5.89556 10 5.5C10 4.96957 9.78929 4.46086 9.41421 4.08579C9.03914 3.71072 8.53043 3.5 8 3.5Z"
          fill="#666666"
        />
        <path
          d="M8.00031 15.4998C6.89153 15.499 5.79671 15.2525 4.79466 14.7778C3.79262 14.3032 2.90828 13.6122 2.20531 12.7548L1.94531 12.4348L2.20531 12.1198C2.90887 11.2634 3.79347 10.5737 4.79549 10.1001C5.7975 9.62649 6.89202 9.38086 8.00031 9.38086C9.10861 9.38086 10.2031 9.62649 11.2051 10.1001C12.2072 10.5737 13.0918 11.2634 13.7953 12.1198L14.0553 12.4348L13.7953 12.7548C13.0923 13.6122 12.208 14.3032 11.206 14.7778C10.2039 15.2525 9.10909 15.499 8.00031 15.4998ZM3.25531 12.4398C3.86343 13.0908 4.59888 13.6098 5.416 13.9647C6.23311 14.3196 7.11446 14.5027 8.00531 14.5027C8.89617 14.5027 9.77751 14.3196 10.5946 13.9647C11.4117 13.6098 12.1472 13.0908 12.7553 12.4398C12.1472 11.7888 11.4117 11.2697 10.5946 10.9148C9.77751 10.56 8.89617 10.3768 8.00531 10.3768C7.11446 10.3768 6.23311 10.56 5.416 10.9148C4.59888 11.2697 3.86343 11.7888 3.25531 12.4398Z"
          fill="#666666"
        />
        <path
          d="M8.00012 15.5C6.2965 15.5011 4.64324 14.9222 3.31247 13.8586C1.98169 12.7949 1.0527 11.3099 0.678391 9.64785C0.304082 7.98586 0.506761 6.24594 1.25307 4.71448C1.99938 3.18302 3.24484 1.95127 4.78448 1.22197C6.32411 0.492673 8.06616 0.309278 9.72392 0.701974C11.3817 1.09467 12.8563 2.04005 13.9052 3.38253C14.9541 4.725 15.5146 6.38457 15.4946 8.08808C15.4746 9.79159 14.8752 11.4375 13.7951 12.755C13.0922 13.6125 12.2078 14.3034 11.2058 14.778C10.2037 15.2527 9.1089 15.4993 8.00012 15.5ZM8.00012 1.50001C6.71455 1.50001 5.45784 1.88122 4.38892 2.59545C3.32 3.30968 2.48688 4.32484 1.99491 5.51256C1.50294 6.70028 1.37422 8.00722 1.62502 9.26809C1.87582 10.529 2.49489 11.6872 3.40393 12.5962C4.31297 13.5052 5.47116 14.1243 6.73204 14.3751C7.99291 14.6259 9.29985 14.4972 10.4876 14.0052C11.6753 13.5133 12.6904 12.6801 13.4047 11.6112C14.1189 10.5423 14.5001 9.28558 14.5001 8.00001C14.5001 6.2761 13.8153 4.6228 12.5963 3.40381C11.3773 2.18482 9.72403 1.50001 8.00012 1.50001Z"
          fill="#666666"
        />
        <path
          d="M2.59033 12.4402C2.59033 12.4402 7.62533 18.0652 12.7503 13.0002L13.4103 12.4402C13.4103 12.4402 9.13033 8.00024 4.78533 10.6652L2.59033 12.4402Z"
          fill="#666666"
        />
        <path
          d="M8 8C9.38071 8 10.5 6.88071 10.5 5.5C10.5 4.11929 9.38071 3 8 3C6.61929 3 5.5 4.11929 5.5 5.5C5.5 6.88071 6.61929 8 8 8Z"
          fill="#666666"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_3633">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CreateArtistProfile;
