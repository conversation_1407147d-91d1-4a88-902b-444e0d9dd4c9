import React from 'react';
interface Iprops {
  className?: any;
}
const CreateProject = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.99973 6.26314C8.72925 6.26314 10.1313 4.86109 10.1313 3.13157C10.1313 1.40205 8.72925 0 6.99973 0C5.27022 0 3.86816 1.40205 3.86816 3.13157C3.86816 4.86109 5.27022 6.26314 6.99973 6.26314Z"
        fill="currentColor"
      />
      <path
        d="M6.99994 7C3.94786 7 1.47363 9.47425 1.47363 12.5263C1.47363 13.3402 2.13344 14 2.94732 14H11.0526C11.8665 14 12.5263 13.3402 12.5263 12.5263C12.5262 9.47425 10.052 7 6.99994 7Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default CreateProject;
