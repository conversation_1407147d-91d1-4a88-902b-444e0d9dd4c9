import React from 'react';
interface Iprops {
  className?: string;
}
const DeleteIcon = ({ className }: Iprops) => {
  return (
    <svg
      className={className && className}
      width="18"
      height="23"
      viewBox="0 0 18 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.2188 3.25H12.4375V2.5625C12.4375 1.4234 11.5141 0.5 10.375 0.5H7.625C6.4859 0.5 5.5625 1.4234 5.5625 2.5625V3.25H1.78125C0.832027 3.25 0.0625 4.01953 0.0625 4.96875V6.34375C0.0625 6.72346 0.370285 7.03125 0.75 7.03125H17.25C17.6297 7.03125 17.9375 6.72346 17.9375 6.34375V4.96875C17.9375 4.01953 17.168 3.25 16.2188 3.25ZM6.9375 2.5625C6.9375 2.18352 7.24602 1.875 7.625 1.875H10.375C10.754 1.875 11.0625 2.18352 11.0625 2.5625V3.25H6.9375V2.5625ZM1.3672 8.40625C1.33813 8.40625 1.30936 8.41215 1.28263 8.4236C1.25591 8.43505 1.23178 8.4518 1.21172 8.47284C1.19166 8.49388 1.17608 8.51878 1.16592 8.54602C1.15576 8.57326 1.15123 8.60228 1.15262 8.63132L1.7198 20.5355C1.77223 21.6372 2.67715 22.5 3.77973 22.5H14.2203C15.3229 22.5 16.2278 21.6372 16.2802 20.5355L16.8474 8.63132C16.8488 8.60228 16.8442 8.57326 16.8341 8.54602C16.8239 8.51878 16.8083 8.49388 16.7883 8.47284C16.7682 8.4518 16.7441 8.43505 16.7174 8.4236C16.6906 8.41215 16.6619 8.40625 16.6328 8.40625H1.3672ZM11.75 10.125C11.75 9.74516 12.0577 9.4375 12.4375 9.4375C12.8173 9.4375 13.125 9.74516 13.125 10.125V19.0625C13.125 19.4423 12.8173 19.75 12.4375 19.75C12.0577 19.75 11.75 19.4423 11.75 19.0625V10.125ZM8.3125 10.125C8.3125 9.74516 8.62016 9.4375 9 9.4375C9.37984 9.4375 9.6875 9.74516 9.6875 10.125V19.0625C9.6875 19.4423 9.37984 19.75 9 19.75C8.62016 19.75 8.3125 19.4423 8.3125 19.0625V10.125ZM4.875 10.125C4.875 9.74516 5.18266 9.4375 5.5625 9.4375C5.94234 9.4375 6.25 9.74516 6.25 10.125V19.0625C6.25 19.4423 5.94234 19.75 5.5625 19.75C5.18266 19.75 4.875 19.4423 4.875 19.0625V10.125Z"
        fill="#FE2125"
      />
    </svg>
  );
};

export default DeleteIcon;
