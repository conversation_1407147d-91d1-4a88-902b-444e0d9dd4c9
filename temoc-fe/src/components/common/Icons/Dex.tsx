import React from 'react';

const Dex = () => {
  return (
    <svg
      width="13"
      height="16"
      viewBox="0 0 13 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2146_42203)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.83156 5.69952C8.30498 5.45547 8.90737 5.09621 9.51047 4.604C9.63764 4.87701 9.65157 5.11525 9.58605 5.30763C9.53962 5.4432 9.45213 5.56112 9.33709 5.65493C9.21251 5.75632 9.05692 5.83013 8.88493 5.87003C8.55859 5.94603 8.17756 5.90165 7.83156 5.69952ZM7.91364 8.20011L8.54032 8.57435C7.26075 9.31579 6.9129 10.6925 6.49968 12.0335C6.08652 10.6925 5.73862 9.31579 4.4591 8.57435L5.08578 8.20011C5.1464 8.17642 5.19822 8.1335 5.2338 8.07751C5.26939 8.02151 5.28692 7.9553 5.28387 7.88837C5.22646 6.63253 5.55445 6.07717 5.99686 5.72848C6.15555 5.60357 6.32878 5.54085 6.49968 5.54085C6.67059 5.54085 6.84382 5.60357 7.00256 5.72848C7.44497 6.07717 7.77296 6.63253 7.71555 7.88837C7.7125 7.9553 7.73003 8.02151 7.76562 8.07751C7.8012 8.1335 7.85302 8.17642 7.91364 8.20011ZM6.49968 0C7.22407 0.0201067 7.95027 0.165493 8.58051 0.44832C9.01694 0.644427 9.42417 0.903467 9.79291 1.21323C9.95944 1.35307 10.0966 1.48816 10.2476 1.64368C10.655 1.65824 11.2504 1.19035 11.5268 0.752533C11.0511 2.3648 8.88039 4.26875 7.3776 4.99728C7.37698 4.99701 7.37656 4.99664 7.3761 4.99632C7.1064 4.78341 6.80307 4.67696 6.49968 4.67696C6.1963 4.67696 5.89302 4.78341 5.62332 4.99632C5.62285 4.99659 5.62244 4.99707 5.62182 4.99728C4.11898 4.26875 1.94834 2.3648 1.47266 0.752533C1.74901 1.19035 2.34438 1.65824 2.75176 1.64368C2.90286 1.48821 3.03998 1.35307 3.20645 1.21323C3.5752 0.903467 3.98243 0.644427 4.41886 0.44832C5.04915 0.165493 5.77535 0.0201067 6.49968 0ZM5.1678 5.69952C4.69444 5.45547 4.092 5.09621 3.48895 4.604C3.36178 4.87701 3.34785 5.11525 3.41332 5.30763C3.4598 5.4432 3.54729 5.56112 3.66228 5.65493C3.78691 5.75632 3.9425 5.83013 4.11449 5.87003C4.44083 5.94603 4.82181 5.90165 5.1678 5.69952Z"
          fill="black"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.1713 4.00066C10.5033 3.65426 10.7959 3.27084 11.0311 2.92871L11.1506 3.16119C11.5352 3.95676 11.7351 4.74914 11.7351 5.64087L11.7343 7.05591L11.7415 7.78946C11.7693 9.59031 12.1462 11.4123 13 13.0798L11.2137 11.5906L9.94975 13.7111L8.62189 12.4187L6.5 15.9787L4.37811 12.4188L3.05031 13.7111L1.78637 11.5906L0 13.0798C0.85377 11.4124 1.23067 9.59036 1.25858 7.78951L1.2658 7.05596L1.26497 5.64092C1.26497 4.74914 1.46477 3.95676 1.84951 3.16124L1.96893 2.92876C2.20417 3.2709 2.49667 3.65426 2.82874 4.00071L2.72505 4.22338C2.5236 4.65586 2.4569 5.13943 2.61383 5.59932C2.71499 5.89554 2.89962 6.14956 3.13764 6.34348C3.36875 6.5318 3.64227 6.65874 3.92863 6.7254C4.11517 6.76882 4.30522 6.78668 4.49403 6.7806C4.44997 7.03879 4.43073 7.30658 4.42949 7.57943L2.74444 8.58562L4.04475 9.33916C4.14868 9.39941 4.24762 9.46842 4.34056 9.54551C5.41259 10.5406 6.05712 13.4844 6.50005 14.9224C6.94303 13.4844 7.58751 10.5406 8.6596 9.54551C8.75253 9.46841 8.85146 9.39939 8.9554 9.33916L10.2557 8.58562L8.57061 7.57943C8.56937 7.30658 8.55013 7.03879 8.50608 6.7806C8.69488 6.78668 8.88493 6.76882 9.07147 6.7254C9.35783 6.65874 9.6314 6.5318 9.86246 6.34348C10.1004 6.14956 10.2851 5.89554 10.3862 5.59932C10.5432 5.13943 10.4765 4.65591 10.2751 4.22338L10.1714 4.00071L10.1713 4.00066Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2146_42203">
          <rect width="13" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Dex;
