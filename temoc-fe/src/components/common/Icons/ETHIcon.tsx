import React from 'react';
interface Iprops {
  className?: string;
}
const ETHIcon = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_125_329)">
        <path
          d="M10.0006 20.4905V15.5279L3.86157 11.9379L10.0006 20.4905Z"
          fill="#F0CDC2"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
        <path
          d="M10.021 20.4905V15.5279L16.1599 11.9379L10.021 20.4905Z"
          fill="#C9B3F5"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
        <path
          d="M10.0007 14.2942V7.96057L3.79126 10.7411L10.0007 14.2942Z"
          fill="#88AAF1"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
        <path
          d="M10.021 14.2942V7.96057L16.2304 10.7411L10.021 14.2942Z"
          fill="#C9B3F5"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
        <path
          d="M3.79126 10.7411L10.0006 0.710205V7.9606L3.79126 10.7411Z"
          fill="#F0CDC2"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
        <path
          d="M16.2304 10.7411L10.021 0.710205V7.9606L16.2304 10.7411Z"
          fill="#B8FAF6"
          stroke="#1616B4"
          strokeWidth="0.0659341"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_125_329">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.600342)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ETHIcon;
