import React from 'react';
interface Iprops {
  className?: any;
}
const Explore = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.00049 0C3.14068 0 0.000488281 3.14019 0.000488281 7C0.000488281 10.8598 3.14068 14 7.00049 14C10.8601 14 14 10.8598 14 7C14 3.14019 10.8601 0 7.00049 0ZM10.4604 3.98761L8.24351 8.10698C8.2131 8.16356 8.16674 8.20997 8.11018 8.24043L3.99104 10.457C3.94328 10.4827 3.8899 10.4961 3.83569 10.496C3.79258 10.4961 3.74988 10.4876 3.71005 10.4712C3.67021 10.4547 3.63403 10.4305 3.60357 10.4C3.55365 10.3501 3.52114 10.2854 3.51087 10.2156C3.5006 10.1458 3.51311 10.0745 3.54655 10.0124L5.763 5.89336C5.79345 5.83677 5.83985 5.79037 5.89644 5.75991L10.016 3.54324C10.0782 3.50979 10.1494 3.49727 10.2192 3.50752C10.2891 3.51777 10.3537 3.55025 10.4036 3.60014C10.4535 3.65003 10.486 3.71467 10.4962 3.78447C10.5064 3.85427 10.4939 3.92551 10.4604 3.98761Z"
        fill="currentColor"
      />
      <path
        d="M7.00712 6.2373C6.58487 6.2373 6.24268 6.57916 6.24268 7.00141C6.24268 7.42354 6.58499 7.76597 7.00712 7.76597C7.42891 7.76597 7.77122 7.42354 7.77122 7.00141C7.77122 6.57916 7.42891 6.2373 7.00712 6.2373Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Explore;
