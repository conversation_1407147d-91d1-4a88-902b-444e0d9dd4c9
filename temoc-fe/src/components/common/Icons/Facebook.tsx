import React from 'react';

interface iconProps {
  width?: any;
  height?: any;
}

const Facebook: React.FC<iconProps> = ({ width, height }: any) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_4528)">
        <path
          d="M12.25 0.5H1.75C0.784875 0.5 0 1.28487 0 2.25V12.75C0 13.7151 0.784875 14.5 1.75 14.5H12.25C13.2151 14.5 14 13.7151 14 12.75V2.25C14 1.28487 13.2151 0.5 12.25 0.5Z"
          fill="#1976D2"
        />
        <path
          d="M11.8125 7.5H9.625V5.75C9.625 5.267 10.017 5.3125 10.5 5.3125H11.375V3.125H9.625C8.17513 3.125 7 4.30013 7 5.75V7.5H5.25V9.6875H7V14.5H9.625V9.6875H10.9375L11.8125 7.5Z"
          fill="#FAFAFA"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_4528">
          <rect
            width="14"
            height="14"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Facebook;
