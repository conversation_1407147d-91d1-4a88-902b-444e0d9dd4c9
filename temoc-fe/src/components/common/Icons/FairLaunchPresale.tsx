import React from 'react';

const FairLaunchPresale = () => {
  return (
    <svg
      width="122"
      height="122"
      viewBox="0 0 122 122"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="3" y="3" width="116" height="116" rx="58" fill="white" />
      <rect
        x="3"
        y="3"
        width="116"
        height="116"
        rx="58"
        stroke="#CECECE"
        strokeWidth="6"
      />
      <g clipPath="url(#clip0_2087_8104)">
        <path
          d="M60.9999 51.1959C50.0249 51.1959 41.0974 60.1234 41.0974 71.0972C41.0974 82.0709 50.0261 90.9997 60.9999 90.9997C71.9736 90.9997 80.9024 82.0722 80.9024 71.0972C80.9024 60.1222 71.9736 51.1959 60.9999 51.1959ZM60.9999 89.1047C51.0711 89.1047 42.9936 81.0259 42.9936 71.0972C42.9936 61.1684 51.0711 53.0909 60.9999 53.0909C70.9286 53.0909 79.0061 61.1684 79.0061 71.0972C79.0061 81.0259 70.9286 89.1047 60.9999 89.1047ZM60.9999 56.2497C52.8124 56.2497 46.1524 62.9109 46.1524 71.0972C46.1524 79.2834 52.8136 85.9447 60.9999 85.9447C69.1861 85.9447 75.8474 79.2847 75.8474 71.0972C75.8474 62.9096 69.1861 56.2497 60.9999 56.2497ZM60.9999 84.0497C53.8574 84.0497 48.0474 78.2397 48.0474 71.0972C48.0474 63.9547 53.8574 58.1459 60.9999 58.1459C68.1424 58.1459 73.9524 63.9559 73.9524 71.0972C73.9524 78.2384 68.1424 84.0497 60.9999 84.0497ZM58.9074 68.0572C58.9074 68.8609 59.4199 69.5722 60.1824 69.8259L62.4174 70.5709C63.1667 70.8196 63.8185 71.2982 64.2801 71.9387C64.7417 72.5792 64.9897 73.3489 64.9886 74.1384C64.9862 75.0295 64.6853 75.894 64.1341 76.5941C63.5828 77.2942 62.813 77.7895 61.9474 78.0009V79.2059C61.9474 79.4572 61.8476 79.6982 61.6699 79.8759C61.4922 80.0536 61.2512 80.1534 60.9999 80.1534C60.7486 80.1534 60.5076 80.0536 60.3299 79.8759C60.1522 79.6982 60.0524 79.4572 60.0524 79.2059V77.9996C59.1868 77.7882 58.4169 77.2929 57.8657 76.5928C57.3145 75.8927 57.0136 75.0282 57.0111 74.1371C57.0111 73.8857 57.111 73.6445 57.2888 73.4667C57.4666 73.2889 57.7078 73.189 57.9593 73.189C58.2107 73.189 58.4519 73.2889 58.6297 73.4667C58.8075 73.6445 58.9074 73.8857 58.9074 74.1371C58.9074 75.2921 59.8449 76.2309 60.9999 76.2309C62.1549 76.2309 63.0924 75.2922 63.0924 74.1384C63.0927 73.747 62.9696 73.3655 62.7408 73.048C62.5119 72.7304 62.1888 72.4931 61.8174 72.3697L59.5824 71.6234C58.8333 71.3748 58.1816 70.8963 57.72 70.2561C57.2584 69.6159 57.0104 68.8464 57.0111 68.0572C57.0136 67.1661 57.3145 66.3016 57.8657 65.6015C58.4169 64.9014 59.1868 64.4061 60.0524 64.1947V62.9897C60.0524 62.7384 60.1522 62.4974 60.3299 62.3197C60.5076 62.142 60.7486 62.0422 60.9999 62.0422C61.2512 62.0422 61.4922 62.142 61.6699 62.3197C61.8476 62.4974 61.9474 62.7384 61.9474 62.9897V64.1947C62.813 64.4061 63.5828 64.9014 64.1341 65.6015C64.6853 66.3016 64.9862 67.1661 64.9886 68.0572C64.9886 68.3086 64.8887 68.5498 64.7109 68.7276C64.5331 68.9054 64.292 69.0053 64.0405 69.0053C63.789 69.0053 63.5479 68.9054 63.3701 68.7276C63.1923 68.5498 63.0924 68.3086 63.0924 68.0572C63.0924 66.9034 62.1536 65.9647 60.9999 65.9647C59.8461 65.9647 58.9074 66.9022 58.9074 68.0572ZM44.2574 53.4072V44.2446H46.3286C46.503 44.2435 46.6736 44.1945 46.8219 44.1029C46.9703 44.0112 47.0905 43.8806 47.1695 43.7251C47.2485 43.5697 47.2832 43.3956 47.2698 43.2218C47.2564 43.0479 47.1955 42.8811 47.0936 42.7396L41.5461 35.1559C41.1899 34.6684 40.3736 34.6684 40.0174 35.1559L34.4699 42.7396C34.3667 42.8809 34.3047 43.048 34.2907 43.2223C34.2766 43.3967 34.3111 43.5715 34.3903 43.7275C34.4695 43.8834 34.5903 44.0144 34.7394 44.1059C34.8885 44.1974 35.06 44.2459 35.2349 44.2459H37.3061V53.4072C37.3061 53.9309 37.7311 54.3547 38.2549 54.3547H43.3086C43.8324 54.3547 44.2561 53.9297 44.2561 53.4072H44.2574ZM42.3611 43.2984V52.4597H39.2024V43.2971C39.2021 43.0461 39.1021 42.8054 38.9244 42.628C38.7468 42.4506 38.506 42.3509 38.2549 42.3509H37.0999L40.7824 37.3184L44.4649 42.3496H43.3086C43.1841 42.3496 43.0608 42.3742 42.9458 42.4219C42.8307 42.4696 42.7262 42.5395 42.6382 42.6276C42.5502 42.7157 42.4804 42.8203 42.4329 42.9354C42.3854 43.0505 42.361 43.1739 42.3611 43.2984ZM55.4536 40.4546H57.5249V47.0884C57.5249 47.6122 57.9499 48.0371 58.4724 48.0371H63.5274C64.0499 48.0371 64.4749 47.6122 64.4749 47.0884V40.4546H66.5461C66.7207 40.4537 66.8916 40.4047 67.0401 40.313C67.1886 40.2213 67.309 40.0905 67.388 39.9348C67.4671 39.7792 67.5017 39.6048 67.4881 39.4308C67.4746 39.2568 67.4133 39.0899 67.3111 38.9484L61.7636 31.3646C61.4086 30.8771 60.5911 30.8771 60.2361 31.3646L54.6874 38.9496C54.5842 39.0909 54.5222 39.258 54.5082 39.4323C54.4941 39.6067 54.5286 39.7815 54.6078 39.9375C54.687 40.0934 54.8078 40.2244 54.9569 40.3159C55.106 40.4074 55.2775 40.4559 55.4524 40.4559L55.4536 40.4546ZM60.9999 33.5271L64.6824 38.5596H63.5274C63.2761 38.5596 63.0351 38.6595 62.8574 38.8372C62.6797 39.0149 62.5799 39.2559 62.5799 39.5071V46.1409H59.4199V39.5071C59.4199 39.2559 59.3201 39.0149 59.1424 38.8372C58.9647 38.6595 58.7237 38.5596 58.4724 38.5596H57.3174L60.9999 33.5271ZM87.5299 42.7396L81.9811 35.1559C81.6261 34.6684 80.8099 34.6684 80.4536 35.1559L74.9061 42.7396C74.803 42.8809 74.741 43.048 74.7269 43.2223C74.7129 43.3967 74.7474 43.5715 74.8266 43.7275C74.9058 43.8834 75.0266 44.0144 75.1757 44.1059C75.3247 44.1974 75.4962 44.2459 75.6711 44.2459H77.7424V53.4072C77.7424 53.9297 78.1674 54.3547 78.6899 54.3547H83.7449C84.2686 54.3547 84.6924 53.9297 84.6924 53.4072V44.2446H86.7649C86.9394 44.2437 87.1103 44.1947 87.2589 44.103C87.4074 44.0113 87.5278 43.8805 87.6068 43.7248C87.6858 43.5692 87.7205 43.3948 87.7069 43.2208C87.6933 43.0468 87.6321 42.8799 87.5299 42.7384V42.7396ZM83.7449 42.3509C83.4936 42.3509 83.2526 42.4507 83.0749 42.6284C82.8972 42.8061 82.7974 43.0471 82.7974 43.2984V52.4597H79.6374V43.2971C79.6372 43.1727 79.6125 43.0495 79.5648 42.9347C79.517 42.8198 79.4471 42.7154 79.359 42.6275C79.2709 42.5397 79.1663 42.47 79.0513 42.4225C78.9363 42.3751 78.8131 42.3507 78.6886 42.3509H77.5336L81.2161 37.3184L84.8999 42.3496L83.7449 42.3509Z"
          fill="#333333"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_8104">
          <rect
            width="60"
            height="60"
            fill="white"
            transform="translate(31 31)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default FairLaunchPresale;
