import React from 'react';

const GatedCommunitySetup = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.1422 4.85654C5.1422 4.29157 5.30973 3.73929 5.62361 3.26953C5.93749 2.79978 6.38363 2.43365 6.90559 2.21744C7.42755 2.00124 8.00191 1.94467 8.55602 2.05489C9.11014 2.16511 9.61913 2.43717 10.0186 2.83666C10.4181 3.23616 10.6902 3.74514 10.8004 4.29926C10.9106 4.85337 10.854 5.42773 10.6378 5.94969C10.4216 6.47166 10.0555 6.91779 9.58575 7.23167C9.11599 7.54555 8.56371 7.71308 7.99874 7.71308C7.24114 7.71308 6.51457 7.41213 5.97886 6.87642C5.44316 6.34072 5.1422 5.61414 5.1422 4.85654ZM12.5692 9.42701C12.9082 9.42701 13.2396 9.32649 13.5214 9.13816C13.8033 8.94983 14.0229 8.68215 14.1527 8.36897C14.2824 8.05579 14.3163 7.71118 14.2502 7.37871C14.1841 7.04624 14.0208 6.74085 13.7811 6.50116C13.5414 6.26146 13.236 6.09822 12.9036 6.03209C12.5711 5.96596 12.2265 5.9999 11.9133 6.12962C11.6001 6.25935 11.3325 6.47902 11.1441 6.76088C10.9558 7.04273 10.8553 7.3741 10.8553 7.71308C10.8553 8.16764 11.0359 8.60359 11.3573 8.92501C11.6787 9.24643 12.1146 9.42701 12.5692 9.42701ZM3.42827 9.42701C3.76726 9.42701 4.09863 9.32649 4.38048 9.13816C4.66233 8.94983 4.88201 8.68215 5.01173 8.36897C5.14146 8.05579 5.1754 7.71118 5.10927 7.37871C5.04313 7.04624 4.8799 6.74085 4.6402 6.50116C4.40051 6.26146 4.09511 6.09822 3.76264 6.03209C3.43018 5.96596 3.08556 5.9999 2.77238 6.12962C2.45921 6.25935 2.19153 6.47902 2.0032 6.76088C1.81487 7.04273 1.71435 7.3741 1.71435 7.71308C1.71435 8.16764 1.89492 8.60359 2.21635 8.92501C2.53777 9.24643 2.97371 9.42701 3.42827 9.42701ZM7.99874 8.28439C6.85638 8.30707 5.75095 8.69312 4.84283 9.38654C3.93471 10.08 3.27114 11.0447 2.94838 12.1407C2.85392 12.4275 2.82952 12.7328 2.87723 13.031C2.92494 13.3292 3.04337 13.6116 3.2226 13.8546C3.38262 14.0775 3.59377 14.2586 3.83833 14.3829C4.08289 14.5072 4.35371 14.5709 4.62802 14.5688H11.3695C11.6453 14.569 11.9171 14.5026 12.1617 14.3753C12.4064 14.248 12.6167 14.0635 12.7749 13.8375C12.9541 13.5945 13.0725 13.3121 13.1203 13.0139C13.168 12.7157 13.1436 12.4104 13.0491 12.1236C12.7233 11.0307 12.0584 10.0698 11.1506 9.37964C10.2427 8.68951 9.13893 8.30594 7.99874 8.28439ZM2.57131 10.2554C2.00204 10.4517 1.48338 10.7718 1.05266 11.1927C0.621948 11.6135 0.289895 12.1246 0.0804088 12.6892C0.00547421 12.8973 -0.0175609 13.1205 0.0133013 13.3396C0.0441635 13.5586 0.127991 13.7668 0.257514 13.9461C0.395999 14.1394 0.578708 14.2967 0.790395 14.4051C1.00208 14.5134 1.23661 14.5695 1.4744 14.5688H2.3485L2.28566 14.5117C2.00501 14.1242 1.81903 13.6764 1.74264 13.2042C1.66625 12.7319 1.70156 12.2484 1.84575 11.7922C2.02293 11.2518 2.26665 10.7356 2.57131 10.2554ZM15.9171 12.6835C15.7076 12.1196 15.3755 11.6092 14.9447 11.1893C14.5139 10.7694 13.9952 10.4504 13.4262 10.2554C13.7269 10.7362 13.9667 11.2524 14.1403 11.7922C14.2861 12.2475 14.3233 12.7306 14.2489 13.2028C14.1745 13.675 13.9906 14.1233 13.7118 14.5117L13.6604 14.5688H14.5345C14.7723 14.5695 15.0068 14.5134 15.2185 14.4051C15.4302 14.2967 15.6129 14.1394 15.7514 13.9461C15.8782 13.7643 15.9594 13.5547 15.9882 13.335C16.0171 13.1152 15.9927 12.8918 15.9171 12.6835Z"
        fill="#666666"
      />
    </svg>
  );
};

export default GatedCommunitySetup;
