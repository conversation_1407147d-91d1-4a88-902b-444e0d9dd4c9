import React from 'react';

const Goal = () => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2185_31780)">
        <path
          d="M7.00054 0.437406C5.26033 0.438283 3.59163 1.12991 2.36103 2.36035C1.13043 3.59079 0.438595 5.25941 0.4375 6.99962C0.438376 8.73998 1.13012 10.4088 2.36074 11.6394C3.59136 12.87 5.26018 13.5618 7.00054 13.5627C8.10726 13.5615 9.19573 13.2806 10.1648 12.7461C11.1339 12.2116 11.9522 11.4409 12.5437 10.5055C13.1351 9.57004 13.4805 8.5003 13.5478 7.39563C13.6151 6.29096 13.4021 5.1872 12.9286 4.18689L12.2779 4.83757C12.217 4.89901 12.1444 4.9478 12.0646 4.98115C11.9847 5.01449 11.899 5.03173 11.8124 5.03187H11.6306C11.8893 5.63625 12.0324 6.30181 12.0324 6.99962C12.0315 8.33388 11.5011 9.61324 10.5576 10.5567C9.61415 11.5002 8.3348 12.0306 7.00054 12.0315C4.22419 12.0315 1.96871 9.77598 1.96871 6.99962C1.96966 6.16855 2.17631 5.35064 2.57021 4.61885C2.96412 3.88706 3.53304 3.26415 4.22621 2.80569C4.91939 2.34723 5.71526 2.06747 6.54285 1.99138C7.37043 1.91529 8.20397 2.04523 8.96912 2.36961V2.18772C8.96912 2.01326 9.03857 1.84543 9.16259 1.72223L9.8141 1.07073C8.93508 0.652907 7.97381 0.435983 7.00054 0.437406ZM11.151 0.437406C11.095 0.439074 11.0417 0.462153 11.0022 0.501896L9.47098 2.03228C9.45052 2.05266 9.43429 2.07688 9.42322 2.10356C9.41215 2.13024 9.40647 2.15884 9.4065 2.18772V3.62881L6.5177 6.5176C6.39046 6.6461 6.31909 6.81962 6.31909 7.00045C6.31909 7.18128 6.39046 7.3548 6.5177 7.48329C6.78227 7.74787 7.21799 7.74704 7.48256 7.48164L10.3705 4.59367H11.8124C11.8413 4.5937 11.8699 4.58801 11.8966 4.57694C11.9233 4.56588 11.9475 4.54964 11.9679 4.52918L13.4983 2.99797C13.5282 2.96725 13.5485 2.92841 13.5566 2.88627C13.5647 2.84413 13.5602 2.80055 13.5437 2.76092C13.5273 2.7213 13.4995 2.68739 13.464 2.66339C13.4284 2.63938 13.3866 2.62636 13.3437 2.62591H12.3391L12.5136 2.45064C12.6407 2.32253 12.7122 2.14945 12.7125 1.96896C12.7128 1.78847 12.6419 1.61514 12.5152 1.4866C12.3829 1.35431 12.2068 1.28817 12.0315 1.28817C11.8563 1.28817 11.681 1.35431 11.5487 1.4866L11.3751 1.66023V0.654852C11.375 0.625727 11.3691 0.596914 11.3577 0.5701C11.3463 0.543285 11.3297 0.519006 11.3089 0.498682C11.288 0.478359 11.2633 0.462399 11.2362 0.451736C11.2091 0.441072 11.1801 0.436747 11.151 0.437406ZM7.00054 3.499C5.06999 3.499 3.4991 5.0699 3.4991 6.99962C3.4991 8.92935 5.06999 10.4994 7.00054 10.4994C7.9284 10.4985 8.81799 10.1295 9.47401 9.47333C10.13 8.81716 10.4989 7.92748 10.4995 6.99962C10.4995 6.44319 10.3656 5.91736 10.1341 5.44939L8.93523 6.64824C8.9862 6.93164 8.97446 7.22276 8.90083 7.50114C8.8272 7.77952 8.69348 8.03838 8.50906 8.25952C8.32464 8.48067 8.09401 8.65872 7.83339 8.78115C7.57277 8.90359 7.28849 8.96744 7.00054 8.9682C6.47879 8.96755 5.97858 8.76005 5.60956 8.39119C5.24055 8.02233 5.03284 7.5222 5.03196 7.00045C5.03262 6.47869 5.24012 5.97848 5.60898 5.60947C5.97784 5.24045 6.47796 5.03274 6.99972 5.03187C7.12043 5.03187 7.23783 5.04427 7.35193 5.06494L8.55077 3.8661C8.06946 3.62525 7.53875 3.49958 7.00054 3.499Z"
          fill="#666666"
        />
      </g>
      <defs>
        <clipPath id="clip0_2185_31780">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Goal;
