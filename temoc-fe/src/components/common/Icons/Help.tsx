import React from 'react';
interface Iprops {
  className?: any;
}
const Help = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      fill="currentColor"
      width="20"
      height="20"
      version="1.1"
      id="Layer_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 299.278 299.278"
      xmlSpace="preserve"
    >
      <g id="XMLID_1255_">
        <g>
          <g>
            <path
              d="M119.077,199.899l-4.661,3.105c6.383,3.282,10.746,9.948,10.703,17.603c-0.016,2.807-0.641,5.504-1.747,7.957
				l2.952,4.124l49.88,25.691l-39.67-55.287C132.527,197.495,124.805,196.082,119.077,199.899z"
            />
            <circle cx="44.465" cy="136.184" r="22.044" />
            <path
              d="M168.117,268.083l-42.528-22.437c-1.813-0.957-3.829-1.462-5.879-1.475l-63.417-0.383l17.768-3.81l-8.784-0.049
				c-8.915-0.051-15.616-6.193-17.318-10.85c-1.501-4.109-12.357-33.823-14.072-38.519l23.116,35.214
				c1.832,2.79,4.944,4.505,8.328,4.526l40.072,0.226h0.001c0.02,0,0.04,0.001,0.059,0.001c5.5,0,9.995-4.445,10.026-9.974
				c0.031-5.541-4.436-10.055-9.974-10.087l-34.694-0.195l-20.933-31.89l24.662,22.281l13.54,0.076l-6.531-28.41
				c-1.843-8.015-9.834-13.02-17.85-11.177l-25.881,5.95c-8.015,1.843-13.02,9.834-11.177,17.85c0,0,16.219,70.55,17.233,74.963
				l0.02-0.025c1.461,5.4,6.367,9.392,12.232,9.428l60.299,0.363l39.768,20.981c6.259,3.301,13.968,0.878,17.245-5.333
				C176.74,279.093,174.351,271.373,168.117,268.083z"
            />
            <path
              d="M9.302,60.366C4.165,60.366,0,64.532,0,69.669V263.5c0,5.137,4.165,9.302,9.302,9.302c5.138,0,9.302-4.165,9.302-9.302
				V69.669C18.604,64.532,14.44,60.366,9.302,60.366z"
            />
            <path
              d="M299.005,76.487c0-8.58-6.955-15.536-15.536-15.536h-27.706c-8.581,0-15.536,6.956-15.536,15.536v9.844l7.97-0.935
				l21.614-11.331l-22.788,22.642l-34.845,4.087c-6.087,0.714-10.442,6.228-9.729,12.314c0.711,6.063,6.198,10.442,12.314,9.729
				l38.654-4.534c2.467-0.289,4.766-1.399,6.529-3.15l25.509-25.346l-17.687,33.218c-3.091,5.804-8.377,5.751-13.057,6.299
				l-14.483,1.699c0,0-0.233,5.311-0.241,123.623c-0.001,7.355,5.961,13.317,13.315,13.317c0.001,0,0.001,0,0.001,0
				c7.354,0,13.316-5.962,13.317-13.315l0.007-98.45h5.744c0,0.012-0.002,0.025-0.002,0.038l0.278,98.448
				c0.021,7.342,5.978,13.278,13.315,13.278c0.012,0,0.026,0,0.038,0c7.354-0.021,13.3-6,13.279-13.354L299.005,76.487z"
            />
            <path
              d="M255.329,48.248c3.905,3.06,8.821,4.888,14.167,4.888c5.349,0,10.267-1.831,14.175-4.894
				c5.37-4.21,8.824-10.752,8.824-18.104c0-12.702-10.297-22.998-22.998-22.998s-22.998,10.297-22.998,22.998
				C246.497,37.494,249.955,44.038,255.329,48.248z"
            />
            <path
              d="M215.797,230.526h-21.255c-1.032,0-2.016,0.435-2.71,1.199c-0.694,0.764-1.034,1.784-0.936,2.811l2.779,29.162
				c0.231,2.418,2.261,4.265,4.69,4.265h13.608c2.429,0,4.46-1.847,4.69-4.265l2.779-29.162c0.098-1.028-0.242-2.048-0.936-2.811
				C217.812,230.961,216.828,230.526,215.797,230.526z"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default Help;
