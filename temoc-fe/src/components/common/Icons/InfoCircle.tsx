import React from 'react';

const InfoCircle = () => {
  return (
    <svg
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_8099)">
        <path
          d="M6.5 0C3.1838 0 0.5 2.6835 0.5 6C0.5 9.31615 3.1835 12 6.5 12C9.8162 12 12.5 9.3165 12.5 6C12.5 2.68385 9.8165 0 6.5 0ZM6.5 11.1628C3.65323 11.1628 1.33721 8.84679 1.33721 6C1.33721 3.15321 3.65323 0.837211 6.5 0.837211C9.34677 0.837211 11.6628 3.15321 11.6628 6C11.6628 8.84679 9.34677 11.1628 6.5 11.1628Z"
          fill="#666666"
        />
        <path
          d="M6.50014 5.0015C6.14473 5.0015 5.89201 5.15159 5.89201 5.37273V8.38177C5.89201 8.57134 6.14473 8.76085 6.50014 8.76085C6.83975 8.76085 7.11615 8.57134 7.11615 8.38177V5.37268C7.11615 5.15157 6.83975 5.0015 6.50014 5.0015ZM6.50014 3.14551C6.13684 3.14551 5.85254 3.40613 5.85254 3.70625C5.85254 4.00639 6.13686 4.27491 6.50014 4.27491C6.85555 4.27491 7.13989 4.00639 7.13989 3.70625C7.13989 3.40613 6.85552 3.14551 6.50014 3.14551Z"
          fill="#666666"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_8099">
          <rect
            width="12"
            height="12"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default InfoCircle;
