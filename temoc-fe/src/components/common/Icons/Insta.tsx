import React from 'react';
interface iconProps {
  width?: string;
  height?: string;
}

const Insta: React.FC<iconProps> = ({ width, height }: any) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_4537)">
        <path
          d="M7.0483 11.1166C5.07681 11.1166 3.47266 9.51247 3.47266 7.54098C3.47266 5.56949 5.07681 3.96533 7.0483 3.96533C9.01979 3.96533 10.6239 5.56949 10.6239 7.54098C10.6239 9.51288 9.01979 11.1166 7.0483 11.1166ZM7.0483 5.45138C5.8962 5.45138 4.95871 6.38887 4.95871 7.54098C4.95871 8.69309 5.8962 9.63057 7.0483 9.63057C8.20041 9.63057 9.1379 8.69309 9.1379 7.54098C9.1379 6.38887 8.20041 5.45138 7.0483 5.45138Z"
          fill="url(#paint0_linear_2087_4537)"
        />
        <path
          d="M10.5797 4.81117C11.0443 4.81117 11.421 4.43447 11.421 3.96979C11.421 3.50512 11.0443 3.12842 10.5797 3.12842C10.115 3.12842 9.73828 3.50512 9.73828 3.96979C9.73828 4.43447 10.115 4.81117 10.5797 4.81117Z"
          fill="url(#paint1_linear_2087_4537)"
        />
        <path
          d="M9.99511 14.5H4.00448C1.79638 14.5 0 12.7036 0 10.4955V4.50448C0 2.29638 1.79638 0.5 4.00448 0.5H9.99552C12.2036 0.5 14 2.29638 14 4.50448V10.4955C14 12.7032 12.2032 14.5 9.99511 14.5ZM4.00448 1.98564C2.61576 1.98564 1.48605 3.11535 1.48605 4.50407V10.4951C1.48605 11.8838 2.61576 13.0135 4.00448 13.0135H9.99552C11.3842 13.0135 12.5139 11.8838 12.5139 10.4951V4.50448C12.5139 3.11576 11.3842 1.98605 9.99552 1.98605H4.00448V1.98564Z"
          fill="url(#paint2_linear_2087_4537)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_2087_4537"
          x1="1.01137"
          y1="13.578"
          x2="12.025"
          y2="2.5644"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEE411" />
          <stop offset="0.052" stopColor="#FEDB16" />
          <stop offset="0.138" stopColor="#FEC125" />
          <stop offset="0.248" stopColor="#FE983D" />
          <stop offset="0.376" stopColor="#FE5F5E" />
          <stop offset="0.5" stopColor="#FE2181" />
          <stop offset="1" stopColor="#9000DC" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_2087_4537"
          x1="0.991222"
          y1="13.5582"
          x2="12.0049"
          y2="2.54454"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEE411" />
          <stop offset="0.052" stopColor="#FEDB16" />
          <stop offset="0.138" stopColor="#FEC125" />
          <stop offset="0.248" stopColor="#FE983D" />
          <stop offset="0.376" stopColor="#FE5F5E" />
          <stop offset="0.5" stopColor="#FE2181" />
          <stop offset="1" stopColor="#9000DC" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_2087_4537"
          x1="0.966443"
          y1="13.5331"
          x2="11.9801"
          y2="2.51947"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEE411" />
          <stop offset="0.052" stopColor="#FEDB16" />
          <stop offset="0.138" stopColor="#FEC125" />
          <stop offset="0.248" stopColor="#FE983D" />
          <stop offset="0.376" stopColor="#FE5F5E" />
          <stop offset="0.5" stopColor="#FE2181" />
          <stop offset="1" stopColor="#9000DC" />
        </linearGradient>
        <clipPath id="clip0_2087_4537">
          <rect
            width="14"
            height="14"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Insta;
