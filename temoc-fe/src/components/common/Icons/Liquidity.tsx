import React from 'react';

const Liquidity = ({ className }: any) => {
  return (
    <svg
      width="123"
      height="122"
      viewBox="0 0 123 122"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect x="3.5" y="3" width="116" height="116" rx="58" fill="white" />
      <rect
        x="3.5"
        y="3"
        width="116"
        height="116"
        rx="58"
        stroke="#CECECE"
        strokeWidth="6"
      />
      <path
        d="M58.6875 82.0938C45.5062 82.0938 34.7812 71.3688 34.7812 58.1875C34.7812 45.0062 45.5062 34.2812 58.6875 34.2812C65.0719 34.2812 71.0756 36.7675 75.5906 41.2844C75.7288 41.4131 75.8396 41.5684 75.9165 41.7409C75.9933 41.9134 76.0347 42.0996 76.038 42.2884C76.0413 42.4772 76.0066 42.6648 75.9359 42.8399C75.8651 43.015 75.7599 43.174 75.6263 43.3076C75.4928 43.4411 75.3337 43.5464 75.1586 43.6171C74.9835 43.6878 74.796 43.7226 74.6071 43.7192C74.4183 43.7159 74.2321 43.6746 74.0596 43.5977C73.8871 43.5209 73.7319 43.41 73.6031 43.2719C69.6188 39.2875 64.32 37.0938 58.6875 37.0938C47.0569 37.0938 37.5938 46.5569 37.5938 58.1875C37.5938 69.8181 47.0569 79.2812 58.6875 79.2812C59.2781 79.2812 59.8687 79.2578 60.4462 79.2091C61.2028 79.1556 61.8994 79.72 61.965 80.4934C61.9804 80.6776 61.9593 80.8631 61.903 81.0391C61.8467 81.2152 61.7562 81.3784 61.6368 81.5195C61.5173 81.6605 61.3712 81.7767 61.2068 81.8612C61.0424 81.9458 60.863 81.9971 60.6788 82.0122C60.0253 82.0666 59.355 82.0938 58.6875 82.0938ZM80.7384 64.0469C80.5291 64.0473 80.3222 64.001 80.133 63.9112C79.9439 63.8215 79.7771 63.6906 79.6451 63.5281C79.513 63.3656 79.4188 63.1757 79.3696 62.9722C79.3203 62.7687 79.3172 62.5568 79.3603 62.3519C79.6397 61.0188 79.7812 59.6172 79.7812 58.1875C79.7812 54.7825 78.9947 51.5294 77.4441 48.5191C77.3563 48.3547 77.3021 48.1745 77.2846 47.9889C77.267 47.8034 77.2866 47.6163 77.342 47.4383C77.3975 47.2604 77.4877 47.0953 77.6076 46.9526C77.7274 46.8099 77.8744 46.6924 78.04 46.6069C78.2056 46.5215 78.3866 46.4699 78.5723 46.455C78.7581 46.4401 78.9449 46.4623 79.1221 46.5203C79.2992 46.5783 79.463 46.6709 79.604 46.7927C79.745 46.9145 79.8604 47.0632 79.9434 47.23C81.6835 50.6206 82.592 54.3765 82.5938 58.1875C82.5971 59.7805 82.4359 61.3695 82.1128 62.9294C82.0466 63.245 81.8739 63.5283 81.6237 63.7317C81.3735 63.9352 81.0609 64.0464 80.7384 64.0469Z"
        fill="#333333"
      />
      <path
        d="M58.6875 74.594C49.6406 74.594 42.2812 67.2347 42.2812 58.1878C42.2812 49.1409 49.6406 41.7815 58.6875 41.7815C62.1442 41.7751 65.5142 42.8629 68.3146 44.8892C71.1151 46.9155 73.2023 49.7763 74.2772 53.0615C74.3348 53.237 74.3573 53.4221 74.3434 53.6062C74.3295 53.7903 74.2795 53.9699 74.1961 54.1347C74.1128 54.2995 73.9979 54.4463 73.8578 54.5666C73.7178 54.687 73.5554 54.7786 73.38 54.8362C73.2046 54.8938 73.0195 54.9163 72.8354 54.9024C72.6512 54.8885 72.4716 54.8385 72.3068 54.7552C71.974 54.5869 71.7217 54.2934 71.6053 53.939C70.7141 51.2173 68.9845 48.8473 66.664 47.1687C64.3436 45.4901 61.5514 44.5888 58.6875 44.594C51.1922 44.594 45.0937 50.6925 45.0937 58.1878C45.0937 65.6831 51.1922 71.7815 58.6875 71.7815C60.6066 71.7815 62.4487 71.3934 64.1616 70.6284C64.3301 70.5531 64.5118 70.5118 64.6964 70.5067C64.8809 70.5017 65.0646 70.533 65.237 70.599C65.4094 70.6649 65.5671 70.7642 65.7012 70.8911C65.8353 71.018 65.943 71.17 66.0183 71.3386C66.0936 71.5071 66.1349 71.6888 66.14 71.8734C66.145 72.0579 66.1137 72.2416 66.0477 72.414C65.9818 72.5864 65.8825 72.7442 65.7556 72.8782C65.6287 73.0123 65.4767 73.12 65.3081 73.1953C63.2251 74.1247 60.9685 74.6015 58.6875 74.594ZM74.5969 43.6847H69.2934C68.9205 43.6847 68.5628 43.5365 68.2991 43.2728C68.0353 43.0091 67.8872 42.6514 67.8872 42.2784C67.8872 41.9055 68.0353 41.5478 68.2991 41.2841C68.5628 41.0203 68.9205 40.8722 69.2934 40.8722H72.7969L71.9081 37.3162C71.8603 37.1361 71.8487 36.9482 71.8741 36.7636C71.8994 36.5789 71.9613 36.4011 72.056 36.2406C72.1507 36.08 72.2764 35.9399 72.4257 35.8284C72.5751 35.7168 72.7451 35.6361 72.9259 35.5909C73.1067 35.5456 73.2948 35.5368 73.479 35.5649C73.6633 35.593 73.8402 35.6574 73.9993 35.7544C74.1584 35.8515 74.2967 35.9792 74.4061 36.1301C74.5154 36.2811 74.5937 36.4523 74.6362 36.6337L75.9619 41.9362C76.0137 42.1436 76.0177 42.36 75.9734 42.569C75.929 42.7781 75.8377 42.9743 75.7062 43.1428C75.5747 43.3112 75.4065 43.4475 75.2145 43.5412C75.0224 43.635 74.8115 43.6837 74.5978 43.6837L74.5969 43.6847Z"
        fill="#333333"
      />
      <path
        d="M58.6876 67C56.6682 67 54.8335 65.8225 54.1219 64.0694C53.9817 63.7238 53.9845 63.3366 54.1297 62.9931C54.275 62.6496 54.5507 62.3778 54.8963 62.2375C55.2419 62.0973 55.6291 62.1001 55.9726 62.2453C56.3162 62.3906 56.5879 62.6663 56.7282 63.0119C57.0132 63.715 57.8007 64.1866 58.6876 64.1866C60.1276 64.1866 61.2994 63.1966 61.2994 61.9788V61.8016C61.2994 60.1188 58.4794 59.5797 58.4513 59.5741C56.5276 59.2535 53.2632 57.8388 53.2632 54.5735V54.3963C53.2632 51.6279 55.6969 49.376 58.6876 49.376C60.7069 49.376 62.5416 50.5525 63.2532 52.3057C63.3226 52.4768 63.3577 52.6599 63.3563 52.8446C63.355 53.0293 63.3173 53.2119 63.2454 53.382C63.1735 53.5521 63.0688 53.7064 62.9372 53.836C62.8057 53.9656 62.6499 54.0681 62.4788 54.1375C62.3077 54.207 62.1245 54.242 61.9399 54.2407C61.7552 54.2394 61.5726 54.2017 61.4025 54.1297C61.2324 54.0578 61.0781 53.9531 60.9485 53.8216C60.8188 53.6901 60.7164 53.5343 60.6469 53.3632C60.3619 52.66 59.5744 52.1885 58.6876 52.1885C57.2476 52.1885 56.0757 53.1785 56.0757 54.3963V54.5735C56.0757 56.2563 58.8957 56.7954 58.9238 56.801C60.8476 57.1216 64.1119 58.5363 64.1119 61.8016V61.9788C64.1119 64.7472 61.6782 66.9991 58.6876 66.9991V67Z"
        fill="#333333"
      />
      <path
        d="M58.6875 52.1875C58.3145 52.1875 57.9569 52.0393 57.6931 51.7756C57.4294 51.5119 57.2812 51.1542 57.2812 50.7813V48.8125C57.2812 48.4395 57.4294 48.0819 57.6931 47.8181C57.9569 47.5544 58.3145 47.4062 58.6875 47.4062C59.0605 47.4062 59.4181 47.5544 59.6819 47.8181C59.9456 48.0819 60.0938 48.4395 60.0938 48.8125V50.7813C60.0938 51.1542 59.9456 51.5119 59.6819 51.7756C59.4181 52.0393 59.0605 52.1875 58.6875 52.1875ZM58.6875 68.9688C58.3145 68.9688 57.9569 68.8206 57.6931 68.5569C57.4294 68.2931 57.2812 67.9355 57.2812 67.5625V65.5938C57.2812 65.2208 57.4294 64.8631 57.6931 64.5994C57.9569 64.3357 58.3145 64.1875 58.6875 64.1875C59.0605 64.1875 59.4181 64.3357 59.6819 64.5994C59.9456 64.8631 60.0938 65.2208 60.0938 65.5938V67.5625C60.0938 67.9355 59.9456 68.2931 59.6819 68.5569C59.4181 68.8206 59.0605 68.9688 58.6875 68.9688ZM75.5634 87.7188C68.5856 87.7188 62.9081 82.0412 62.9081 75.0634C62.9081 65.0509 74.2987 57.3409 74.7834 57.0175C75.0144 56.8635 75.2858 56.7814 75.5634 56.7814C75.841 56.7814 76.1124 56.8635 76.3434 57.0175C76.8291 57.3409 88.2188 65.05 88.2188 75.0634C88.2188 82.0412 82.5413 87.7188 75.5634 87.7188ZM75.5634 59.9219C73.1091 61.7725 65.7206 67.9375 65.7206 75.0634C65.7206 80.4906 70.1362 84.9062 75.5634 84.9062C80.9906 84.9062 85.4062 80.4906 85.4062 75.0634C85.4062 67.9366 78.0187 61.7716 75.5634 59.9219Z"
        fill="#333333"
      />
      <path
        d="M80.803 71.7811C80.2986 71.7811 79.8092 71.5083 79.5589 71.0292C78.8258 69.637 77.7627 68.1698 76.3977 66.668C76.2734 66.5314 76.1773 66.3718 76.1148 66.1981C76.0523 66.0244 76.0246 65.8401 76.0333 65.6557C76.042 65.4713 76.0869 65.2905 76.1655 65.1235C76.2442 64.9565 76.3549 64.8066 76.4914 64.6823C76.628 64.5581 76.7876 64.462 76.9613 64.3995C77.135 64.337 77.3193 64.3093 77.5036 64.318C77.688 64.3267 77.8689 64.3716 78.0359 64.4502C78.2029 64.5288 78.3528 64.6395 78.477 64.7761C80.0033 66.4542 81.2042 68.1173 82.047 69.7205C82.1599 69.9346 82.2156 70.1743 82.2086 70.4163C82.2016 70.6583 82.1322 70.8944 82.0072 71.1017C81.8821 71.309 81.7056 71.4805 81.4948 71.5995C81.284 71.7185 81.0451 71.7811 80.803 71.7811Z"
        fill="#333333"
      />
    </svg>
  );
};

export default Liquidity;
