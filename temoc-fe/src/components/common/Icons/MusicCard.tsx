import React from 'react';

const MusicCard = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2185_31693)">
        <path
          d="M5.22608 2.58921V11.1061C4.73049 10.664 4.07743 10.3949 3.36258 10.3949C1.81727 10.3949 0.560059 11.6521 0.560059 13.1974C0.560059 14.7428 1.81727 16 3.36258 16C4.90599 16 6.16189 14.7459 6.16508 13.2033H6.16514V6.35858L14.5013 4.24543V8.84526C14.0057 8.40317 13.3526 8.13402 12.6378 8.13402C11.0925 8.13402 9.83526 9.39123 9.83526 10.9365C9.83526 12.4819 11.0925 13.7391 12.6378 13.7391C14.1831 13.7391 15.4403 12.4819 15.4403 10.9365V0L5.22608 2.58921Z"
          fill="#666666"
        />
      </g>
      <defs>
        <clipPath id="clip0_2185_31693">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default MusicCard;
