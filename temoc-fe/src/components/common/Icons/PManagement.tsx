import React from 'react';
interface Iprops {
  className?: any;
}
const PManagement = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2058_2036)">
        <path
          d="M11.7858 3.91072H2.21423C0.991346 3.91072 0 4.90206 0 6.12495V11.0281C0 12.251 0.991346 13.2426 2.21423 13.2426H11.7858C13.0087 13.2426 14 12.2512 14 11.0281V6.12495C14 4.90206 13.0087 3.91072 11.7858 3.91072ZM8.98098 6.62719C8.98098 6.82588 8.84353 6.9954 8.65693 7.0251L7.70054 7.17779V10.339C7.70054 10.8281 7.3389 11.2454 6.83088 11.4076C6.68423 11.4544 6.52548 11.4801 6.35965 11.4801C5.61903 11.4801 5.01875 10.9692 5.01875 10.339C5.01875 9.70872 5.61903 9.19754 6.35965 9.19754C6.52548 9.19754 6.68423 9.2233 6.83088 9.27008V6.25373C6.83088 6.08001 6.95729 5.93178 7.12865 5.90445L8.5426 5.67869C8.77309 5.64189 8.98072 5.8306 8.98072 6.07659V6.62772L8.98098 6.62719ZM0.915655 2.02606C1.42473 1.49044 2.17429 1.1514 3.00899 1.1514H10.9905C11.8226 1.1514 12.57 1.48833 13.0788 2.0208C13.1093 2.0526 13.1627 2.03131 13.1627 1.98742C13.1627 0.889898 12.1902 0 10.9905 0H3.00899C1.80923 0 0.83681 0.889898 0.83681 1.98742C0.83681 2.03394 0.885431 2.05786 0.915392 2.02632L0.915655 2.02606Z"
          fill="currentColor"
        />
        <path
          d="M0.730427 3.8797C1.25948 3.40978 1.99589 3.11779 2.80931 3.11779H11.1908C12.0042 3.11779 12.7406 3.40978 13.2697 3.8797C13.3504 3.95145 13.4718 3.89047 13.4718 3.77746C13.4718 2.67994 12.4505 1.79004 11.1908 1.79004H2.80931C1.54963 1.79004 0.52832 2.67994 0.52832 3.77746C0.52832 3.89047 0.649742 3.95145 0.730427 3.8797Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2058_2036">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PManagement;
