import React from 'react';

const Podcast = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 6.66667C14 6.48986 13.9298 6.32029 13.8047 6.19526C13.6797 6.07024 13.5101 6 13.3333 6C13.1565 6 12.987 6.07024 12.8619 6.19526C12.7369 6.32029 12.6667 6.48986 12.6667 6.66667C12.6667 7.90434 12.175 9.09133 11.2998 9.9665C10.4247 10.8417 9.23768 11.3333 8 11.3333C6.76232 11.3333 5.57534 10.8417 4.70017 9.9665C3.825 9.09133 3.33333 7.90434 3.33333 6.66667C3.33333 6.48986 3.2631 6.32029 3.13807 6.19526C3.01305 6.07024 2.84348 6 2.66667 6C2.48986 6 2.32029 6.07024 2.19526 6.19526C2.07024 6.32029 2 6.48986 2 6.66667C2.002 8.14174 2.5467 9.56451 3.53028 10.6638C4.51387 11.7631 5.86756 12.462 7.33333 12.6273V14.6667H6C5.82319 14.6667 5.65362 14.7369 5.5286 14.8619C5.40357 14.987 5.33333 15.1565 5.33333 15.3333C5.33333 15.5101 5.40357 15.6797 5.5286 15.8047C5.65362 15.9298 5.82319 16 6 16H10C10.1768 16 10.3464 15.9298 10.4714 15.8047C10.5964 15.6797 10.6667 15.5101 10.6667 15.3333C10.6667 15.1565 10.5964 14.987 10.4714 14.8619C10.3464 14.7369 10.1768 14.6667 10 14.6667H8.66667V12.6273C10.1324 12.462 11.4861 11.7631 12.4697 10.6638C13.4533 9.56451 13.998 8.14174 14 6.66667Z"
        fill="#666666"
      />
      <path
        d="M7.99984 10C8.88357 9.99894 9.7308 9.64741 10.3557 9.02252C10.9806 8.39763 11.3321 7.5504 11.3332 6.66667V3.33333C11.3332 2.44928 10.982 1.60143 10.3569 0.976311C9.73174 0.351189 8.88389 0 7.99984 0C7.11578 0 6.26794 0.351189 5.64281 0.976311C5.01769 1.60143 4.6665 2.44928 4.6665 3.33333V6.66667C4.66756 7.5504 5.01909 8.39763 5.64398 9.02252C6.26888 9.64741 7.11611 9.99894 7.99984 10Z"
        fill="#666666"
      />
    </svg>
  );
};

export default Podcast;
