import React from 'react';

const Position = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2185_46327)">
        <path
          d="M12.1702 6.53516H7.07548C6.67351 6.53516 6.34766 6.86101 6.34766 7.26298V17.8164C6.34766 18.2183 6.67351 18.5442 7.07548 18.5442H12.1702C12.5722 18.5442 12.898 18.2183 12.898 17.8164V7.26298C12.898 6.86101 12.5722 6.53516 12.1702 6.53516Z"
          fill="#FFBD00"
        />
        <path
          d="M4.34676 10.1748H1.07157C0.669606 10.1748 0.34375 10.5007 0.34375 10.9026V17.8169C0.34375 18.2189 0.669606 18.5447 1.07157 18.5447H4.34676C4.74872 18.5447 5.07458 18.2189 5.07458 17.8169V10.9026C5.07458 10.5007 4.74872 10.1748 4.34676 10.1748Z"
          fill="#FFDE50"
        />
        <path
          d="M18.1749 8.71875H14.8997C14.4977 8.71875 14.1719 9.04461 14.1719 9.44657V17.8165C14.1719 18.2185 14.4977 18.5443 14.8997 18.5443H18.1749C18.5768 18.5443 18.9027 18.2185 18.9027 17.8165V9.44657C18.9027 9.04461 18.5768 8.71875 18.1749 8.71875Z"
          fill="#FFDE50"
        />
        <path
          d="M12.6949 1.47991L11.2866 2.04251L10.0762 0.228056C10.0235 0.157286 9.95501 0.0998111 9.87616 0.0602157C9.7973 0.0206202 9.71029 0 9.62206 0C9.53382 0 9.44681 0.0206202 9.36795 0.0602157C9.2891 0.0998111 9.22059 0.157286 9.1679 0.228056L7.95753 2.04251L6.5492 1.47991C6.45588 1.44261 6.35407 1.43181 6.255 1.44868C6.15592 1.46556 6.06343 1.50945 5.98771 1.57554C5.91199 1.64162 5.85599 1.72732 5.82587 1.8232C5.79575 1.91909 5.79269 2.02142 5.81701 2.11893L6.54483 5.03021C6.57439 5.14834 6.64259 5.25321 6.7386 5.32811C6.83461 5.40302 6.95291 5.44368 7.07469 5.44361H12.1694C12.2912 5.44368 12.4095 5.40302 12.5055 5.32811C12.6015 5.25321 12.6697 5.14834 12.6993 5.03021L13.4271 2.11893C13.4514 2.02142 13.4484 1.91909 13.4182 1.8232C13.3881 1.72732 13.3321 1.64162 13.2564 1.57554C13.1807 1.50945 13.0882 1.46556 12.9891 1.44868C12.89 1.43181 12.7882 1.44261 12.6949 1.47991Z"
          fill="#FFBD00"
        />
        <path
          d="M9.62264 13.4493C9.47787 13.4493 9.33903 13.3918 9.23666 13.2894C9.13429 13.187 9.07678 13.0482 9.07678 12.9034V10.8706C8.94582 10.9167 8.80222 10.9112 8.6752 10.8552C8.54817 10.7992 8.44726 10.6968 8.39301 10.569C8.33876 10.4413 8.33524 10.2976 8.38317 10.1673C8.4311 10.037 8.52687 9.92986 8.651 9.86768L9.37882 9.50377C9.46204 9.46222 9.5545 9.44263 9.64742 9.44685C9.74034 9.45107 9.83064 9.47897 9.90975 9.5279C9.98886 9.57682 10.0542 9.64515 10.0994 9.7264C10.1447 9.80765 10.1685 9.89912 10.1685 9.99214V12.9034C10.1685 13.0482 10.111 13.187 10.0086 13.2894C9.90626 13.3918 9.76741 13.4493 9.62264 13.4493Z"
          fill="#F98824"
        />
        <path
          d="M10.1688 13.4492H9.07711C8.93234 13.4492 8.7935 13.3916 8.69113 13.2893C8.58876 13.1869 8.53125 13.0481 8.53125 12.9033C8.53125 12.7585 8.58876 12.6197 8.69113 12.5173C8.7935 12.4149 8.93234 12.3574 9.07711 12.3574H10.1688C10.3136 12.3574 10.4525 12.4149 10.5548 12.5173C10.6572 12.6197 10.7147 12.7585 10.7147 12.9033C10.7147 13.0481 10.6572 13.1869 10.5548 13.2893C10.4525 13.3916 10.3136 13.4492 10.1688 13.4492Z"
          fill="#F98824"
        />
        <path
          d="M19.2664 17.4521H-0.0208559C-0.221838 17.4521 -0.384766 17.6151 -0.384766 17.8161V19.6356C-0.384766 19.8366 -0.221838 19.9995 -0.0208559 19.9995H19.2664C19.4673 19.9995 19.6303 19.8366 19.6303 19.6356V17.8161C19.6303 17.6151 19.4673 17.4521 19.2664 17.4521Z"
          fill="#F98824"
        />
        <path
          d="M12.897 5.44329H6.34665C6.20187 5.44329 6.06303 5.38578 5.96066 5.28341C5.85829 5.18104 5.80078 5.0422 5.80078 4.89743C5.80078 4.75265 5.85829 4.61381 5.96066 4.51144C6.06303 4.40907 6.20187 4.35156 6.34665 4.35156H12.897C13.0418 4.35156 13.1806 4.40907 13.283 4.51144C13.3854 4.61381 13.4429 4.75265 13.4429 4.89743C13.4429 5.0422 13.3854 5.18104 13.283 5.28341C13.1806 5.38578 13.0418 5.44329 12.897 5.44329Z"
          fill="#F98824"
        />
      </g>
      <defs>
        <clipPath id="clip0_2185_46327">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Position;
