import React from 'react';
interface Iprops {
  className?: any;
}
const PublishEvents = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.875 7.875H6.125C5.64175 7.875 5.25 8.26675 5.25 8.75V12.25C5.25 12.7332 5.64175 13.125 6.125 13.125H7.875C8.35825 13.125 8.75 12.7332 8.75 12.25V8.75C8.75 8.26675 8.35825 7.875 7.875 7.875Z"
        fill="currentColor"
      />
      <path
        d="M3.5 9.1875H1.75C1.26675 9.1875 0.875 9.57925 0.875 10.0625V12.25C0.875 12.7332 1.26675 13.125 1.75 13.125H3.5C3.98325 13.125 4.375 12.7332 4.375 12.25V10.0625C4.375 9.57925 3.98325 9.1875 3.5 9.1875Z"
        fill="currentColor"
      />
      <path
        d="M12.25 5.6875H10.5C10.0168 5.6875 9.625 6.07925 9.625 6.5625V12.25C9.625 12.7332 10.0168 13.125 10.5 13.125H12.25C12.7332 13.125 13.125 12.7332 13.125 12.25V6.5625C13.125 6.07925 12.7332 5.6875 12.25 5.6875Z"
        fill="currentColor"
      />
      <path
        d="M2.62503 7.4375C2.66147 7.43747 2.69777 7.43291 2.73309 7.42394C4.29902 7.02501 5.8057 6.42192 7.2144 5.63019C8.74797 4.76358 10.1491 3.68125 11.375 2.41631V3.5C11.375 3.61603 11.4211 3.72731 11.5032 3.80936C11.5852 3.89141 11.6965 3.9375 11.8125 3.9375C11.9286 3.9375 12.0398 3.89141 12.1219 3.80936C12.2039 3.72731 12.25 3.61603 12.25 3.5V1.3125C12.25 1.19647 12.2039 1.08519 12.1219 1.00314C12.0398 0.921094 11.9286 0.875 11.8125 0.875H9.62503C9.50899 0.875 9.39771 0.921094 9.31567 1.00314C9.23362 1.08519 9.18753 1.19647 9.18753 1.3125C9.18753 1.42853 9.23362 1.53981 9.31567 1.62186C9.39771 1.70391 9.50899 1.75 9.62503 1.75H10.7958C9.61775 2.97889 8.26683 4.02944 6.78565 4.8685C5.44372 5.62195 4.00852 6.19562 2.51696 6.57475C2.41242 6.59999 2.32084 6.66285 2.2597 6.75132C2.19856 6.8398 2.17216 6.9477 2.18552 7.05441C2.19889 7.16112 2.25109 7.25917 2.33216 7.32983C2.41323 7.4005 2.51749 7.43883 2.62503 7.4375Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default PublishEvents;
