import React from 'react';

const PublishNews = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.81953 9.62791C6.16558 9.62791 4.00558 7.46791 4.00558 4.81395C4.00558 2.16 6.16558 0 8.81953 0C11.4735 0 13.6335 2.16 13.6335 4.81395C13.6335 7.46791 11.4735 9.62791 8.81953 9.62791ZM8.81953 1.25581C6.86047 1.25581 5.2614 2.85488 5.2614 4.81395C5.2614 6.77302 6.86047 8.37209 8.81953 8.37209C10.7786 8.37209 12.3777 6.77302 12.3777 4.81395C12.3777 2.85488 10.7786 1.25581 8.81953 1.25581ZM16.0112 18C15.6679 18 15.3833 17.7153 15.3833 17.3721C15.3833 14.4837 12.4363 12.1395 8.81953 12.1395C5.20279 12.1395 2.25581 14.4837 2.25581 17.3721C2.25581 17.7153 1.97116 18 1.62791 18C1.28465 18 1 17.7153 1 17.3721C1 13.7972 4.50791 10.8837 8.81953 10.8837C13.1312 10.8837 16.6391 13.7972 16.6391 17.3721C16.6391 17.7153 16.3544 18 16.0112 18Z"
        fill="#666666"
      />
    </svg>
  );
};

export default PublishNews;
