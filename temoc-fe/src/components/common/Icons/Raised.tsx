import React from 'react';

const Raised = () => {
  return (
    <svg
      width="12"
      height="14"
      viewBox="0 0 12 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.73347 3.04617C7.79847 2.15152 8.5101 0.135982 8.07216 0.0478268C7.49026 -0.0693504 6.22664 0.444457 5.61589 0.534984C4.74962 0.639577 3.80614 -0.404378 3.27656 0.177522C2.84596 0.650646 3.58526 2.37124 4.74191 3.12082C1.2911 4.81685 -3.55926 13.331 4.90244 13.9488C16.6107 14.8036 10.7479 4.69891 6.73347 3.04617ZM7.73898 9.76086C7.70297 10.0928 7.52706 10.393 7.26411 10.5967C6.98647 10.8119 6.63823 10.912 6.29223 10.94V11.3068C6.29223 11.4049 6.24937 11.4998 6.17591 11.5648C6.07494 11.6541 5.92657 11.6762 5.80402 11.6197C5.68302 11.564 5.60353 11.4399 5.60353 11.3068V10.9059C5.5442 10.8948 5.48534 10.8812 5.42709 10.8653C5.10372 10.7764 4.80378 10.6033 4.58468 10.347C4.47554 10.2193 4.38656 10.0744 4.32502 9.91796C4.30895 9.87705 4.29462 9.83547 4.28207 9.79334C4.2707 9.75522 4.25897 9.71648 4.25492 9.67675C4.2482 9.60982 4.26122 9.54238 4.29238 9.48275C4.35622 9.36064 4.49076 9.28688 4.62813 9.29913C4.76333 9.31112 4.88182 9.40494 4.92438 9.53382C4.9375 9.57351 4.94639 9.6142 4.96112 9.65346C4.97566 9.69251 4.99337 9.73031 5.01405 9.76649C5.05514 9.83777 5.10604 9.90292 5.16526 9.96003C5.28695 10.0773 5.44195 10.1535 5.60353 10.1988V8.83622C5.28758 8.75442 4.96145 8.64878 4.70054 8.44592C4.57374 8.34728 4.46615 8.22474 4.39569 8.07969C4.32133 7.92661 4.29053 7.75606 4.28928 7.58667C4.288 7.41468 4.32068 7.2442 4.39243 7.08749C4.45963 6.94076 4.55638 6.80902 4.67501 6.69982C4.92867 6.46635 5.2656 6.33613 5.60356 6.2883V5.90758C5.60356 5.80948 5.64642 5.71454 5.71989 5.64957C5.82089 5.56026 5.9692 5.53819 6.09178 5.59466C6.21274 5.65036 6.29227 5.77443 6.29227 5.90758V6.28622C6.3373 6.29189 6.3822 6.29868 6.42687 6.30678C6.75873 6.36683 7.08332 6.50388 7.32601 6.74314C7.44137 6.85691 7.5347 6.99103 7.60128 7.13875C7.61973 7.17984 7.63597 7.22189 7.64993 7.26472C7.66298 7.30468 7.67609 7.34583 7.68232 7.38753C7.69218 7.45435 7.68221 7.5226 7.65362 7.5838C7.59525 7.70859 7.46433 7.78805 7.3266 7.78215C7.19127 7.77626 7.06866 7.68813 7.0202 7.56163C7.00577 7.52401 6.99856 7.48419 6.98383 7.44666C6.96877 7.40871 6.94992 7.37238 6.92757 7.33822C6.88448 7.27167 6.82864 7.21462 6.76489 7.16761C6.62686 7.06568 6.45945 7.01258 6.29217 6.9829V8.28683C6.49098 8.33444 6.68995 8.38636 6.88042 8.46146C7.16831 8.57498 7.44111 8.74905 7.59825 9.02353C7.57387 8.98077 7.55015 8.9391 7.59907 9.02495C7.6471 9.10928 7.62417 9.06909 7.60006 9.02683C7.72508 9.24715 7.76616 9.51059 7.73898 9.76086Z"
        fill="#666666"
      />
    </svg>
  );
};

export default Raised;
