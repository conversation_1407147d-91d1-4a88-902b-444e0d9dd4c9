import React from 'react';

const Rank2 = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2185_31917)">
        <path
          d="M5.72515 9.89481C5.58537 9.89481 5.47168 9.78109 5.47168 9.64131V9.41597C5.47168 9.32659 5.49068 9.22862 5.52818 9.12469C5.56606 9.01962 5.63052 8.92281 5.71974 8.83694L7.85302 6.69875C8.03362 6.51816 8.1949 6.34275 8.33243 6.17734C8.46943 6.01266 8.58524 5.84891 8.67662 5.69056C8.76746 5.53316 8.83655 5.37122 8.88193 5.20925C8.92724 5.0475 8.95021 4.87512 8.95021 4.69691C8.95021 4.38634 8.8699 4.14772 8.71143 3.98762C8.55299 3.82772 8.3253 3.74666 8.03471 3.74666C7.90665 3.74666 7.78743 3.76525 7.6804 3.80194C7.57327 3.83875 7.47612 3.88975 7.39165 3.95347C7.29871 4.02369 7.22727 4.09741 7.17334 4.17878C7.03055 4.39434 6.98252 4.55109 6.93612 4.70269C6.91971 4.75622 6.90277 4.81156 6.88202 4.868C6.87456 4.88845 6.86101 4.90612 6.8432 4.91864C6.82539 4.93115 6.80417 4.93791 6.7824 4.938C6.77602 4.938 6.76962 4.93744 6.76318 4.93625L5.67818 4.73672C5.62134 4.72625 5.58305 4.67175 5.5928 4.61522C5.6483 4.292 5.7458 4.00206 5.88259 3.75347C6.02934 3.48653 6.21568 3.26122 6.43646 3.08387C6.65699 2.90681 6.91268 2.77222 7.19637 2.68375C7.47946 2.59553 7.78821 2.55078 8.11409 2.55078C8.45687 2.55078 8.77356 2.60234 9.05546 2.70406C9.33777 2.80603 9.58262 2.95009 9.78321 3.13228C9.98424 3.31494 10.1422 3.53822 10.2526 3.79597C10.3631 4.05356 10.419 4.34169 10.419 4.65225C10.419 4.91928 10.38 5.16984 10.3031 5.397C10.2255 5.62553 10.1202 5.84371 9.98965 6.04669C9.85846 6.25078 9.70465 6.45169 9.53249 6.64378C9.35464 6.84181 9.1723 7.03575 8.98562 7.22547L7.69827 8.54206C7.69555 8.54474 7.6937 8.54818 7.69299 8.55194C7.69227 8.55569 7.69272 8.55957 7.69427 8.56306C7.69568 8.56662 7.69815 8.56967 7.70134 8.57178C7.70453 8.5739 7.70829 8.57499 7.71212 8.57491H10.3803C10.4195 8.57495 10.4571 8.59054 10.4848 8.61827C10.5126 8.646 10.5282 8.6836 10.5282 8.72281V9.74697C10.5282 9.78618 10.5126 9.82378 10.4848 9.85151C10.4571 9.87924 10.4195 9.89483 10.3803 9.89487H5.72515V9.89481Z"
          fill="#FD9727"
        />
        <path
          d="M9.06641 7.14292C9.22499 6.97984 9.38035 6.81365 9.53241 6.64445C9.70459 6.45236 9.85841 6.25145 9.98956 6.04736C10.1201 5.84438 10.2254 5.6262 10.3031 5.39767C10.3799 5.17052 10.4189 4.91995 10.4189 4.65292C10.4189 4.34236 10.363 4.05423 10.2526 3.79664C10.1421 3.53889 9.98412 3.31561 9.78312 3.13295C9.58522 2.95317 9.34403 2.8107 9.06644 2.70898V7.14292H9.06641ZM10.5281 9.74761V8.72345C10.5281 8.68424 10.5125 8.64664 10.4847 8.61891C10.457 8.59118 10.4194 8.57559 10.3802 8.57555H9.06641V9.89552H10.3802C10.4194 9.89547 10.457 9.87988 10.4847 9.85215C10.5125 9.82442 10.5281 9.78682 10.5281 9.74761Z"
          fill="#FF6D3A"
        />
        <path
          d="M1.22179 3.88937C1.62542 3.449 2.04842 3.05272 2.51413 2.68481C2.56892 2.64156 2.64776 2.69334 2.62942 2.76069C2.36838 3.72015 1.91229 4.66834 1.20917 5.37322C1.1776 4.87975 1.17713 4.38216 1.22179 3.88937ZM1.20923 5.37359C0.803291 4.88372 0.454478 4.37725 0.140135 3.83044C0.104197 3.7679 0.00860335 3.79022 0.00494711 3.86225C-0.0517404 4.98234 0.376228 6.29294 1.41888 6.82153C1.31498 6.34519 1.2401 5.86047 1.20923 5.37359Z"
          fill="#FD9727"
        />
        <path
          d="M1.41865 6.82124C1.67412 6.29561 1.96009 5.80655 2.29525 5.33249C2.33503 5.27624 2.42418 5.30202 2.42709 5.37083C2.46793 6.3384 2.3019 7.36121 1.83828 8.21055C1.66647 7.75849 1.52147 7.29415 1.41865 6.82124ZM1.8384 8.21093C1.31131 7.87218 0.829778 7.50174 0.367903 7.08571C0.314622 7.03771 0.231028 7.08783 0.249091 7.15721C0.527341 8.22708 1.32306 9.32499 2.45597 9.5189C2.22047 9.09833 2.0095 8.66186 1.8384 8.21093Z"
          fill="#FD9727"
        />
        <path
          d="M2.45618 9.51864C2.54853 8.94517 2.68059 8.39814 2.86443 7.85123C2.88631 7.78617 2.97865 7.78492 3.00156 7.84961C3.32309 8.75736 3.45334 9.78145 3.25934 10.7226C2.96449 10.341 2.69168 9.93989 2.45618 9.51864ZM3.25959 10.7229C2.65871 10.5527 2.09109 10.3398 1.52887 10.0789C1.46393 10.0487 1.39884 10.121 1.43628 10.182C2.01431 11.1246 3.09649 11.9408 4.23634 11.8001C3.88643 11.4649 3.55578 11.1068 3.25959 10.7229Z"
          fill="#FD9727"
        />
        <path
          d="M4.2359 11.7997C4.1539 11.222 4.11908 10.6585 4.13518 10.0792C4.13708 10.0106 4.22518 9.98241 4.26593 10.0377C4.84165 10.8182 5.27112 11.7663 5.3739 12.7276C4.97358 12.4463 4.58921 12.1387 4.2359 11.7997ZM14.7783 3.88937C14.3747 3.449 13.9517 3.05272 13.486 2.68481C13.4312 2.64156 13.3523 2.69334 13.3707 2.76069C13.6317 3.72015 14.0878 4.66834 14.7909 5.37322C14.8225 4.87975 14.823 4.38216 14.7783 3.88937ZM14.7909 5.37359C15.1968 4.88372 15.5456 4.37725 15.86 3.83044C15.8959 3.7679 15.9915 3.79022 15.9951 3.86225C16.0518 4.98234 15.6239 6.29294 14.5812 6.82153C14.6851 6.34519 14.76 5.86047 14.7909 5.37359Z"
          fill="#FD9727"
        />
        <path
          d="M14.5813 6.82124C14.3258 6.29561 14.0398 5.80655 13.7047 5.33249C13.6649 5.27624 13.5757 5.30202 13.5728 5.37083C13.532 6.3384 13.698 7.36121 14.1616 8.21055C14.3334 7.75849 14.4784 7.29415 14.5813 6.82124ZM14.1615 8.21093C14.6886 7.87218 15.1701 7.50174 15.632 7.08571C15.6853 7.03771 15.7689 7.08783 15.7508 7.15721C15.4726 8.22708 14.6769 9.32499 13.5439 9.5189C13.7794 9.09833 13.9904 8.66186 14.1615 8.21093Z"
          fill="#FD9727"
        />
        <path
          d="M13.5443 9.51864C13.4519 8.94517 13.3199 8.39814 13.136 7.85123C13.1142 7.78617 13.0218 7.78492 12.9989 7.84961C12.6774 8.75736 12.5471 9.78145 12.7411 10.7226C13.036 10.341 13.3088 9.93989 13.5443 9.51864ZM12.7409 10.7229C13.3418 10.5527 13.9094 10.3398 14.4716 10.0789C14.5366 10.0487 14.6016 10.121 14.5642 10.182C13.9862 11.1246 12.904 11.9408 11.7642 11.8001C12.114 11.4649 12.4447 11.1068 12.7409 10.7229Z"
          fill="#FD9727"
        />
        <path
          d="M11.764 11.8001C11.846 11.2223 11.8808 10.6588 11.8647 10.0796C11.8628 10.0109 11.7747 9.98278 11.7339 10.0381C11.1582 10.8186 10.7288 11.7667 10.626 12.728C11.0263 12.4467 11.4107 12.1391 11.764 11.8001Z"
          fill="#FD9727"
        />
        <path
          d="M15.0108 3.90937C15.0108 3.90462 15.0108 3.8999 15.0105 3.89512C14.9553 3.16052 14.8199 2.42412 14.608 1.7064C14.5899 1.64498 14.5481 1.59328 14.4919 1.56267C14.4356 1.53206 14.3695 1.52505 14.3081 1.54318C14.2467 1.5613 14.195 1.60309 14.1644 1.65934C14.1337 1.71559 14.1267 1.78169 14.1448 1.84312C14.345 2.52121 14.4734 3.21652 14.5271 3.91008C14.5272 3.91527 14.5272 3.92046 14.5277 3.92571C14.5684 4.37412 14.5724 4.86002 14.5398 5.36999C14.5398 5.37019 14.5398 5.37039 14.5398 5.37058C14.5117 5.81337 14.443 6.28749 14.3356 6.7798L14.3355 6.77993C14.242 7.21002 14.1045 7.66515 13.9268 8.13268L13.9266 8.13312C13.7708 8.54368 13.5685 8.97218 13.3252 9.40668C13.1106 9.7904 12.8475 10.1848 12.5429 10.5791L12.5428 10.5793L12.5426 10.5795C12.2727 10.9293 11.9527 11.2821 11.5916 11.6281L11.5916 11.6281C11.2698 11.9369 10.8971 12.2407 10.4836 12.5312C10.4821 12.5323 10.4808 12.5335 10.4793 12.5346C10.4779 12.5356 10.4764 12.5365 10.4751 12.5375C9.77957 13.0719 9.42713 13.2052 9.054 13.3462C8.75847 13.458 8.45679 13.5723 8.00029 13.8766C7.54379 13.5723 7.2421 13.458 6.94657 13.3462C6.57344 13.2051 6.221 13.0719 5.5255 12.5375C5.52413 12.5365 5.52263 12.5356 5.52122 12.5346C5.51975 12.5335 5.51841 12.5323 5.51691 12.5312C5.10347 12.2407 4.73072 11.9369 4.40894 11.6281L4.40891 11.6281C4.04782 11.2821 3.72788 10.9293 3.45794 10.5795L3.45779 10.5793L3.45763 10.5791C3.15307 10.1848 2.88988 9.7904 2.67538 9.40668C2.43204 8.97218 2.22969 8.54371 2.07391 8.13315L2.07388 8.13312L2.07372 8.13268C1.89597 7.66515 1.75844 7.21002 1.66491 6.77993L1.66488 6.7798C1.55744 6.28749 1.48875 5.81333 1.46069 5.37058L1.46063 5.36999C1.428 4.86005 1.43207 4.37412 1.47275 3.92571C1.47322 3.92046 1.47325 3.91527 1.47338 3.91008C1.527 3.21652 1.65541 2.52124 1.8556 1.84312C1.87372 1.78169 1.86671 1.71559 1.83609 1.65934C1.80548 1.60309 1.75377 1.5613 1.69235 1.54318C1.56457 1.50533 1.43016 1.57849 1.39241 1.7064C1.18054 2.42412 1.04513 3.16052 0.989942 3.89512C0.989567 3.8999 0.98966 3.90462 0.989598 3.90937C0.948973 4.37483 0.945129 4.87633 0.978723 5.40083V5.40118C1.00835 5.86783 1.08041 6.3659 1.19285 6.88155L1.193 6.88258C1.29163 7.33599 1.436 7.81427 1.62219 8.30412C1.62225 8.30424 1.62229 8.30436 1.62232 8.30449C1.78647 8.73702 1.99885 9.18705 2.25363 9.64205L2.25379 9.64233C2.47991 10.0468 2.75635 10.4613 3.07547 10.8743L3.07557 10.8744L3.0756 10.8745C3.36022 11.2434 3.69644 11.6143 4.07494 11.9769L4.07525 11.9772C4.41347 12.3016 4.80375 12.62 5.23529 12.9235C5.98472 13.4988 6.38688 13.6509 6.77572 13.7979C7.01775 13.8894 7.25335 13.9787 7.5795 14.1793C7.32982 14.3706 7.04063 14.6108 6.6975 14.9178C6.66105 14.9504 6.63535 14.9933 6.62382 15.0408C6.61229 15.0884 6.61546 15.1383 6.63293 15.184C6.65039 15.2296 6.68132 15.2689 6.72162 15.2967C6.76192 15.3244 6.80969 15.3392 6.8586 15.3392C6.91799 15.3392 6.97531 15.3173 7.0195 15.2776C7.41738 14.9216 7.73538 14.6614 8.00022 14.4657C8.26504 14.6614 8.58304 14.9216 8.98094 15.2776C9.02514 15.3173 9.08246 15.3392 9.14185 15.3392C9.17584 15.3392 9.20945 15.3321 9.24048 15.3182C9.27151 15.3043 9.29925 15.2841 9.32188 15.2587C9.34303 15.2351 9.35931 15.2075 9.36981 15.1776C9.3803 15.1477 9.3848 15.116 9.38304 15.0843C9.38128 15.0527 9.3733 15.0217 9.35956 14.9931C9.34582 14.9645 9.32658 14.9389 9.30294 14.9178C8.95985 14.6107 8.67063 14.3706 8.42094 14.1793C8.74707 13.9787 8.98269 13.8894 9.22472 13.7979C9.6136 13.6509 10.0157 13.4988 10.7652 12.9235C11.1967 12.62 11.587 12.3016 11.9252 11.9772L11.9255 11.9769C12.304 11.6143 12.6402 11.2434 12.9248 10.8745L12.9249 10.8744L12.925 10.8743C13.2441 10.4613 13.5205 10.0468 13.7467 9.64233L13.7468 9.64205C14.0016 9.18705 14.214 8.73702 14.3781 8.30449L14.3783 8.30412C14.5644 7.81427 14.7088 7.33599 14.8074 6.88258L14.8076 6.88155C14.9201 6.3659 14.9921 5.86783 15.0217 5.40118V5.40083C15.0553 4.87633 15.0515 4.37483 15.0108 3.90937Z"
          fill="#FF6D3A"
        />
      </g>
      <defs>
        <clipPath id="clip0_2185_31917">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.436523)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Rank2;
