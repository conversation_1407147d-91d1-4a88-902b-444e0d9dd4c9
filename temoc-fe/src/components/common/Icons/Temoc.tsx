import React from 'react';

const Temoc = () => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.50542 10.1184L10.592 2.12801L2.8403 10.4436C2.76486 10.5245 2.64279 10.5411 2.54819 10.4833C2.41875 10.4042 2.39772 10.2248 2.50542 10.1184Z"
        fill="#212428"
      />
      <path
        d="M4.68133 11.0524L14.9929 0.500054L5.12379 11.4673C5.02504 11.5771 4.86139 11.5994 4.73644 11.5203C4.57349 11.417 4.54676 11.1901 4.68133 11.0524Z"
        fill="#212428"
      />
      <path
        d="M4.89589 12.8727L12.2612 4.74349L5.23585 13.1682C5.16754 13.2501 5.05097 13.2727 4.95679 13.2222C4.82769 13.153 4.79773 12.9811 4.89589 12.8727Z"
        fill="#212428"
      />
      <path
        d="M8.19519 11.3703L15.7199 3.66989L8.51807 11.673C8.44601 11.7531 8.32658 11.7694 8.23541 11.7117C8.1165 11.6363 8.09699 11.4708 8.19519 11.3703Z"
        fill="#212428"
      />
      <path
        d="M5.37027 8.64037L11.7802 2.08079L5.64531 8.89829C5.58392 8.9665 5.48219 8.98041 5.40453 8.9312C5.30323 8.86702 5.28662 8.72597 5.37027 8.64037Z"
        fill="#212428"
      />
      <path
        d="M9.97735 12.694C10.189 12.7666 10.3042 12.9967 10.2186 13.2034C9.9929 13.748 9.6806 14.2519 9.29346 14.6945C8.8181 15.238 8.23976 15.6779 7.59324 15.9876C6.94673 16.2973 6.24542 16.4704 5.53154 16.4965C4.81765 16.5227 4.10596 16.4013 3.4393 16.1397C2.77265 15.8782 2.16482 15.4818 1.65241 14.9744C1.14 14.4671 0.733616 13.8594 0.457714 13.1877C0.181813 12.5161 0.0421042 11.7945 0.0469993 11.0664C0.0509613 10.4771 0.149588 9.89338 0.338109 9.33822C0.411437 9.12228 0.656767 9.02666 0.865249 9.11908C1.06965 9.2097 1.16148 9.44726 1.09199 9.65978C0.943297 10.1145 0.865521 10.5911 0.862287 11.0721C0.858146 11.6881 0.97634 12.2986 1.20976 12.8668C1.44317 13.435 1.78698 13.9491 2.22048 14.3783C2.65398 14.8075 3.16821 15.1429 3.7322 15.3642C4.2962 15.5855 4.8983 15.6882 5.50225 15.666C6.10621 15.6439 6.69952 15.4974 7.24648 15.2354C7.79343 14.9735 8.28272 14.6013 8.68487 14.1415C8.99671 13.785 9.25116 13.3814 9.43961 12.9459C9.52993 12.7372 9.76224 12.6202 9.97735 12.694Z"
        fill="#212428"
      />
      <path
        d="M1.35063 8.21555C1.16941 8.08441 1.12543 7.83112 1.26664 7.65763C1.65245 7.18363 2.11503 6.78045 2.63422 6.46558C2.8318 6.34577 3.0842 6.43257 3.18474 6.64061C3.28315 6.84425 3.19865 7.08755 3.00664 7.20708C2.60703 7.45586 2.24815 7.76699 1.94312 8.12907C1.79528 8.30456 1.53652 8.35007 1.35063 8.21555Z"
        fill="#212428"
      />
      <ellipse
        cx="0.40727"
        cy="0.407292"
        rx="0.40727"
        ry="0.407292"
        transform="matrix(-1 0 0 1 5.98047 4.02002)"
        fill="#212428"
      />
      <ellipse
        cx="0.305452"
        cy="0.305469"
        rx="0.305452"
        ry="0.305469"
        transform="matrix(-1 0 0 1 14.0957 3.69995)"
        fill="#212428"
      />
      <ellipse
        cx="0.305452"
        cy="0.305469"
        rx="0.305452"
        ry="0.305469"
        transform="matrix(-1 0 0 1 8.01758 12.3401)"
        fill="#212428"
      />
    </svg>
  );
};

export default Temoc;
