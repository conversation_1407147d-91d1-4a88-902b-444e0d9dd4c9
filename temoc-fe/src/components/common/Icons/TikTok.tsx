import React from 'react';
interface iconProps {
  width?: string;
  height?: string;
}

const TikTok: React.FC<iconProps> = ({ width, height }: any) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_4563)">
        <path
          d="M4.97913 7.75703C3.86897 7.75703 2.9721 8.65391 2.9885 9.75312C2.99944 10.4586 3.38772 11.0711 3.95921 11.4129C3.76507 11.1148 3.65022 10.7621 3.64475 10.382C3.62835 9.28281 4.52522 8.38594 5.63538 8.38594C5.85413 8.38594 6.06467 8.42148 6.26155 8.48437V6.2832C6.05647 6.25312 5.84592 6.23672 5.63538 6.23672H5.6053V7.85547C5.40842 7.79258 5.19788 7.75703 4.97913 7.75703ZM9.77796 1.15625H9.1928C9.35686 1.9793 9.81897 2.6957 10.4643 3.18242C10.0405 2.62187 9.78342 1.92187 9.77796 1.15625Z"
          fill="#F00044"
        />
        <path
          d="M13.1246 4.50596C12.9086 4.50596 12.7008 4.48408 12.4957 4.4458V6.02354C11.752 6.02354 11.0301 5.87861 10.352 5.58877C9.91445 5.40283 9.50703 5.16494 9.13242 4.87783L9.14336 9.74228C9.13789 10.836 8.70586 11.8614 7.92383 12.6353C7.28672 13.2642 6.48008 13.6661 5.60508 13.7946C5.4 13.8247 5.18945 13.8411 4.97891 13.8411C4.04375 13.8411 3.15234 13.5376 2.42773 12.977C2.50977 13.0755 2.59727 13.1712 2.69297 13.2642C3.48047 14.0407 4.525 14.47 5.63789 14.47C5.84844 14.47 6.05898 14.4536 6.26406 14.4235C7.13906 14.295 7.9457 13.8931 8.58281 13.2642C9.36484 12.4903 9.79688 11.4649 9.80234 10.3712L9.76133 5.50674C10.1332 5.79385 10.5406 6.03447 10.9809 6.21768C11.6617 6.50479 12.3836 6.65244 13.1246 6.65244"
          fill="#F00044"
        />
        <path
          d="M2.68478 7.44824C3.46408 6.67441 4.5004 6.24512 5.60509 6.23691V5.65449C5.40001 5.62441 5.18947 5.60801 4.97892 5.60801C3.86329 5.60801 2.81603 6.0373 2.02853 6.81934C1.2547 7.5877 0.811731 8.6377 0.814466 9.72871C0.814466 10.8279 1.24923 11.8588 2.034 12.6381C2.15978 12.7611 2.28829 12.876 2.42501 12.9799C1.80704 12.2443 1.47072 11.3256 1.46798 10.3576C1.47072 9.2666 1.91095 8.2166 2.68478 7.44824ZM12.4957 4.4459V3.86074H12.4902C11.7301 3.86074 11.0301 3.60918 10.4668 3.18262C10.9563 3.82793 11.6727 4.2873 12.4957 4.4459Z"
          fill="#08FFF9"
        />
        <path
          d="M5.52305 12.318C5.78281 12.3316 6.03164 12.2961 6.26133 12.2223C7.0543 11.9625 7.62578 11.2242 7.62578 10.3547L7.62852 7.10078V1.15625H9.19258C9.15156 0.951172 9.12969 0.743359 9.12695 0.527344H6.97227V6.46914L6.96953 9.72305C6.96953 10.5926 6.39805 11.3309 5.60508 11.5906C5.37539 11.6672 5.12656 11.7027 4.8668 11.6863C4.53594 11.6672 4.22695 11.5688 3.95898 11.4102C4.29531 11.9297 4.86953 12.2824 5.52305 12.318Z"
          fill="#08FFF9"
        />
        <path
          d="M5.60509 13.7945C6.48009 13.666 7.28673 13.2641 7.92384 12.6352C8.70587 11.8613 9.1379 10.8359 9.14337 9.74219L9.13243 4.87773C9.50431 5.16484 9.91173 5.40547 10.352 5.58867C11.0328 5.87578 11.7547 6.02344 12.4957 6.02344V4.4457C11.6727 4.28711 10.9563 3.82773 10.4668 3.18242C9.8215 2.6957 9.35665 1.9793 9.19532 1.15625H7.62853V7.09805L7.62579 10.352C7.62579 11.2215 7.05431 11.9598 6.26134 12.2195C6.03165 12.2961 5.78282 12.3316 5.52306 12.3152C4.86681 12.2797 4.29533 11.927 3.959 11.4102C3.38751 11.0711 2.99923 10.4559 2.98829 9.75039C2.97189 8.65117 3.86876 7.7543 4.97892 7.7543C5.19767 7.7543 5.40822 7.78984 5.60509 7.85273V6.23398C4.5004 6.24219 3.46408 6.67148 2.68478 7.44531C1.91095 8.21367 1.46798 9.26367 1.47072 10.352C1.47072 11.3199 1.80704 12.2387 2.42775 12.9742C3.15509 13.5348 4.04376 13.8383 4.97892 13.8383C5.18947 13.841 5.40001 13.8246 5.60509 13.7945Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_4563">
          <rect
            width="14"
            height="14"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TikTok;
