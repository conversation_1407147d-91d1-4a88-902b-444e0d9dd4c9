import React from 'react';

const TokenCreation = ({ className, color }: any) => {
  return (
    <svg
      className={className}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.93321 13.3332C7.35414 13.3331 7.774 13.291 8.18654 13.2073C8.13375 13.0372 8.09242 12.8638 8.06281 12.6881C6.82645 12.9326 5.54426 12.7731 4.40543 12.2333C3.26661 11.6935 2.33149 10.8019 1.73806 9.69002C1.14462 8.57818 0.92432 7.30503 1.10965 6.05844C1.29499 4.81185 1.87614 3.65785 2.76735 2.76674C3.65857 1.87563 4.81263 1.29461 6.05924 1.10942C7.30586 0.924233 8.57898 1.14468 9.69075 1.73824C10.8025 2.3318 11.694 3.26703 12.2337 4.40591C12.7734 5.5448 12.9327 6.82701 12.6881 8.06334C12.8638 8.09295 13.0372 8.13429 13.2073 8.18707C13.291 7.77436 13.3332 7.35431 13.3332 6.93321C13.3332 5.66741 12.9579 4.43003 12.2546 3.37756C11.5514 2.32508 10.5518 1.50478 9.38238 1.02038C8.21293 0.535976 6.92611 0.409235 5.68463 0.65618C4.44315 0.903126 3.30278 1.51267 2.40772 2.40772C1.51267 3.30278 0.903126 4.44315 0.65618 5.68463C0.409235 6.92611 0.535976 8.21293 1.02038 9.38238C1.50478 10.5518 2.32508 11.5514 3.37756 12.2546C4.43003 12.9579 5.66741 13.3332 6.93321 13.3332Z"
        fill={color ? color : '#666666'}
      />
      <path fill={color ? color : '#666666'} />
      <path
        d="M12.0001 8.00009C12.0534 8.00009 12.1033 8.00596 12.1553 8.00782C12.389 6.8814 12.2531 5.70963 11.7676 4.66665C11.2822 3.62367 10.4732 2.76515 9.46087 2.21868C8.44854 1.67221 7.28691 1.46694 6.1486 1.6334C5.01029 1.79986 3.95607 2.32915 3.14261 3.14261C2.32915 3.95607 1.79986 5.01029 1.6334 6.1486C1.46694 7.28691 1.67221 8.44854 2.21868 9.46087C2.76515 10.4732 3.62367 11.2822 4.66665 11.7676C5.70963 12.2531 6.8814 12.389 8.00782 12.1553C8.00596 12.1033 8.00009 12.0534 8.00009 12.0001C8.00129 10.9396 8.4231 9.92287 9.17299 9.17299C9.92287 8.4231 10.9396 8.00129 12.0001 8.00009ZM11.7118 6.47636C11.7409 6.78037 11.7409 7.08647 11.7118 7.39049C11.7055 7.45646 11.6749 7.51772 11.6259 7.56234C11.5769 7.60695 11.513 7.63172 11.4468 7.63182C11.4379 7.63239 11.429 7.63239 11.4201 7.63182C11.3497 7.62507 11.2849 7.59066 11.2399 7.53616C11.1949 7.48166 11.1734 7.41152 11.1801 7.34116C11.2054 7.07081 11.2054 6.7987 11.1801 6.52836C11.1734 6.45799 11.1949 6.38785 11.2399 6.33335C11.2849 6.27885 11.3497 6.24444 11.4201 6.23769C11.455 6.23375 11.4903 6.23683 11.524 6.24676C11.5577 6.2567 11.5891 6.27327 11.6163 6.29551C11.6435 6.31776 11.6659 6.34522 11.6823 6.37627C11.6987 6.40733 11.7088 6.44136 11.7118 6.47636ZM10.4713 4.08569C10.5289 4.04473 10.6004 4.02832 10.6701 4.04008C10.7398 4.05183 10.802 4.09077 10.843 4.14836C11.0198 4.3973 11.1728 4.66229 11.3001 4.93982C11.3157 4.9718 11.3248 5.00658 11.3268 5.04212C11.3288 5.07767 11.3236 5.11325 11.3117 5.14677C11.2997 5.18028 11.2811 5.21107 11.257 5.23729C11.233 5.26352 11.2039 5.28467 11.1715 5.29948C11.1391 5.31429 11.1041 5.32248 11.0686 5.32354C11.033 5.32461 10.9975 5.31855 10.9643 5.30571C10.9311 5.29287 10.9009 5.27351 10.8753 5.24877C10.8497 5.22404 10.8293 5.19443 10.8153 5.16169C10.7598 5.04009 10.6966 4.91849 10.6286 4.79982C10.5606 4.68116 10.4862 4.56622 10.4089 4.45769C10.3886 4.42917 10.374 4.39692 10.3662 4.36279C10.3583 4.32866 10.3572 4.29331 10.363 4.25876C10.3688 4.22422 10.3814 4.19115 10.3999 4.16145C10.4185 4.13176 10.4428 4.10601 10.4713 4.08569ZM8.57342 2.69902C8.588 2.66716 8.60871 2.63849 8.63437 2.61464C8.66003 2.59079 8.69014 2.57223 8.72298 2.56002C8.75582 2.54781 8.79074 2.54219 8.82575 2.54349C8.86076 2.54478 8.89517 2.55296 8.92702 2.56756C9.06329 2.62969 9.20009 2.70089 9.33316 2.77716C9.46622 2.85342 9.59609 2.93716 9.71849 3.02462C9.77502 3.06606 9.81296 3.12809 9.8241 3.19728C9.83525 3.26648 9.81871 3.33728 9.77805 3.39437C9.7374 3.45146 9.6759 3.49026 9.60686 3.50235C9.53783 3.51445 9.4668 3.49889 9.40916 3.45902C9.18788 3.3015 8.95216 3.16534 8.70516 3.05236C8.67321 3.03781 8.64445 3.01709 8.62053 2.9914C8.5966 2.96571 8.57799 2.93555 8.56575 2.90264C8.55351 2.86974 8.54789 2.83475 8.5492 2.79967C8.55052 2.76459 8.55875 2.73011 8.57342 2.69822V2.69902ZM6.47636 2.15502C6.7804 2.12649 7.08645 2.12649 7.39049 2.15502C7.46121 2.15835 7.52772 2.18963 7.57538 2.24199C7.62304 2.29435 7.64795 2.3635 7.64462 2.43422C7.6413 2.50495 7.61001 2.57145 7.55765 2.61911C7.50529 2.66677 7.43615 2.69168 7.36542 2.68836C7.35651 2.68847 7.34761 2.68803 7.33876 2.68702C7.06839 2.66222 6.79632 2.66222 6.52596 2.68702C6.4568 2.69148 6.38862 2.66884 6.33587 2.6239C6.28312 2.57895 6.24993 2.51524 6.24334 2.44625C6.23676 2.37727 6.25729 2.30842 6.30058 2.25431C6.34388 2.2002 6.40654 2.16507 6.47529 2.15636L6.47636 2.15502ZM4.14836 3.02382C4.3973 2.84705 4.66229 2.69402 4.93982 2.56676C4.9718 2.55111 5.00658 2.54203 5.04212 2.54005C5.07767 2.53806 5.11325 2.54321 5.14677 2.55519C5.18028 2.56718 5.21107 2.58575 5.23729 2.60982C5.26352 2.63388 5.28467 2.66296 5.29948 2.69533C5.31429 2.7277 5.32248 2.7627 5.32354 2.79828C5.32461 2.83386 5.31855 2.8693 5.30571 2.9025C5.29287 2.9357 5.27351 2.96599 5.24877 2.99159C5.22404 3.01719 5.19443 3.03758 5.16169 3.05156C5.04009 3.10702 4.91849 3.17022 4.79982 3.23822C4.68116 3.30622 4.56622 3.38062 4.45769 3.45796C4.4292 3.47884 4.39684 3.49386 4.36249 3.50213C4.32815 3.51041 4.2925 3.51178 4.25762 3.50616C4.22274 3.50054 4.18933 3.48805 4.15932 3.46941C4.12931 3.45076 4.1033 3.42634 4.08281 3.39757C4.06232 3.36879 4.04775 3.33623 4.03995 3.30177C4.03214 3.26731 4.03127 3.23165 4.03737 3.19685C4.04346 3.16206 4.05642 3.12882 4.07547 3.09907C4.09452 3.06932 4.1193 3.04392 4.14836 3.02382ZM2.42009 7.63182C2.35382 7.63172 2.28996 7.60695 2.24096 7.56234C2.19195 7.51772 2.16132 7.45646 2.15502 7.39049C2.12597 7.08647 2.12597 6.78037 2.15502 6.47636C2.15773 6.44128 2.16749 6.40711 2.1837 6.37588C2.19991 6.34466 2.22225 6.31702 2.24938 6.29462C2.27651 6.27222 2.30787 6.25551 2.3416 6.2455C2.37533 6.23549 2.41073 6.23238 2.44569 6.23636C2.51605 6.24311 2.58084 6.27751 2.62585 6.33202C2.67085 6.38652 2.69237 6.45666 2.68569 6.52702C2.66034 6.79736 2.66034 7.06948 2.68569 7.33982C2.69237 7.41019 2.67085 7.48032 2.62585 7.53483C2.58084 7.58933 2.51605 7.62374 2.44569 7.63049C2.43719 7.63146 2.42864 7.6319 2.42009 7.63182ZM3.39556 9.78116C3.33794 9.82211 3.26642 9.83852 3.19672 9.82677C3.12701 9.81502 3.06482 9.77607 3.02382 9.71849C2.84705 9.46954 2.69402 9.20456 2.56676 8.92702C2.55111 8.89505 2.54203 8.86026 2.54005 8.82472C2.53806 8.78918 2.54321 8.7536 2.55519 8.72008C2.56718 8.68656 2.58575 8.65578 2.60982 8.62955C2.63388 8.60332 2.66296 8.58218 2.69533 8.56736C2.7277 8.55255 2.7627 8.54437 2.79828 8.5433C2.83386 8.54223 2.8693 8.54829 2.9025 8.56114C2.9357 8.57398 2.96599 8.59334 2.99159 8.61807C3.01719 8.64281 3.03758 8.67242 3.05156 8.70516C3.10702 8.82676 3.17022 8.94836 3.23822 9.06702C3.30622 9.18569 3.38062 9.30062 3.45796 9.40916C3.47829 9.43768 3.49281 9.46992 3.50068 9.50406C3.50855 9.53819 3.50961 9.57354 3.50382 9.60808C3.49803 9.64263 3.48548 9.67569 3.46691 9.70539C3.44833 9.73509 3.42408 9.76083 3.39556 9.78116ZM3.45822 4.45769C3.3007 4.67896 3.16454 4.91469 3.05156 5.16169C3.03029 5.20818 2.99611 5.24757 2.95309 5.27518C2.91006 5.30279 2.86001 5.31745 2.80889 5.31742C2.76425 5.31736 2.72033 5.30609 2.68118 5.28465C2.64202 5.26321 2.60887 5.23228 2.58477 5.1947C2.56068 5.15712 2.5464 5.11409 2.54325 5.06956C2.5401 5.02503 2.54818 4.98042 2.56676 4.93982C2.62889 4.80356 2.70009 4.66676 2.77636 4.53369C2.85262 4.40062 2.93636 4.27076 3.02382 4.14836C3.04392 4.1193 3.06958 4.09452 3.09933 4.07547C3.12908 4.05642 3.16232 4.04346 3.19712 4.03737C3.23192 4.03127 3.26758 4.03214 3.30204 4.03995C3.33649 4.04775 3.36906 4.06232 3.39783 4.08281C3.42661 4.1033 3.45103 4.12931 3.46967 4.15932C3.48831 4.18933 3.50081 4.22274 3.50643 4.25762C3.51204 4.2925 3.51068 4.32815 3.5024 4.36249C3.49413 4.39684 3.47911 4.4292 3.45822 4.45769ZM5.29342 11.1686C5.27885 11.2005 5.25814 11.2292 5.23247 11.253C5.20681 11.2769 5.1767 11.2954 5.14386 11.3076C5.11102 11.3198 5.0761 11.3255 5.04109 11.3242C5.00608 11.3229 4.97167 11.3147 4.93982 11.3001C4.80356 11.238 4.66676 11.1668 4.53369 11.0905C4.40062 11.0142 4.27076 10.9305 4.14836 10.843C4.1193 10.8229 4.09452 10.7973 4.07547 10.7675C4.05642 10.7378 4.04346 10.7045 4.03737 10.6697C4.03127 10.6349 4.03214 10.5993 4.03995 10.5648C4.04775 10.5304 4.06232 10.4978 4.08281 10.469C4.1033 10.4402 4.12931 10.4158 4.15932 10.3972C4.18933 10.3785 4.22274 10.366 4.25762 10.3604C4.2925 10.3548 4.32815 10.3562 4.36249 10.3644C4.39684 10.3727 4.4292 10.3877 4.45769 10.4086C4.67898 10.5661 4.9147 10.7023 5.16169 10.8153C5.22601 10.8447 5.27602 10.8984 5.30072 10.9647C5.32543 11.0309 5.3228 11.1043 5.29342 11.1686ZM7.39049 11.7118C7.08647 11.7409 6.78037 11.7409 6.47636 11.7118C6.44072 11.7095 6.40591 11.7001 6.37398 11.6841C6.34206 11.6681 6.31368 11.6458 6.29052 11.6186C6.26736 11.5915 6.2499 11.5599 6.23916 11.5258C6.22843 11.4918 6.22465 11.4559 6.22805 11.4204C6.23144 11.3848 6.24194 11.3503 6.25892 11.3189C6.27591 11.2875 6.29903 11.2598 6.32691 11.2375C6.35479 11.2152 6.38688 11.1987 6.42125 11.189C6.45563 11.1793 6.49159 11.1767 6.52702 11.1812C6.79739 11.206 7.06946 11.206 7.33982 11.1812C7.37475 11.1774 7.41008 11.1806 7.44373 11.1907C7.47738 11.2008 7.50867 11.2175 7.53576 11.2399C7.56285 11.2622 7.58519 11.2898 7.60146 11.3209C7.61773 11.3521 7.6276 11.3861 7.63049 11.4212C7.63717 11.4915 7.61565 11.5617 7.57065 11.6162C7.52565 11.6707 7.46085 11.7051 7.39049 11.7118ZM6.93342 9.06676C6.51149 9.06676 6.09903 8.94164 5.74821 8.70722C5.39738 8.47281 5.12395 8.13963 4.96248 7.74981C4.80101 7.36 4.75876 6.93106 4.84108 6.51723C4.92339 6.1034 5.12658 5.72328 5.42493 5.42493C5.72328 5.12658 6.1034 4.92339 6.51723 4.84108C6.93106 4.75876 7.36 4.80101 7.74981 4.96248C8.13963 5.12395 8.47281 5.39738 8.70722 5.74821C8.94164 6.09903 9.06676 6.51149 9.06676 6.93342C9.06612 7.49902 8.84116 8.04128 8.44122 8.44122C8.04128 8.84116 7.49902 9.06612 6.93342 9.06676Z"
        fill={color ? color : '#666666'}
      />
      <path
        d="M11.9999 8.5332C11.3142 8.5332 10.644 8.73652 10.0739 9.11744C9.5038 9.49836 9.05947 10.0398 8.79709 10.6732C8.53471 11.3067 8.46605 12.0037 8.59982 12.6762C8.73358 13.3487 9.06375 13.9664 9.54857 14.4512C10.0334 14.936 10.6511 15.2662 11.3236 15.3999C11.996 15.5337 12.6931 15.465 13.3265 15.2027C13.96 14.9403 14.5014 14.4959 14.8823 13.9258C15.2632 13.3558 15.4665 12.6855 15.4665 11.9999C15.4655 11.0808 15.0999 10.1996 14.45 9.54974C13.8001 8.89984 12.919 8.53426 11.9999 8.5332ZM13.8665 12.2665H12.2665V13.8665C12.2665 13.9373 12.2384 14.0051 12.1884 14.0551C12.1384 14.1051 12.0706 14.1332 11.9999 14.1332C11.9291 14.1332 11.8613 14.1051 11.8113 14.0551C11.7613 14.0051 11.7332 13.9373 11.7332 13.8665V12.2665H10.1332C10.0625 12.2665 9.99465 12.2384 9.94464 12.1884C9.89463 12.1384 9.86654 12.0706 9.86654 11.9999C9.86654 11.9291 9.89463 11.8613 9.94464 11.8113C9.99465 11.7613 10.0625 11.7332 10.1332 11.7332H11.7332V10.1332C11.7332 10.0625 11.7613 9.99465 11.8113 9.94464C11.8613 9.89463 11.9291 9.86654 11.9999 9.86654C12.0706 9.86654 12.1384 9.89463 12.1884 9.94464C12.2384 9.99465 12.2665 10.0625 12.2665 10.1332V11.7332H13.8665C13.9373 11.7332 14.0051 11.7613 14.0551 11.8113C14.1051 11.8613 14.1332 11.9291 14.1332 11.9999C14.1332 12.0706 14.1051 12.1384 14.0551 12.1884C14.0051 12.2384 13.9373 12.2665 13.8665 12.2665Z"
        fill={color ? color : '#666666'}
      />
    </svg>
  );
};

export default TokenCreation;
