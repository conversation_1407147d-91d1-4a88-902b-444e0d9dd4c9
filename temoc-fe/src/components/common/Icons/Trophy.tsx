import React from 'react';

const Trophy = () => {
  return (
    <svg
      width="16"
      height="17"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2185_31905)">
        <path
          d="M11.2438 14.0527L8 13.1152L4.75622 14.0527C4.72813 14.109 4.71875 14.1652 4.71875 14.2309V16.1684H11.2812V14.2309C11.2812 14.1652 11.2719 14.109 11.2438 14.0527Z"
          fill="#646D73"
        />
        <path
          d="M11.2812 16.1684V14.2309C11.2812 14.1652 11.2719 14.109 11.2438 14.0527L8 13.1152V16.1684H11.2812Z"
          fill="#474F54"
        />
        <path
          d="M15.5312 1.57422H13.1361C13.1402 1.41588 13.1562 1.26672 13.1562 1.10547C13.1562 0.842937 12.95 0.636719 12.6875 0.636719H3.3125C3.04997 0.636719 2.84375 0.842937 2.84375 1.10547C2.84375 1.26672 2.85978 1.41588 2.86384 1.57422H0.46875C0.209656 1.57422 0 1.78387 0 2.04297V3.23041C0 5.84772 2.09662 7.97388 4.68041 8.11075C5.21869 8.85188 5.85753 9.40187 6.58437 9.71172C6.47187 11.9898 5.16875 13.5742 4.85941 13.9024C4.8125 13.9398 4.775 13.9961 4.75625 14.0523H11.2438C11.225 13.9961 11.1875 13.9398 11.1406 13.9024C10.8219 13.5742 9.52812 11.9992 9.41562 9.71172C10.1426 9.40181 10.7853 8.85169 11.324 8.11028C13.9056 7.97119 16 5.84625 16 3.23041V2.04297C16 1.78387 15.7903 1.57422 15.5312 1.57422ZM0.9375 3.23041V2.51172H2.90406C3.02572 4.06103 3.36169 5.70191 4.06781 7.09634C2.29781 6.69775 0.9375 5.11931 0.9375 3.23041ZM15.0625 3.23041C15.0625 5.11834 13.7034 6.69603 11.9347 7.09553C12.6353 5.70113 12.9731 4.07613 13.0959 2.51172H15.0625V3.23041Z"
          fill="#FED843"
        />
        <path
          d="M15.5312 1.57422H13.1361C13.1402 1.41588 13.1562 1.26672 13.1562 1.10547C13.1562 0.842937 12.95 0.636719 12.6875 0.636719H8V14.0523H11.2438C11.225 13.9961 11.1875 13.9398 11.1406 13.9024C10.8219 13.5742 9.52812 11.9992 9.41562 9.71172C10.1426 9.40181 10.7853 8.85169 11.324 8.11028C13.9056 7.97119 16 5.84625 16 3.23041V2.04297C16 1.78387 15.7903 1.57422 15.5312 1.57422ZM15.0625 3.23041C15.0625 5.11834 13.7034 6.69603 11.9347 7.09553C12.6353 5.70113 12.9732 4.07613 13.0959 2.51172H15.0625V3.23041Z"
          fill="#FABE2C"
        />
        <path
          d="M8.74799 6.59877L7.99999 6.21012L7.25199 6.59877C7.09542 6.67934 6.90408 6.66605 6.76036 6.56215C6.68992 6.51103 6.63513 6.44128 6.60213 6.36074C6.56914 6.2802 6.55925 6.19206 6.57358 6.10621L6.71092 5.27352L6.11127 4.68165C5.98074 4.5544 5.94083 4.36574 5.99408 4.20284C6.0211 4.12007 6.0706 4.04645 6.13705 3.99019C6.2035 3.93393 6.28427 3.89725 6.37036 3.88424L7.20349 3.7588L7.58067 3.00487C7.73999 2.68718 8.25999 2.68718 8.4193 3.00487L8.79649 3.7588L9.62961 3.88424C9.71569 3.89726 9.79647 3.93395 9.86291 3.9902C9.92935 4.04646 9.97886 4.12008 10.0059 4.20284C10.0329 4.28563 10.0363 4.37435 10.0156 4.45896C9.9949 4.54357 9.95094 4.6207 9.8887 4.68165L9.28905 5.27352L9.42639 6.10621C9.44073 6.19206 9.43085 6.2802 9.39785 6.36075C9.36486 6.44129 9.31006 6.51103 9.23961 6.56215C9.09683 6.66562 8.90583 6.6809 8.74799 6.59877Z"
          fill="#FABE2C"
        />
        <path
          d="M8.748 6.59879C8.90588 6.68091 9.09687 6.66563 9.23962 6.56216C9.31007 6.51104 9.36486 6.4413 9.39785 6.36076C9.43085 6.28022 9.44074 6.19207 9.42641 6.10623L9.28906 5.27354L9.88872 4.68166C9.95096 4.62072 9.99491 4.54359 10.0156 4.45898C10.0363 4.37437 10.033 4.28565 10.0059 4.20285C9.97888 4.12009 9.92938 4.04646 9.86294 3.9902C9.79649 3.93394 9.71571 3.89726 9.62963 3.88426L8.7965 3.75882L8.41931 3.00488C8.33966 2.84604 8.16984 2.7666 8 2.7666V6.21013L8.748 6.59879Z"
          fill="#FF9100"
        />
        <path
          d="M11.75 16.6367H4.25C3.99091 16.6367 3.78125 16.4271 3.78125 16.168C3.78125 15.9089 3.99091 15.6992 4.25 15.6992H11.75C12.0091 15.6992 12.2188 15.9089 12.2188 16.168C12.2188 16.4271 12.0091 16.6367 11.75 16.6367Z"
          fill="#474F54"
        />
        <path
          d="M11.75 15.6992H8V16.6367H11.75C12.0091 16.6367 12.2188 16.4271 12.2188 16.168C12.2188 15.9089 12.0091 15.6992 11.75 15.6992Z"
          fill="#32393F"
        />
      </g>
      <defs>
        <clipPath id="clip0_2185_31905">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.636719)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Trophy;
