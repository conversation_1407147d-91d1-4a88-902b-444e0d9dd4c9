import React from 'react';

interface Iprops {
  className?: string;
}

const USDCIcon = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
    >
      <rect
        y="0.0461426"
        width="20"
        height="20"
        fill="url(#pattern0_125_363)"
      />
      <defs>
        <pattern
          id="pattern0_125_363"
          patternContentUnits="objectBoundingBox"
          width="1"
          height="1"
        >
          <use href="#image0_125_363" transform="scale(0.0108696)" />
        </pattern>
        <image
          id="image0_125_363"
          width="92"
          height="92"
          preserveAspectRatio="none"
          href="data:image/png;base64,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"
        />
      </defs>
    </svg>
  );
};

export default USDCIcon;
