import React from 'react';

const UniSwap = () => {
  return (
    <svg
      width="15"
      height="16"
      viewBox="0 0 15 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2146_42179)">
        <path
          d="M4.53645 1.11438C4.33098 1.08364 4.32232 1.08004 4.41901 1.06571C4.6043 1.03822 5.04184 1.07568 5.34334 1.14483C6.04723 1.3062 6.68773 1.71958 7.37142 2.45374L7.55304 2.64879L7.81291 2.6085C8.90754 2.43884 10.0211 2.57368 10.9526 2.98867C11.2088 3.10284 11.6128 3.33009 11.6633 3.38849C11.6793 3.40711 11.7089 3.52692 11.7289 3.65476C11.798 4.09706 11.7634 4.43609 11.6231 4.6893C11.5469 4.8271 11.5426 4.87076 11.5939 4.98869C11.6348 5.08279 11.7491 5.15244 11.8622 5.15229C12.0936 5.15199 12.3428 4.79112 12.4582 4.289L12.504 4.08954L12.5948 4.18878C13.0931 4.73322 13.4844 5.47571 13.5517 6.00421L13.5692 6.14201L13.4855 6.01673C13.3413 5.80118 13.1965 5.65444 13.011 5.53609C12.6767 5.32276 12.3233 5.25015 11.3872 5.20257C10.5417 5.15961 10.0632 5.08996 9.58876 4.94074C8.78146 4.68689 8.37451 4.34882 7.41563 3.13543C6.98971 2.59648 6.72646 2.29829 6.46459 2.05815C5.86956 1.51251 5.28488 1.22635 4.53645 1.11438Z"
          fill="currentColor"
        />
        <path
          d="M11.8536 2.3182C11.8749 1.95705 11.9257 1.71885 12.0278 1.50131C12.0682 1.4152 12.1061 1.34473 12.1119 1.34473C12.1178 1.34473 12.1002 1.40828 12.0728 1.48595C11.9987 1.69708 11.9864 1.98585 12.0376 2.32181C12.1025 2.7481 12.1394 2.80961 12.6065 3.27009C12.8257 3.48607 13.0805 3.75848 13.1729 3.87543L13.3409 4.08809L13.1729 3.93611C12.9675 3.75026 12.495 3.38782 12.3906 3.33601C12.3206 3.30126 12.3103 3.30185 12.2671 3.34329C12.2273 3.38148 12.2189 3.43885 12.2134 3.71008C12.2048 4.13281 12.1451 4.40414 12.0009 4.67544C11.9229 4.82218 11.9106 4.79087 11.9812 4.62524C12.0339 4.50157 12.0392 4.44721 12.0389 4.03798C12.038 3.21574 11.9368 3.01807 11.3432 2.67945C11.1929 2.59366 10.9451 2.46996 10.7927 2.40452C10.6403 2.33908 10.5192 2.28208 10.5236 2.27782C10.5404 2.26169 11.1193 2.42463 11.3522 2.51107C11.6988 2.63964 11.756 2.6563 11.7981 2.64079C11.8263 2.6304 11.84 2.55115 11.8536 2.3182Z"
          fill="currentColor"
        />
        <path
          d="M4.93549 3.72454C4.51837 3.17068 4.26029 2.32149 4.31616 1.68668L4.33344 1.49023L4.42839 1.50695C4.60668 1.53831 4.91412 1.64868 5.05807 1.73301C5.45312 1.9644 5.62414 2.26905 5.79813 3.05135C5.8491 3.28049 5.91597 3.53979 5.94674 3.62758C5.99627 3.76888 6.18345 4.09894 6.33562 4.3133C6.44523 4.46767 6.37242 4.54083 6.13013 4.51973C5.76009 4.48751 5.25885 4.15388 4.93549 3.72454Z"
          fill="currentColor"
        />
        <path
          d="M11.3469 7.84668C9.39756 7.08968 8.71094 6.43262 8.71094 5.32398C8.71094 5.16083 8.71679 5.02734 8.72391 5.02734C8.73096 5.02734 8.80641 5.08118 8.89146 5.147C9.28679 5.45278 9.72944 5.58338 10.9548 5.7558C11.6759 5.85727 12.0817 5.93922 12.4561 6.05895C13.6458 6.43951 14.3819 7.21182 14.5575 8.26377C14.6085 8.56937 14.5786 9.14261 14.4958 9.44472C14.4306 9.68326 14.2313 10.1134 14.1785 10.1299C14.1639 10.1345 14.1495 10.0803 14.1457 10.0066C14.1256 9.61163 13.9187 9.22705 13.5712 8.93897C13.176 8.61148 12.6451 8.35075 11.3469 7.84668Z"
          fill="currentColor"
        />
        <path
          d="M9.97975 8.16092C9.9553 8.0207 9.91299 7.84172 9.88562 7.7631L9.8359 7.62012L9.92822 7.72019C10.0561 7.85859 10.1571 8.03568 10.2428 8.27154C10.3081 8.45161 10.3154 8.50514 10.315 8.79764C10.3145 9.08484 10.3063 9.14506 10.246 9.3071C10.1508 9.56266 10.0327 9.74383 9.83454 9.9383C9.47844 10.2878 9.02065 10.4814 8.36005 10.5616C8.24522 10.5755 7.9105 10.5991 7.61627 10.6138C6.87469 10.651 6.38663 10.7279 5.94808 10.8764C5.88503 10.8978 5.82874 10.9107 5.82302 10.9052C5.80528 10.8882 6.10386 10.7166 6.35049 10.6019C6.69824 10.4404 7.04441 10.3522 7.82005 10.2277C8.20315 10.1661 8.59885 10.0915 8.69927 10.0617C9.6481 9.78114 10.1358 9.05706 9.97975 8.16092Z"
          fill="currentColor"
        />
        <path
          d="M10.8728 9.69147C10.6137 9.15452 10.5543 8.63605 10.6962 8.15256C10.7114 8.10085 10.7358 8.05859 10.7505 8.05859C10.7652 8.05859 10.8263 8.09045 10.8863 8.12936C11.0056 8.20688 11.2449 8.33736 11.8827 8.6727C12.6784 9.09118 13.1322 9.41518 13.4407 9.78536C13.7109 10.1096 13.8781 10.4788 13.9586 10.9291C14.0042 11.1841 13.9775 11.7978 13.9096 12.0547C13.6956 12.8644 13.1984 13.5005 12.4892 13.8717C12.3852 13.926 12.2919 13.9707 12.2819 13.9709C12.2718 13.9711 12.3096 13.8782 12.366 13.7646C12.6046 13.2835 12.6318 12.8156 12.4514 12.2948C12.3409 11.9759 12.1158 11.5868 11.661 10.9292C11.1324 10.1646 11.0028 9.96114 10.8728 9.69147Z"
          fill="currentColor"
        />
        <path
          d="M3.55131 12.5895C4.27469 12.0005 5.17475 11.5819 5.99462 11.4535C6.34796 11.3981 6.93658 11.4201 7.26376 11.5009C7.78825 11.6303 8.25737 11.9202 8.50142 12.2657C8.73984 12.6033 8.84214 12.8975 8.94864 13.5521C8.99064 13.8103 9.03639 14.0697 9.05019 14.1284C9.13022 14.4676 9.28585 14.7388 9.47875 14.875C9.78512 15.0912 10.3127 15.1047 10.8316 14.9095C10.9196 14.8763 10.9961 14.8534 11.0015 14.8586C11.0203 14.8766 10.759 15.0453 10.5746 15.1341C10.3266 15.2536 10.1294 15.2998 9.86725 15.2998C9.39197 15.2998 8.99732 15.0666 8.66807 14.5911C8.60327 14.4975 8.45762 14.2173 8.34444 13.9683C7.99674 13.2035 7.82507 12.9706 7.42145 12.7156C7.07017 12.4938 6.61713 12.454 6.27632 12.6151C5.82864 12.8269 5.70373 13.3786 6.02437 13.7283C6.15181 13.8673 6.38946 13.9871 6.58378 14.0105C6.94732 14.0541 7.25974 13.7874 7.25974 13.4333C7.25974 13.2035 7.16814 13.0723 6.93754 12.9719C6.62259 12.8349 6.28404 12.9951 6.28566 13.2805C6.28637 13.4021 6.34131 13.4786 6.46778 13.5338C6.54891 13.5692 6.5508 13.5719 6.48464 13.5587C6.19566 13.5009 6.12796 13.1649 6.36031 12.9418C6.63927 12.6741 7.21611 12.7922 7.4142 13.1578C7.49742 13.3113 7.50707 13.617 7.43452 13.8017C7.27214 14.2149 6.79867 14.4322 6.31836 14.314C5.99136 14.2335 5.85821 14.1463 5.46394 13.7546C4.77885 13.074 4.51288 12.9421 3.52521 12.7934L3.33594 12.7649L3.55131 12.5895Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0.336945 0.40712C2.62485 3.08529 4.20065 4.19026 4.37576 4.42371C4.52035 4.61647 4.46593 4.78977 4.21823 4.92559C4.08049 5.0011 3.7973 5.07761 3.65551 5.07761C3.49514 5.07761 3.44008 5.01794 3.44008 5.01794C3.34709 4.93288 3.29472 4.94776 2.81721 4.13027C2.15427 3.13815 1.59947 2.31513 1.58434 2.30136C1.54934 2.26949 1.54994 2.27057 2.74961 4.34081C2.94344 4.77231 2.78817 4.93069 2.78817 4.99215C2.78817 5.11717 2.75281 5.18288 2.5929 5.3549C2.32634 5.64172 2.20718 5.96398 2.12116 6.63093C2.02472 7.37855 1.75357 7.90669 1.0021 8.81062C0.56222 9.33971 0.490242 9.43665 0.379247 9.64996C0.23944 9.91847 0.200999 10.0689 0.185418 10.408C0.16895 10.7665 0.201023 10.9982 0.314602 11.341C0.414035 11.6411 0.517826 11.8393 0.783165 12.2356C1.01215 12.5777 1.144 12.8319 1.144 12.9313C1.144 13.0105 1.15966 13.0105 1.51448 12.9333C2.36363 12.7484 3.05314 12.4231 3.44093 12.0245C3.68093 11.7777 3.73727 11.6415 3.7391 11.3034C3.7403 11.0823 3.73223 11.036 3.67025 10.9087C3.56936 10.7017 3.38568 10.5295 2.98085 10.2626C2.45041 9.91287 2.22386 9.63135 2.16127 9.24415C2.10994 8.9264 2.16949 8.70225 2.46293 8.10909C2.76666 7.49513 2.84193 7.23347 2.89285 6.6146C2.92573 6.21476 2.97127 6.05708 3.09038 5.93051C3.21461 5.79852 3.32644 5.75383 3.63388 5.71332C4.13509 5.64727 4.45424 5.52221 4.71657 5.28906C4.94415 5.0868 5.03938 4.89191 5.05399 4.59853L5.06509 4.37616L4.93791 4.23316C4.47736 3.71524 0.0285177 0 0.000175296 0C-0.00587943 0 0.145673 0.183217 0.336945 0.40712ZM1.40252 10.8137C1.50665 10.6359 1.45133 10.4072 1.27715 10.2955C1.11257 10.19 0.85692 10.2396 0.85692 10.3772C0.85692 10.4192 0.88098 10.4497 0.935213 10.4767C1.02653 10.522 1.03315 10.5729 0.961312 10.6771C0.888547 10.7825 0.89442 10.8753 0.97788 10.9383C1.11239 11.0399 1.3028 10.984 1.40252 10.8137Z"
          fill="currentColor"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.3807 5.8298C5.14541 5.8995 4.91668 6.14001 4.84588 6.3922C4.80269 6.54605 4.8272 6.81593 4.89189 6.89929C4.99641 7.0339 5.09749 7.06937 5.37118 7.06752C5.90702 7.06391 6.37285 6.84226 6.427 6.56517C6.47139 6.33804 6.26681 6.02326 5.985 5.88507C5.83958 5.81379 5.53032 5.78551 5.3807 5.8298ZM6.0071 6.30221C6.08973 6.18897 6.05359 6.06658 5.91305 5.9838C5.64542 5.82619 5.24069 5.95662 5.24069 6.20047C5.24069 6.32185 5.45174 6.45428 5.64521 6.45428C5.77397 6.45428 5.95019 6.38023 6.0071 6.30221Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2146_42179">
          <rect width="15" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default UniSwap;
