import React from 'react';
interface Iprops {
  className?: any;
}
const Upload = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.0003 15.9167C10.467 15.9167 8.41699 13.8667 8.41699 11.3333C8.41699 8.8 10.467 6.75 13.0003 6.75C15.5337 6.75 17.5837 8.8 17.5837 11.3333C17.5837 13.8667 15.5337 15.9167 13.0003 15.9167ZM13.0003 9.25C12.4478 9.25 11.9179 9.46949 11.5272 9.86019C11.1365 10.2509 10.917 10.7808 10.917 11.3333C10.917 11.8859 11.1365 12.4158 11.5272 12.8065C11.9179 13.1972 12.4478 13.4167 13.0003 13.4167C13.5529 13.4167 14.0828 13.1972 14.4735 12.8065C14.8642 12.4158 15.0837 11.8859 15.0837 11.3333C15.0837 10.7808 14.8642 10.2509 14.4735 9.86019C14.0828 9.46949 13.5529 9.25 13.0003 9.25Z"
        fill="#666666"
      />
      <path
        d="M23.0002 35.9168H13.0002C3.95016 35.9168 0.0834961 32.0502 0.0834961 23.0002V13.0002C0.0834961 3.95016 3.95016 0.0834961 13.0002 0.0834961H19.6668C20.3502 0.0834961 20.9168 0.650163 20.9168 1.3335C20.9168 2.01683 20.3502 2.5835 19.6668 2.5835H13.0002C5.31683 2.5835 2.5835 5.31683 2.5835 13.0002V23.0002C2.5835 30.6835 5.31683 33.4168 13.0002 33.4168H23.0002C30.6835 33.4168 33.4168 30.6835 33.4168 23.0002V14.6668C33.4168 13.9835 33.9835 13.4168 34.6668 13.4168C35.3502 13.4168 35.9168 13.9835 35.9168 14.6668V23.0002C35.9168 32.0502 32.0502 35.9168 23.0002 35.9168Z"
        fill="#666666"
      />
      <path
        d="M28 12.5834C27.3167 12.5834 26.75 12.0167 26.75 11.3334V1.33339C26.75 0.833395 27.05 0.366728 27.5167 0.183395C27.9833 6.12468e-05 28.5167 0.100061 28.8833 0.450061L32.2167 3.78339C32.7 4.26673 32.7 5.06673 32.2167 5.55006C31.7333 6.0334 30.9333 6.0334 30.45 5.55006L29.25 4.35006V11.3334C29.25 12.0167 28.6833 12.5834 28 12.5834Z"
        fill="#666666"
      />
      <path
        d="M24.6667 5.91706C24.35 5.91706 24.0333 5.80039 23.7833 5.55039C23.5509 5.31516 23.4205 4.99777 23.4205 4.66706C23.4205 4.33634 23.5509 4.01895 23.7833 3.78372L27.1167 0.450391C27.6 -0.0329427 28.4 -0.0329427 28.8833 0.450391C29.3667 0.933724 29.3667 1.73372 28.8833 2.21706L25.55 5.55039C25.3 5.80039 24.9833 5.91706 24.6667 5.91706ZM2.44999 30.8337C2.18359 30.8319 1.92473 30.7451 1.71107 30.5859C1.49741 30.4268 1.34009 30.2037 1.262 29.949C1.18392 29.6943 1.18914 29.4213 1.2769 29.1697C1.36467 28.9182 1.5304 28.7012 1.74999 28.5504L9.96665 23.0337C11.7667 21.8337 14.25 21.9671 15.8833 23.3504L16.4333 23.8337C17.2667 24.5504 18.6833 24.5504 19.5 23.8337L26.4333 17.8837C28.2 16.3671 30.9833 16.3671 32.7667 17.8837L35.4833 20.2171C36 20.6671 36.0667 21.4504 35.6167 21.9837C35.1667 22.5004 34.3667 22.5671 33.85 22.1171L31.1333 19.7837C30.3 19.0671 28.9 19.0671 28.0667 19.7837L21.1333 25.7337C19.3667 27.2504 16.5833 27.2504 14.8 25.7337L14.25 25.2504C13.4833 24.6004 12.2167 24.5337 11.3667 25.1171L3.16665 30.6337C2.93332 30.7671 2.68332 30.8337 2.44999 30.8337Z"
        fill="#666666"
      />
    </svg>
  );
};

export default Upload;
