import React from 'react';
interface Iprops {
  className?: string;
}
const Usdt = ({ className }: Iprops) => {
  return (
    <svg
      className={className}
      width="21"
      height="17"
      viewBox="0 0 21 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_125_1015)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.25401 0.284668H16.6267C16.8981 0.284668 17.1488 0.42365 17.2841 0.649126L20.5974 6.16947C20.7692 6.45575 20.7182 6.81708 20.4731 7.04923L11.3531 15.6879C11.0576 15.9678 10.5824 15.9678 10.287 15.6879L1.17923 7.0609C0.928452 6.82336 0.881491 6.4515 1.0659 6.16342L4.6076 0.631415C4.74553 0.415969 4.9903 0.284668 5.25401 0.284668ZM15.1583 2.74931V4.29825H11.9187V5.37216C14.194 5.48693 15.901 5.95827 15.9137 6.52322L15.9136 7.70102C15.901 8.26598 14.194 8.73731 11.9187 8.85208V11.4876H9.7675V8.85208C7.49219 8.73731 5.78512 8.26598 5.77249 7.70102L5.77253 6.52322C5.7852 5.95827 7.49219 5.48693 9.7675 5.37216V4.29825H6.52787V2.74931H15.1583ZM10.8431 8.04295C13.2712 8.04295 15.3007 7.64439 15.7974 7.11214C15.3762 6.66079 13.8528 6.30555 11.9187 6.20799V7.33239C11.572 7.34988 11.2121 7.35909 10.8431 7.35909C10.474 7.35909 10.1142 7.34988 9.7675 7.33239V6.20799C7.8334 6.30555 6.30992 6.66079 5.88874 7.11214C6.38541 7.64439 8.41493 8.04295 10.8431 8.04295Z"
          fill="#009393"
        />
      </g>
      <defs>
        <clipPath id="clip0_125_1015">
          <rect
            width="20"
            height="16"
            fill="white"
            transform="translate(0.953857 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Usdt;
