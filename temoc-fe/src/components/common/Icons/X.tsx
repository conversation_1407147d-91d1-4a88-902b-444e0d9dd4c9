import React from 'react';
interface iconProps {
  width?: string;
  height?: string;
}

const X: React.FC<iconProps> = ({ width, height }: any) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2087_4555)">
        <path
          d="M8.30314 6.42804L13.4029 0.5H12.1944L7.7663 5.64724L4.22958 0.5H0.150391L5.4986 8.28354L0.150391 14.5H1.35894L6.03514 9.06434L9.77017 14.5H13.8494L8.30284 6.42804H8.30314ZM6.64787 8.35211L6.10598 7.57705L1.79439 1.40977H3.65065L7.13015 6.38696L7.67204 7.16202L12.195 13.6316H10.3387L6.64787 8.35241V8.35211Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_2087_4555">
          <rect
            width="14"
            height="14"
            fill="currentColor"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default X;
