import React from 'react';

interface iconProps {
  width?: string;
  height?: string;
}

const Youtube: React.FC<iconProps> = ({ width, height }: any) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 11"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.0924 0.546875H2.90762C1.30178 0.546875 0 1.84866 0 3.4545V7.54601C0 9.15184 1.30178 10.4536 2.90762 10.4536H11.0924C12.6982 10.4536 14 9.15184 14 7.54601V3.4545C14 1.84866 12.6982 0.546875 11.0924 0.546875ZM9.12598 5.69932L5.2977 7.52518C5.19569 7.57383 5.07786 7.49945 5.07786 7.38645V3.62061C5.07786 3.506 5.19879 3.43172 5.30101 3.48353L9.12929 5.42351C9.24311 5.48118 9.24113 5.64441 9.12598 5.69932Z"
        fill="#F61C0D"
      />
    </svg>
  );
};

export default Youtube;
