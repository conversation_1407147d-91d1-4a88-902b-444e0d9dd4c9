'use client';

import React from 'react';
import { HashLoader } from 'react-spinners';

interface LoaderProps {
  loading?: boolean;
  size?: number;
  color?: string;
  className?: string;
}

const Loader: React.FC<LoaderProps> = ({
  loading = true,
  size = 40,
  color = '#FF6E00',
  className = '',
}) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <HashLoader loading={loading} size={size} color={color} />
    </div>
  );
};

export default Loader;
