import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { AiOutlineCloseCircle } from 'react-icons/ai';

interface Iprops {
  className?: string;
  iconClass?: string;
  dialogClass?: string;
  classOverlay?: string;
  children: any;
  show: any;
  hide: any;
  title?: any;
  titleClass?: any;
  childClass?: any;
  closeModal?: any;
}

export default function Modal({
  show,
  hide,
  children,
  className,
  classOverlay,
  childClass,
  iconClass,
  dialogClass,
}: Iprops) {
  return (
    <Transition show={show} as={Fragment}>
      <Dialog
        as="div"
        className={`${dialogClass} fixed inset-0 z-[500000000]`}
        onClose={hide}
      >
        <div className="h-screem flex items-center justify-center px-4 pb-20 text-center sm:block md:p-0">
          {/* Add the `show` prop to this `Transition` */}
          <Transition show={show}>
            <div
              onClick={() => {
                hide(false);
              }}
              className={`${classOverlay} fixed inset-0 bg-black bg-opacity-60 transition-opacity`}
            ></div>
          </Transition>
          <span
            className="inline-block h-screen align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>
          <div
            className={`${className} hideScrollbar atScrollHide relative inline-block max-h-[calc(100vh-60px)] w-full overflow-hidden overflow-y-auto rounded-3xl border border-gray-400 bg-white px-4 py-5 text-left align-top sm:w-7/12 sm:align-middle md:max-h-[calc(100vh-20px)] lg:w-10/12`}
          >
            <div
              className={`${iconClass} absolute right-4 top-4 z-20 cursor-pointer text-right`}
              onClick={() => {
                hide(false);
              }}
            >
              <button
                type="button"
                className="hover:text-black-100 text-4xl text-gray-400 focus:outline-none"
              >
                <span className="sr-only">Close</span>
                {/* <Cross /> */}
                <AiOutlineCloseCircle className="text-3xl" />
              </button>
            </div>
            <div className={`${childClass} mt-5`}>{children}</div>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
