import PaginationNew from '@/components/ui/PaginationNew';
import React, { ReactElement } from 'react';
interface TableColumn {
  header: string | ReactElement;
  accessor: string;
  render?: (row: Record<string, any>) => ReactElement; // Custom rendering option
}

interface TableProps {
  data: Record<string, any>[];
  columns: TableColumn[];
  loading?: boolean;
  noDataMessage?: string | ReactElement;
  lastRowColumnCount?: number;
  headerStyle?: string;
  rowStyle?: string;
  onPageChange?: (page: number) => void;
  initialPage?: number; // Initial page number
  itemsPerPage?: number; // Number of items per page
  totalPages?: number; // Number of items per page
}

const Table: React.FC<TableProps> = ({
  data,
  columns,
  loading = false,
  noDataMessage,
  initialPage = 1,
  totalPages = 0,
  onPageChange,
  headerStyle,
  rowStyle,
}) => {
  const handlePageChange = (page: number) => {
    onPageChange?.(page);
  };
  return (
    <div className="hideScrollbar relative mt-3 overflow-x-auto">
      <table className="min-w-full">
        <thead>
          <tr>
            {columns.map((column, index) => (
              <th
                key={index}
                className={`${headerStyle} xs2:px-3 xs2:py-5 whitespace-nowrap border-b border-black px-4 py-7 pb-7 text-start text-lg font-bold leading-none text-black xs1:text-base ${
                  column.header == 'Score' && 'text-end'
                }`}
              >
                {column.header}
              </th>
            ))}
          </tr>
          {loading && (
            <tr>
              <td colSpan={columns.length} className={`px-5 py-7 text-center`}>
                <div className="flex h-[50vh] items-center justify-center py-6 text-black">
                  Loading ...
                </div>
              </td>
            </tr>
          )}
        </thead>
        <tbody>
          {data.length === 0 && !loading && (
            <tr>
              <td
                colSpan={columns.length}
                className="h5 py-4 text-center text-black"
              >
                {noDataMessage || 'No Data Available'}
              </td>
            </tr>
          )}
          {data.length > 0 &&
            data.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-b border-[#565656]">
                {columns.map((column, colIndex) => (
                  <td
                    key={colIndex}
                    className={`${rowStyle} ${
                      column.header == 'Score' && 'text-end'
                    } xs2:px-3 xs2:py-4 xs2:text-sm whitespace-nowrap px-5 py-5 text-lg text-black xs1:px-4 xs1:text-base`}
                  >
                    {row[column.accessor]}
                  </td>
                ))}
              </tr>
            ))}
        </tbody>
      </table>
      {totalPages > 1 && (
        <div className="mt-8 flex w-full justify-center">
          <PaginationNew
            currentPage={initialPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default Table;
