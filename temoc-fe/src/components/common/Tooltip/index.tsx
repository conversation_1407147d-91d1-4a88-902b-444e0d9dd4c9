import React from 'react';
import { BsInfoCircle } from 'react-icons/bs';
interface ToltipProps {
  description?: string;
}
const index = ({ description }: ToltipProps) => {
  return (
    <div className="group absolute right-2 top-1/2 -translate-y-1/2 cursor-pointer">
      <BsInfoCircle className="text-[#666666]" />
      <p className="absolute bottom-full right-0 hidden w-[200px] rounded-md border border-borderColor bg-white p-2 text-center text-xs !leading-[14px] shadow-md group-hover:block">
        {description}
      </p>
    </div>
  );
};

export default index;
