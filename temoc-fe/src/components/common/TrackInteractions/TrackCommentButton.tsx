'use client';

import React from 'react';
import { FaComment, FaRegComment } from 'react-icons/fa';

interface TrackCommentButtonProps {
  count: number;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  className?: string;
  onClick: () => void;
}

export const TrackCommentButton: React.FC<TrackCommentButtonProps> = ({
  count,
  size = 'md',
  showCount = true,
  className = '',
  onClick,
}) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const iconSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  return (
    <button
      onClick={(e) => {
        e.stopPropagation();
        onClick();
      }}
      className={`flex items-center gap-1 text-gray-500 transition-colors hover:text-primary ${
        sizeClasses[size]
      } ${className}`}
      title="View comments"
    >
      {count > 0 ? (
        <FaComment className={iconSizeClasses[size]} />
      ) : (
        <FaRegComment className={iconSizeClasses[size]} />
      )}
      {showCount && (
        <span className="font-medium">
          {count > 999 ? `${(count / 1000).toFixed(1)}k` : count}
        </span>
      )}
    </button>
  );
};
