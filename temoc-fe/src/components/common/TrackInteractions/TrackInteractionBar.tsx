'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaReg<PERSON><PERSON>t, FaComment, FaRegComment } from 'react-icons/fa';
import { useQuery } from '@tanstack/react-query';
import { trackInteractionService } from '@/services/track-interaction.service';
import { useAuth } from '@/hooks/useAuth';
import { useTrackInteractions } from '@/hooks/useTrackInteractions';

interface TrackInteractionBarProps {
  trackId: string;
  onCommentClick: () => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const TrackInteractionBar: React.FC<TrackInteractionBarProps> = ({
  trackId,
  onCommentClick,
  size = 'sm',
  className = '',
}) => {
  const { user } = useAuth();
  const { stats, toggleLike, isLiking } = useTrackInteractions({
    trackId,
    userId: user?._id,
  });

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const iconSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  const formatCount = (count: number) => {
    if (count > 999) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  return (
    <div className={`flex items-center gap-3 ${sizeClasses[size]} ${className}`}>
      {/* Like Button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          toggleLike();
        }}
        disabled={isLiking}
        className={`flex items-center gap-1 transition-colors hover:text-red-500 ${
          stats?.userLiked ? 'text-red-500' : 'text-gray-500'
        } ${isLiking ? 'opacity-50 cursor-not-allowed' : ''}`}
        title={stats?.userLiked ? 'Unlike track' : 'Like track'}
      >
        {stats?.userLiked ? (
          <FaHeart className={`${iconSizeClasses[size]} animate-pulse`} />
        ) : (
          <FaRegHeart className={iconSizeClasses[size]} />
        )}
        <span className="font-medium">
          {formatCount(stats?.likesCount || 0)}
        </span>
      </button>

      {/* Comment Button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onCommentClick();
        }}
        className={`flex items-center gap-1 text-gray-500 transition-colors hover:text-primary ${sizeClasses[size]}`}
        title="View comments"
      >
        {(stats?.commentsCount || 0) > 0 ? (
          <FaComment className={iconSizeClasses[size]} />
        ) : (
          <FaRegComment className={iconSizeClasses[size]} />
        )}
        <span className="font-medium">
          {formatCount(stats?.commentsCount || 0)}
        </span>
      </button>
    </div>
  );
};
