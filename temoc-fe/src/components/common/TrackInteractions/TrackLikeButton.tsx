'use client';

import React, { useState } from 'react';
import { FaHeart, FaRegHeart } from 'react-icons/fa';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { trackInteractionService } from '@/services/track-interaction.service';
import { toast } from 'react-toastify';

interface TrackLikeButtonProps {
  trackId: string;
  initialLiked: boolean;
  initialCount: number;
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  className?: string;
}

export const TrackLikeButton: React.FC<TrackLikeButtonProps> = ({
  trackId,
  initialLiked,
  initialCount,
  size = 'md',
  showCount = true,
  className = '',
}) => {
  const [liked, setLiked] = useState(initialLiked);
  const [count, setCount] = useState(initialCount);
  const queryClient = useQueryClient();

  const likeMutation = useMutation({
    mutationFn: () => trackInteractionService.toggleLike(trackId),
    onMutate: async () => {
      // Optimistic update
      const newLiked = !liked;
      const newCount = newLiked ? count + 1 : count - 1;
      
      setLiked(newLiked);
      setCount(newCount);

      return { previousLiked: liked, previousCount: count };
    },
    onError: (error, variables, context) => {
      // Revert optimistic update on error
      if (context) {
        setLiked(context.previousLiked);
        setCount(context.previousCount);
      }
      toast.error('Failed to update like status');
      console.error('Like error:', error);
    },
    onSuccess: (data) => {
      // Update with server response
      setLiked(data.liked);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['track-stats', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-likes', trackId] });
      
      toast.success(data.liked ? 'Track liked!' : 'Track unliked');
    },
  });

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    likeMutation.mutate();
  };

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const iconSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  return (
    <button
      onClick={handleClick}
      disabled={likeMutation.isPending}
      className={`flex items-center gap-1 transition-colors hover:text-red-500 ${
        liked ? 'text-red-500' : 'text-gray-500'
      } ${sizeClasses[size]} ${className} ${
        likeMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      title={liked ? 'Unlike track' : 'Like track'}
    >
      {liked ? (
        <FaHeart className={`${iconSizeClasses[size]} animate-pulse`} />
      ) : (
        <FaRegHeart className={iconSizeClasses[size]} />
      )}
      {showCount && (
        <span className="font-medium">
          {count > 999 ? `${(count / 1000).toFixed(1)}k` : count}
        </span>
      )}
    </button>
  );
};
