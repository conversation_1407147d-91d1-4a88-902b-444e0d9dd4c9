'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Artist } from '@/services/discovery';
import { FollowButton } from '@/components/social/FollowButton';
import { VerifiedBadge } from '@/components/ui/VerifiedBadge';
import { TokenBadge } from '@/components/ui/TokenBadge';
import { useFollowSync } from '@/hooks/useFollowSync';

interface ArtistCardProps {
  artist: Artist;
}

export function ArtistCard({ artist }: ArtistCardProps) {
  const { isFollowing, isLoading, handleFollowToggle, loadingAction } =
    useFollowSync({
      userId: artist._id,
      displayName: artist.displayName,
      initialFollowStatus: artist.isFollowing, // Use the status from discovery API
    });

  // Get the cover image - prioritize artist cover photo, then user cover picture, then gradient
  const getCoverImage = () => {
    return artist.artistProfile?.coverPhoto || artist.coverPicture || null; // Return null to show gradient background
  };

  // Get the profile image - prioritize artist profile pic, then user avatar
  const getProfileImage = () => {
    return artist.artistProfile?.profilePic || artist.avatarUrl || null;
  };

  return (
    <div className="flex h-full flex-col overflow-hidden rounded-lg bg-white shadow-md transition-shadow duration-200 hover:shadow-lg">
      {/* Cover Image */}
      <div className="relative h-32 bg-gradient-to-r from-primary to-yellow-400">
        {getCoverImage() && (
          <Image
            src={getCoverImage()!}
            alt={`${artist.displayName} cover`}
            fill
            className="object-cover"
          />
        )}
      </div>

      {/* Profile Section */}
      <div className="relative flex flex-1 flex-col px-4 pb-4">
        {/* Avatar */}
        <div className="relative -mt-8 mb-3">
          <div className="h-16 w-16 overflow-hidden rounded-full border-4 border-white bg-gray-200">
            {getProfileImage() ? (
              <Image
                src={getProfileImage()!}
                alt={artist.displayName}
                width={64}
                height={64}
                className="h-full w-full object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-gray-300">
                <span className="text-lg font-semibold text-gray-600">
                  {artist.displayName.charAt(0).toUpperCase()}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Artist Info */}
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <Link
              href={`/app/discovery/${artist.username}`}
              className="font-semibold text-gray-900 transition-colors hover:text-primary"
            >
              {artist.displayName}
            </Link>
            {artist.artistProfile.isVerified && <VerifiedBadge />}
          </div>

          {/* <p className="mb-1 text-sm text-gray-600">@{artist.username}</p> */}

          {artist.artistProfile.genre && (
            <p className="text-xs font-medium text-primary">
              {artist.artistProfile.genre}
            </p>
          )}
        </div>

        {/* Bio */}
        {/* {artist.artistProfile.bio && (
          <p className="mb-3 line-clamp-2 text-sm text-gray-700">
            {artist.artistProfile.bio}
          </p>
        )} */}

        {/* Stats */}
        <div className="mb-3 flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>{artist.followersCount} followers</span>
            {artist.hasToken && <TokenBadge count={artist.tokenCount} />}
          </div>
        </div>

        {/* Tokens */}
        {artist.tokens.length > 0 && (
          <div className="mb-3">
            <div className="flex flex-wrap gap-1">
              {artist.tokens.slice(0, 2).map((token, index) => (
                <span
                  key={index}
                  className="inline-flex items-center rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-primary"
                >
                  ${token.symbol}
                </span>
              ))}
              {artist.tokens.length > 2 && (
                <span className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-600">
                  +{artist.tokens.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Location */}
        {artist.location && (
          <p className="mb-3 flex items-center gap-1 text-xs text-gray-500">
            <svg
              className="h-3 w-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            {artist.location}
          </p>
        )}

        {/* Spacer to push actions to bottom */}
        <div className="flex-1"></div>

        {/* Actions */}
        <div className="flex gap-2">
          <FollowButton
            isFollowing={isFollowing}
            isLoading={isLoading}
            onClick={handleFollowToggle}
            className="flex-1"
            loadingAction={loadingAction || undefined}
          />

          <Link
            href={`/app/discovery/${artist.username}`}
            className="whitespace-nowrap rounded-md bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
          >
            View Profile
          </Link>
        </div>
      </div>
    </div>
  );
}
