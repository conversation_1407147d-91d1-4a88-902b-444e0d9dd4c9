'use client';
import { Artist } from '@/services/discovery';
import { useFollowSync } from '@/hooks/useFollowSync';
import { Verfied } from '../common/Icons';
import Link from 'next/link';
import { FollowButton } from '../social/FollowButton';

interface ArtistCardProps {
  artist: Artist;
}

export function ArtistNewCard({ artist }: ArtistCardProps) {
  console.log(artist, 'artist data');
  const { isFollowing, isLoading, handleFollowToggle, loadingAction } =
    useFollowSync({
      userId: artist._id,
      displayName: artist.displayName,
      initialFollowStatus: artist.isFollowing, // Use the status from discovery API
    });

  // Get the cover image - prioritize artist cover photo, then user cover picture, then gradient
  //   const getCoverImage = () => {
  //     return artist.artistProfile?.coverPhoto || artist.coverPicture || null; // Return null to show gradient background
  //   };

  // Get the profile image - prioritize artist profile pic, then user avatar
  //   const getProfileImage = () => {
  //     return artist.artistProfile?.profilePic || artist.avatarUrl || null;
  //   };
  const getProfileImage = () => {
    return (
      artist.artistProfile?.profilePic ||
      artist.avatarUrl ||
      '/assets/images/avatar.webp'
    );
  };

  return (
    <Link href={`/app/discovery/${artist.username}`}>
      <div className="group w-full rounded-[10px] bg-white p-4 shadow-[0_20px_1px_rgba(0,0,0,0.1)]">
        <div className="aspect-h-1 aspect-w-1 overflow-hidden rounded-md">
          <img
            src={getProfileImage()!}
            alt={`${artist.displayName} cover`}
            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
          />
        </div>
        {/* <ImageComponent
        src={imageSrc}
        height={220}
        width={220}
        className="w-full"
      /> */}
        <div className="mt-4">
          <div className="flex items-center justify-between gap-1">
            <p className="w-[90%] truncate text-xs !font-medium sm:text-sm">
              {artist?.displayName}
            </p>
            {artist?.artistProfile?.isVerified && (
              <span className="flex-shrink-0">
                <Verfied />
              </span>
            )}
            {/* <span className="flex-shrink-0">
              <Verfied />
            </span> */}
          </div>
          <div className="mt-1 flex items-center justify-between gap-2">
            <span className="text-xs text-[#666666]">Followers</span>
            <p className="text-xs !font-medium text-[#FF8000]">
              <span className="">{artist.followersCount} </span>
            </p>
            <FollowButton
              isFollowing={isFollowing}
              isLoading={isLoading}
              onClick={handleFollowToggle}
              className="flex-1"
              loadingAction={loadingAction || undefined}
            />
          </div>
        </div>
      </div>
    </Link>
  );
}
