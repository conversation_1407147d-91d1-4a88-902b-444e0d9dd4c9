'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  DiscoveryFilters as FiltersType,
  discoveryApi,
} from '@/services/discovery';
import { LuFilter } from 'react-icons/lu';

interface DiscoveryFiltersProps {
  filters: FiltersType;
  onFiltersChange: (filters: FiltersType) => void;
}

export function DiscoveryFilters({
  filters,
  onFiltersChange,
}: DiscoveryFiltersProps) {
  const [localFilters, setLocalFilters] = useState<FiltersType>(filters);

  // Get available genres
  const { data: genresData } = useQuery({
    queryKey: ['discovery', 'genres'],
    queryFn: () => discoveryApi.getGenres(),
  });

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof FiltersType, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleGenreToggle = (genre: string) => {
    const currentGenres = localFilters.genre || [];
    const newGenres = currentGenres.includes(genre)
      ? currentGenres.filter((g) => g !== genre)
      : [...currentGenres, genre];

    handleFilterChange('genre', newGenres.length > 0 ? newGenres : undefined);
  };

  const clearFilters = () => {
    const clearedFilters: FiltersType = {
      sortBy: 'popular',
      sortOrder: 'desc',
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = !!(
    localFilters.genre?.length ||
    localFilters.isVerified !== undefined ||
    localFilters.hasToken !== undefined ||
    localFilters.location
  );

  return (
    <div className="top-0 w-[300px] flex-shrink-0 bg-white p-5 md:rounded-[10px] lg:sticky lg:w-64">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="flex items-center gap-1 text-lg font-semibold text-gray-900">
          <LuFilter className="h-6 w-6 text-black" />
          Filters
        </h3>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm font-medium text-primary hover:text-orange-700"
          >
            Clear all
          </button>
        )}
      </div>
      <hr className="h-[1px] w-full bg-borderColor" />

      {/* Sort By */}
      <div className="mt-3">
        <label className="mb-1 block pl-1 text-sm font-medium text-[#666666]">
          Sort by
        </label>
        <select
          value={localFilters.sortBy || 'popular'}
          onChange={(e) => handleFilterChange('sortBy', e.target.value as any)}
          className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
        >
          <option value="popular">Most Popular</option>
          <option value="newest">Newest</option>
          <option value="alphabetical">Alphabetical</option>
          <option value="mostActive">Most Active</option>
        </select>
      </div>
      <hr className="my-5 block h-[1px] w-full bg-borderColor" />
      {/* Verification Status */}
      <div className="">
        <label className="mb-1 block pl-1 text-sm font-medium text-[#666666]">
          Verification
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="verification"
              checked={localFilters.isVerified === undefined}
              onChange={() => handleFilterChange('isVerified', undefined)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              All Artists
            </span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="verification"
              checked={localFilters.isVerified === true}
              onChange={() => handleFilterChange('isVerified', true)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              Verified Only
            </span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="verification"
              checked={localFilters.isVerified === false}
              onChange={() => handleFilterChange('isVerified', false)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              Unverified Only
            </span>
          </label>
        </div>
      </div>
      <hr className="my-5 block h-[1px] w-full bg-borderColor" />
      {/* Token Status */}
      <div className="">
        <label className="mb-1 block pl-1 text-sm font-medium text-[#666666]">
          Token Status
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="tokens"
              checked={localFilters.hasToken === undefined}
              onChange={() => handleFilterChange('hasToken', undefined)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              All Artists
            </span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="tokens"
              checked={localFilters.hasToken === true}
              onChange={() => handleFilterChange('hasToken', true)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              With Tokens
            </span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="tokens"
              checked={localFilters.hasToken === false}
              onChange={() => handleFilterChange('hasToken', false)}
              className="h-4 w-4 border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
            />
            <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
              Without Tokens
            </span>
          </label>
        </div>
      </div>
      <hr className="my-5 block h-[1px] w-full bg-borderColor" />
      {/* Genres */}
      {genresData && (
        <div className="mb-6">
          <label className="mb-1 block pl-1 text-sm font-medium text-[#666666]">
            Genres
          </label>
          <div className="max-h-48 space-y-2 overflow-y-auto">
            {genresData.genres.map((genre) => (
              <label key={genre} className="flex items-center pl-1">
                <input
                  type="checkbox"
                  checked={localFilters.genre?.includes(genre) || false}
                  onChange={() => handleGenreToggle(genre)}
                  className="h-4 w-4 rounded-sm border-gray-300 text-primary focus:h-3 focus:w-3 focus:ring-primary"
                />
                <span className="ml-2 cursor-pointer text-sm text-[#333333] hover:text-black">
                  {genre}
                </span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Location */}
      {/* <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Location
        </label>
        <input
          type="text"
          value={localFilters.location || ''}
          onChange={(e) => handleFilterChange('location', e.target.value || undefined)}
          placeholder="Enter location..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        />
      </div> */}
    </div>
  );
}
