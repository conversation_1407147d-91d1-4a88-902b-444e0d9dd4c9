'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Trending<PERSON>rtist, PopularArtist, Artist } from '@/services/discovery';
import { VerifiedBadge } from '@/components/ui/VerifiedBadge';

interface TrendingSectionProps {
  trendingArtists?: TrendingArtist[];
  popularArtists?: PopularArtist[];
  newVerifiedArtists?: Artist[];
}

export function TrendingSection({
  trendingArtists,
  popularArtists,
  newVerifiedArtists,
}: TrendingSectionProps) {
  return (
    <div className="bd mb-8 space-y-8">
      {/* Trending Artists */}
      {trendingArtists && trendingArtists.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="flex items-center gap-2 text-xl font-semibold text-gray-900">
              <svg
                className="h-5 w-5 text-red-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                  clipRule="evenodd"
                />
              </svg>
              Trending This Week
            </h2>
            <Link
              href="/app/discovery?sortBy=popular"
              className="text-sm font-medium text-primary hover:text-orange-700"
            >
              View all
            </Link>
          </div>

          <div className="flex gap-4 overflow-x-auto pb-4">
            {trendingArtists.slice(0, 5).map((artist) => (
              <Link
                key={artist._id}
                href={`/app/discovery/${artist.username}`}
                className="w-48 flex-shrink-0 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
              >
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    {artist.artistProfile?.profilePic || artist.avatarUrl ? (
                      <Image
                        src={
                          artist.artistProfile?.profilePic || artist.avatarUrl!
                        }
                        alt={artist.displayName}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-300">
                        <span className="font-semibold text-gray-600">
                          {artist.displayName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1">
                      <p className="truncate font-medium text-gray-900">
                        {artist.displayName}
                      </p>
                      {artist.artistProfile.isVerified && (
                        <VerifiedBadge size="sm" />
                      )}
                    </div>
                    <p className="truncate text-xs text-gray-600">
                      @{artist.username}
                    </p>
                    {artist.artistProfile.genre && (
                      <p className="truncate text-xs text-primary">
                        {artist.artistProfile.genre}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mt-2 text-xs text-gray-500">
                  +{artist.recentFollowersCount} new followers
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Popular Artists */}
      {popularArtists && popularArtists.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="flex items-center gap-2 text-xl font-semibold text-gray-900">
              <svg
                className="h-5 w-5 text-yellow-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
              Most Popular
            </h2>
            <Link
              href="/app/discovery?sortBy=popular"
              className="text-sm font-medium text-primary hover:text-orange-600"
            >
              View all
            </Link>
          </div>

          <div className="flex gap-4 overflow-x-auto pb-4">
            {popularArtists.slice(0, 5).map((artist) => (
              <Link
                key={artist._id}
                href={`/app/discovery/${artist.username}`}
                className="w-48 flex-shrink-0 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
              >
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    {artist.avatarUrl ? (
                      <Image
                        src={artist.avatarUrl}
                        alt={artist.displayName}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-300">
                        <span className="font-semibold text-gray-600">
                          {artist.displayName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1">
                      <p className="truncate font-medium text-gray-900">
                        {artist.displayName}
                      </p>
                      {artist.artistProfile.isVerified && (
                        <VerifiedBadge size="sm" />
                      )}
                    </div>
                    <p className="truncate text-xs text-gray-600">
                      @{artist.username}
                    </p>
                    {artist.artistProfile.genre && (
                      <p className="truncate text-xs text-primary">
                        {artist.artistProfile.genre}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mt-2 text-xs text-gray-500">
                  {artist.followersCount} followers
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* New Verified Artists */}
      {newVerifiedArtists && newVerifiedArtists.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="flex items-center gap-2 text-xl font-semibold text-gray-900">
              <svg
                className="h-5 w-5 text-blue-500"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              Newly Verified
            </h2>
            <Link
              href="/app/discovery?isVerified=true&sortBy=newest"
              className="text-sm font-medium text-primary hover:text-orange-700"
            >
              View all
            </Link>
          </div>

          <div className="flex gap-4 overflow-x-auto pb-4">
            {newVerifiedArtists.slice(0, 5).map((artist) => (
              <Link
                key={artist._id}
                href={`/app/discovery/${artist.username}`}
                className="w-48 flex-shrink-0 rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md"
              >
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 overflow-hidden rounded-full bg-gray-200">
                    {artist.avatarUrl ? (
                      <Image
                        src={artist.avatarUrl}
                        alt={artist.displayName}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center bg-gray-300">
                        <span className="font-semibold text-gray-600">
                          {artist.displayName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-1">
                      <p className="truncate font-medium text-gray-900">
                        {artist.displayName}
                      </p>
                      <VerifiedBadge size="sm" />
                    </div>
                    <p className="truncate text-xs text-gray-600">
                      @{artist.username}
                    </p>
                    {artist.artistProfile.genre && (
                      <p className="truncate text-xs text-primary">
                        {artist.artistProfile.genre}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mt-2 text-xs text-gray-500">
                  Recently verified
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
