'use client';

import NoCollection from '@/components/common/Icons/NoCollection';
import AlbumCard from '@/components/ui/Card/AlbumCard';
import NoData from '@/components/ui/NoData';
import { libraryService } from '@/services/library.service';
import { useQuery } from '@tanstack/react-query';
import React, { useContext, useState } from 'react';
import Skeleton from 'react-loading-skeleton';
import TokenContentModal from './TokenContentModal';
import { Lock } from 'lucide-react';
import { transactionsService } from '@/services/transaction.service';
import { useTokenBalance } from '@/hooks/useTokenBalance';
import { useEthersSigner } from '@/hooks/useEthersSigner';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { tokenService } from '@/services/token.service';
import { ChainContext } from '@/context/ChainContextProvider';

interface LibraryAlbumsProps {
  profile: any;
  setAlbumId: any;
  setCurrentTab: any;
}

const ArtistAlbum: React.FC<LibraryAlbumsProps> = ({
  profile,
  setAlbumId,
  setCurrentTab,
}) => {
  const [show, setShow] = useState(false);
  const [albumData, setAlbumData] = useState();
  const { chain } = useContext(ChainContext);
  const signer = useEthersSigner({ chainId: chain.id });
  const { primaryWallet } = useDynamicContext();

  const { data } = useQuery({
    queryKey: ['artist_token'],
    queryFn: () => tokenService.getArtistToken(profile?._id),
  });

  const token = data?.data?.[0];
  const { balance } = useTokenBalance(
    signer,
    primaryWallet?.address,
    token?.address,
  );

  // Fetch all albums by creator
  const {
    data: allAlbums,
    isLoading: isAlbumsLoading,
    refetch,
  } = useQuery({
    queryKey: ['creator_albums', profile?._id],
    queryFn: () => libraryService.getAlbumsByCreator(profile?._id),
    enabled: !!profile?._id,
  });

  const albums = allAlbums?.data || [];

  // Fetch access for all albums
  const { data: accessMap = {}, refetch: refetchAccessMap } = useQuery({
    queryKey: ['access_map', albums.map((a: any) => a._id)],
    queryFn: async () => {
      const results = await Promise.all(
        albums.map(async (album: any) => {
          try {
            const res = await transactionsService.getUserAccess(album._id);
            return [album._id, res?.data?.hasAccess ?? false];
          } catch (e) {
            console.log(e);

            return [album._id, false];
          }
        }),
      );
      return Object.fromEntries(results);
    },
    enabled: albums.length > 0,
  });

  React.useEffect(() => {
    const handleFocus = () => {
      refetchAccessMap();
      refetch();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchAccessMap, refetch]);

  return (
    <div className="p-4">
      {isAlbumsLoading ? (
        <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-5 xl:grid-cols-6">
          {Array(6)
            .fill(null)
            .map((_, idx) => (
              <Skeleton key={idx} height={200} borderRadius={8} />
            ))}
        </div>
      ) : albums.length > 0 ? (
        <div className="mt-5 grid grid-cols-1 gap-3 sm:grid-cols-3 md:grid-cols-4 md:gap-5 xl:grid-cols-6">
          {albums.map((album: any) => {
            const tokenStatus = token?.status; // 'presale' or 'launched'
            const isPresale = tokenStatus === 'presale';
            const isLaunched = tokenStatus === 'launched';

            let albumHasAccess = false;

            if (isPresale) {
              albumHasAccess = accessMap[album._id] ?? false;
            } else if (isLaunched) {
              const required = parseFloat(album?.requiredTokenAmount || '0');
              albumHasAccess =
                parseFloat(balance.toString() || '0') >= required;
            }

            const isLocked = !albumHasAccess && album.requiresTokens;

            return (
              <div className="relative" key={album._id}>
                {isLocked && (
                  <div
                    className="absolute left-1/2 top-1/2 z-20 -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full bg-primary p-3"
                    onClick={() => {
                      setShow(true);
                      setAlbumData(album);
                    }}
                  >
                    <Lock className="text-white" />
                  </div>
                )}

                <div
                  className={`${isLocked ? 'blur-sm' : ''}`}
                  onClick={() => {
                    if (!isLocked) {
                      setAlbumId(album._id);
                      setCurrentTab('track');
                    }
                  }}
                >
                  <div
                    className="relative cursor-pointer"
                    onClick={(e) => e.preventDefault()}
                  >
                    <AlbumCard
                      imageSrc={album?.thumbnailUrl}
                      name={album.title}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <NoData
          icon={<NoCollection />}
          heading="No albums created"
          description="You haven’t created any albums yet. Albums help you group related tracks, making it easier for listeners to discover and enjoy your music."
        />
      )}

      <TokenContentModal
        show={show}
        hide={setShow}
        profile={profile}
        albumData={albumData}
      />
    </div>
  );
};

export default ArtistAlbum;
