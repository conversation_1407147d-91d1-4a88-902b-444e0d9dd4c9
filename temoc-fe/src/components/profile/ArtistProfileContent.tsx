'use client';
import React, { useState } from 'react';
import ArtistProfileSocialLinks from './ArtistProfileSocialLinks';
import ArtistProfileResponsiveTab from './ArtistProfileResponsiveTab';
interface ArtistProfileContentProps {
  profile: any;
  artistContent: any;
  followStats: any;
}

const ArtistProfileContent: React.FC<ArtistProfileContentProps> = ({
  profile,
  artistContent,
  followStats,
}) => {
  const [exploreDetail] = useState(true); // Always show artist detail view

  return (
    <div>
      {exploreDetail ? (
        <div>
          {/* Desktop Layout */}
          <div className="mt-7 hidden sm:block sm:pr-4">
            {/* <ArtistProfileSummary profile={profile} followStats={followStats} /> */}
            <ArtistProfileSocialLinks
              profile={profile}
              followStats={followStats}
              artistContent={artistContent}
            />
          </div>

          {/* Mobile Layout */}
          <div className="block sm:hidden">
            <ArtistProfileResponsiveTab
              profile={profile}
              artistContent={artistContent}
              followStats={followStats}
            />
          </div>
        </div>
      ) : (
        // This would be the explore view, but we always show artist detail
        <div className="p-7">
          <p className="w-max text-[22px] font-normal">EXPLORE</p>
        </div>
      )}
    </div>
  );
};

export default ArtistProfileContent;
