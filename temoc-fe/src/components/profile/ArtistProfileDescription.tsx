import React, { useState } from 'react';

interface ArtistProfileSummaryProps {
  profile: {
    description: string;
    // Add other fields if needed
  };
  followStats?: any; // optional if unused
}

const ArtistProfileDescription: React.FC<ArtistProfileSummaryProps> = ({
  profile,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const charLimit = 150;

  const toggleReadMore = () => setIsExpanded(!isExpanded);

  const shouldTruncate = profile.description.length > charLimit;
  const displayedText = isExpanded
    ? profile.description
    : profile.description.slice(0, charLimit) + (shouldTruncate ? '...' : '');

  return (
    <div className="mb-5 w-full rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:mb-[30px] sm:p-6">
      <h3 className="text-sm font-normal sm:text-lg">Description</h3>
      <p className="mt-2 text-xs font-normal text-[#666666] sm:mt-4 sm:text-base">
        {displayedText}{' '}
        {shouldTruncate && (
          <button
            onClick={toggleReadMore}
            className="ml-1 text-xs text-orange-500 underline sm:text-sm"
          >
            {isExpanded ? 'Read Less' : 'Read More'}
          </button>
        )}
      </p>
    </div>
  );
};

export default ArtistProfileDescription;
