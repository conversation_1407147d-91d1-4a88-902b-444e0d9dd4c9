import React, { JSX, useState } from 'react';
import {
  FaSpotify,
  FaYoutube,
  FaTiktok,
  FaInstagram,
  FaXTwitter,
} from 'react-icons/fa6';
import { MdVerified } from 'react-icons/md';
// import { IoPlay } from 'react-icons/io5';
import { Button } from '@/components/common';
import UniSwap from '@/components/common/Icons/UniSwap';
import Taj from '@/components/common/Icons/Taj';
import ImageComponent from '@/components/common/ImageComponent';
import { IoIosArrowBack } from 'react-icons/io';
import Dropdown from '@/components/ui/Dropdown';
import { FollowButton } from '@/components/social/FollowButton';
import { useQuery } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';
import Modal from '../common/Modal';
import BuyModal from '@/app/app/trading/Presale/BuyModal';
import { presaleService } from '@/services/presale.service';

interface ArtistProfileHeaderProps {
  profile: any;
  followStats: any;
  isFollowing: boolean;
  isFollowLoading: boolean;
  handleFollowToggle: () => void;
  loadingAction: any;
  isOwnProfile: boolean;
  onReportClick: () => void;
}

const ArtistProfileHeader: React.FC<ArtistProfileHeaderProps> = ({
  profile,
  followStats,
  isFollowing,
  isFollowLoading,
  handleFollowToggle,
  loadingAction,
  isOwnProfile,
  onReportClick,
}) => {
  // Get social media icons based on artist's social links
  const getSocialIcons = () => {
    const socialLinks = profile.artistProfile?.socialLinks || [];
    const iconMap: { [key: string]: JSX.Element } = {
      youtube: <FaYoutube />,
      spotify: <FaSpotify />,
      instagram: <FaInstagram />,
      twitter: <FaXTwitter />,
      tiktok: <FaTiktok />,
    };

    // If artist has social links, show their actual platforms
    if (socialLinks.length > 0) {
      return socialLinks.slice(0, 5).map((link: any, index: number) => (
        <a
          key={index}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className="cursor-pointer text-white transition-colors hover:text-yellow-300"
        >
          {iconMap[link.platform.toLowerCase()] || <FaInstagram />}
        </a>
      ));
    }

    // Default social icons if no links provided
    // return [
    //   <FaYoutube key="youtube" />,
    //   <FaSpotify key="spotify" />,
    //   <FaInstagram key="instagram" />,
    //   <FaXTwitter key="twitter" />,
    //   <FaTiktok key="tiktok" />,
    // ];
  };

  // Get the cover image - prioritize artist cover photo, then user cover picture, then default
  const getCoverImage = () => {
    return (
      profile.artistProfile?.coverPhoto ||
      profile.coverPicture ||
      '/assets/images/artist/bg.png'
    );
  };

  // Get the profile image - prioritize artist profile pic, then user avatar, then default
  const getProfileImage = () => {
    return (
      profile.artistProfile?.profilePic ||
      profile.avatarUrl ||
      '/assets/images/placeholder.avif'
    );
  };

  const [openModal, setOpenModal] = useState(false);

  const { data: preData } = useQuery({
    queryKey: ['presale', profile?._id],
    queryFn: () => presaleService.getPresale(profile?._id as string),
    enabled: !!profile?._id,
  });

  const albumData = preData?.data || {};

  const { data } = useQuery({
    queryKey: ['artist_token'],
    queryFn: () => tokenService.getArtistToken(profile?._id),
    enabled: !!profile?._id,
  });

  const token = data?.data?.[0];

  return (
    <>
      <div>
        <div
          className="Atbg relative flex w-full flex-col flex-wrap items-start justify-between pt-4 sm:h-[300px] sm:p-2.5 md:flex-row lg:items-end lg:pt-0"
          // style={{
          //   backgroundImage: `url(${getCoverImage()})`,
          // }}
        >
          <ImageComponent
            src={getCoverImage()}
            fill
            figClassName="w-full h-full !absolute left-0 top-0"
            className="object-cover"
          />
          <div className="absolute left-0 top-0 h-full w-full bg-black/30"></div>
          {/* Back Button - Desktop */}
          <div className="absolute left-2 top-2 z-20 hidden lg:block">
            <Button
              className="!h-[43px] !w-[113px] !border-none !bg-white !text-sm !text-primary"
              onClick={() => window.history.back()}
            >
              <IoIosArrowBack className="text-[#181818]" /> Back
            </Button>
          </div>

          {/* Back Button - Mobile */}
          <IoIosArrowBack
            className="relative z-20 block cursor-pointer text-xl font-bold text-white lg:hidden"
            onClick={() => window.history.back()}
          />

          <div className="sm:pl- relative z-20 flex flex-row gap-2 pl-2 sm:gap-4">
            <div className="flex flex-col items-center justify-center">
              <ImageComponent
                src={getProfileImage()}
                alt={profile.displayName}
                figClassName="sm:h-[160px] sm:w-[160px] h-20 w-20 rounded-full"
                className="rounded-full"
                fill
              />
              <div className="mt-2 flex gap-1 text-xs text-white sm:gap-3">
                {getSocialIcons()}
              </div>
            </div>
            <div>
              <div className="flex items-center gap-1 text-xs text-white">
                {profile.artistProfile?.isVerified && (
                  <>
                    <MdVerified className="text-blue-500" />
                    Verified Artist
                  </>
                )}
              </div>
              <h3 className="text-xl font-semibold capitalize text-white sm:mt-1 sm:text-start sm:text-5xl xl:text-[60px]">
                {profile.firstName} {profile.lastName}
              </h3>

              <div className="flex flex-wrap items-center gap-3.5 sm:mt-6">
                <p className="text-xs text-white">
                  Followed by {followStats?.followersCount || 0} fans
                </p>
                <div className="flex flex-nowrap items-center justify-center gap-1 sm:flex-wrap sm:gap-2.5 xs1:flex-wrap">
                  {/* <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary sm:h-[30px] sm:w-[30px]">
                    <IoPlay className="text-[8px] text-white" />
                  </div> */}

                  {!isOwnProfile ? (
                    <FollowButton
                      isFollowing={isFollowing}
                      isLoading={isFollowLoading}
                      onClick={handleFollowToggle}
                      loadingAction={loadingAction}
                      className="!h-[22px] !w-[85px] !text-[8px] sm:!h-[36px] sm:!w-[142px] sm:!text-xs"
                      // @ts-expect-error
                      variant="outline"
                    />
                  ) : (
                    <Button
                      variant="outline"
                      className="!h-[22px] !w-[85px] !rounded-md !text-[8px] sm:!h-[36px] sm:!w-[142px] sm:!text-xs"
                    >
                      EDIT PROFILE
                    </Button>
                  )}

                  <Button
                    onClick={() => {
                      if (token?.status === 'presale') {
                        setOpenModal(true);
                      } else if (
                        token?.status === 'launched' &&
                        token?.address
                      ) {
                        const uniswapUrl = `https://app.uniswap.org/explore/tokens/ethereum_sepolia/${token.address}`;
                        window.open(uniswapUrl, '_blank');
                      }
                    }}
                    className="!h-[22px] !w-[85px] !rounded-md !text-[8px] uppercase sm:!h-[36px] sm:!w-[166px] sm:!text-xs"
                  >
                    Get ${token?.name} Coins
                  </Button>
                  {/* <Button className="!h-[22px] !w-[65px] !rounded-md !text-[8px] uppercase sm:!h-[36px] sm:!w-[126px] sm:!text-xs">
                    Send a Gift
                  </Button> */}
                  {!isOwnProfile && <Dropdown onReportClick={onReportClick} />}
                </div>
              </div>
            </div>
          </div>

          {/* Token Info - Mobile */}
          {/* <div className="relative z-20 mt-4 flex w-full flex-row items-center justify-between rounded-md bg-white/10 px-3 py-2.5 sm:mt-0 sm:hidden sm:w-auto sm:space-y-2.5 md:flex-col xl:flex xs:flex-col">
            <p className="flex w-full items-center justify-between gap-0.5 !text-[10px] text-white sm:!text-xs">
              <span className="flex items-center gap-1">
                <FaCoins className="text-[#FF6E00]" />${token?.name}
              </span>
              <span className="ml-6 text-[10px] font-medium text-[#FF6E00] sm:text-base">
                0 holders
              </span>
            </p>
            <p className="flex w-full items-center justify-between gap-0.5 !text-[10px] text-white sm:!text-xs">
              <span className="flex items-center gap-1">
                <FaDollarSign className="text-[#FF6E00]" />
                Current Price:{' '}
              </span>
              <span className="ml-6 justify-between text-[10px] font-medium text-[#FF6E00] sm:text-base">
                $0.00
              </span>
            </p>
          </div> */}
        </div>

        {/* Token Purchase Banner */}
        <div className="xs1-gap-2 flex w-full flex-col items-center justify-between bg-gradient-to-l from-[#FE2181] to-[#FE5F5E] px-5 py-2.5 sm:flex-row sm:gap-5 sm:px-7 lg:gap-0 xs1:flex-wrap">
          <p className="flex items-center justify-center gap-2 !text-[10px] font-medium text-white sm:!text-base">
            <Taj /> Buy {token?.name} Coin now to unlock premium content & early
            supporter rewards.
          </p>
          <div className="flex w-full justify-end gap-1 sm:justify-center sm:gap-2.5 lg:w-auto">
            {token?.status == 'launched' ? (
              <Button
                onClick={() => {
                  const uniswapUrl = `https://app.uniswap.org/explore/tokens/ethereum_sepolia/${token?.address}`;
                  window.open(uniswapUrl, '_blank');
                }}
                className="!hover:none group flex !h-[30px] !w-full items-center justify-center !border-none !text-[10px] text-white sm:!h-[46px] sm:!text-sm"
              >
                <span className="text-white">BUY ON</span> <UniSwap />
                <span className="text-white">Uniswap</span>
              </Button>
            ) : token?.status == 'presale' ? (
              <Button
                onClick={() => setOpenModal(true)}
                className="!h-[46px] w-full !text-sm font-semibold uppercase"
              >
                JOIN PRESALE
              </Button>
            ) : null}
            {/* <Button className="flex !h-[30px] !w-[131px] items-center justify-center !border-none bg-white !text-[10px] !text-primary sm:!h-[46px] sm:!w-[240px] sm:!text-sm">
              <span className="group-hover:text-white">BUY ON</span> <Dex />
              <span className="text-black">DexSCREENER</span>
            </Button> */}
          </div>
        </div>
      </div>
      <Modal
        show={openModal}
        hide={setOpenModal}
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
      >
        <BuyModal
          profile={profile}
          album={albumData}
          // hideFirstModal={hide}
          setOpenModal={setOpenModal}
        />
      </Modal>
    </>
  );
};

export default ArtistProfileHeader;
