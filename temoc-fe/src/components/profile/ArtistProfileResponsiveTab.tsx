'use client';
import React, { useState } from 'react';
// import ArtistProfileSummary from './ArtistProfileSummary';
import ArtistProfileSocialLinks from './ArtistProfileSocialLinks';
import ArtistSummary from './ArtistSummary';

interface ArtistProfileResponsiveTabProps {
  profile: any;
  artistContent: any;
  followStats: any;
}

const ArtistProfileResponsiveTab: React.FC<ArtistProfileResponsiveTabProps> = ({
  profile,
  artistContent,
  followStats,
}) => {
  const [activeTab, setActiveTab] = useState('Description');

  return (
    <>
      <div className="mx-auto my-5 flex w-full max-w-[311px] items-center justify-between rounded-[10px] bg-white p-5 text-sm">
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Description' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Description')}
        >
          Description
        </p>
        <div className="h-4 w-0.5 border border-black bg-black"></div>
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Media' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Media')}
        >
          Media
        </p>
        <div className="h-4 w-0.5 border border-black bg-black"></div>
        <p
          className={`cursor-pointer text-sm font-semibold ${
            activeTab === 'Comments' ? 'text-primary' : 'text-black'
          }`}
          onClick={() => setActiveTab('Comments')}
        >
          Comments
        </p>
      </div>

      {/* Description Tab */}
      {activeTab === 'Description' && (
        <div className="">
          <ArtistSummary profile={profile} followStats={followStats} />
        </div>
      )}

      {/* Media Tab */}
      {activeTab === 'Media' && (
        <div className="">
          <ArtistProfileSocialLinks
            profile={profile}
            followStats={followStats}
            artistContent={artistContent}
          />
        </div>
      )}

      {/* Comments Tab */}
      {activeTab === 'Comments' && (
        <div className="">
          <div className="rounded-2xl bg-white p-4 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-6">
            <h3 className="mb-4 text-lg font-semibold">Comments</h3>

            {/* Comments coming soon message */}
            <div className="py-12 text-center">
              <div className="mb-4 text-gray-400">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                Comments coming soon
              </h3>
              <p className="text-gray-600">
                Fan comments and interactions will be displayed here.
              </p>
            </div>

            {/* Mock comment structure for future implementation */}
            <div className="hidden space-y-4">
              <div className="border-b border-gray-200 pb-4">
                <div className="flex items-start space-x-3">
                  <div className="h-8 w-8 flex-shrink-0 rounded-full bg-gray-300"></div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        Fan Name
                      </span>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <p className="mt-1 text-gray-700">
                      Amazing music! Can&apos;t wait for more tracks.
                    </p>
                    <div className="mt-2 flex items-center space-x-4">
                      <button className="text-xs text-gray-500 hover:text-primary">
                        Like
                      </button>
                      <button className="text-xs text-gray-500 hover:text-primary">
                        Reply
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ArtistProfileResponsiveTab;
