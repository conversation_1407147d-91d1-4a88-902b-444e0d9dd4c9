import React, { useState, useRef, useEffect, JSX } from 'react';
import { useQuery } from '@tanstack/react-query';
// import Image from 'next/image';
import ImageComponent from '@/components/common/ImageComponent';
import { Button } from '@/components/common';
import { socialApi } from '@/services/social';
import { contentApi } from '@/services/content';
import { FaPlay } from 'react-icons/fa';
import { IoPause } from 'react-icons/io5';
import { GiPreviousButton, GiNextButton } from 'react-icons/gi';
import { RxLoop } from 'react-icons/rx';
import {
  FaInstagram,
  FaXTwitter,
  FaTiktok,
  FaYoutube,
  FaSpotify,
  FaFacebook,
} from 'react-icons/fa6';
import ArtistAlbum from './ArtistAlbum';
import { libraryService } from '@/services/library.service';
import BuyModal from '@/app/app/trading/Presale/BuyModal';
import Modal from '../common/Modal';
import ArtistProfileSummary from './ArtistProfileSummary';
import ArtistProfileDescription from './ArtistProfileDescription';
import { tokenService } from '@/services/token.service';
import UniSwap from '../common/Icons/UniSwap';
import { TrackInteractionBar, CommentModal } from '@/components/common/TrackInteractions';
import { useAuth } from '@/hooks/useAuth';

interface ArtistProfileSocialLinksProps {
  profile: any;
  followStats?: any;
  artistContent?: any;
}

const ArtistProfileSocialLinks: React.FC<ArtistProfileSocialLinksProps> = ({
  profile,
  followStats,
}) => {
  const { user } = useAuth();
  const socialLinks = profile.artistProfile?.socialLinks || [];

  // Music player state
  const [currentTrackUrl, setCurrentTrackUrl] = useState<string | null>(null);
  const [currentTrackIndex, setCurrentTrackIndex] = useState<number>(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [buyModal, setBuyModal] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLooping, setIsLooping] = useState(false);
  const [currentTab, setCurrentTab] = useState('album');

  const [albumId, setAlbumId] = useState('');

  const [showControls, setShowControls] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Comment modal state
  const [commentModalOpen, setCommentModalOpen] = useState(false);
  const [selectedTrackForComments, setSelectedTrackForComments] = useState<any>(null);

  // Get real followers of this artist
  const { data: realFollowers } = useQuery({
    queryKey: ['artist-followers', profile._id],
    queryFn: () => socialApi.getFollowers(profile._id, 1, 10),
    enabled: !!profile._id,
  });

  // Get tracks for this artist
  // const tracks = artistContent?.tracks || [];
  const { data: tracksData } = useQuery({
    queryFn: () => libraryService.getTracks(albumId),
    queryKey: ['tracks', albumId],
    enabled: !!albumId,
  });

  const tracks = tracksData?.data?.tracks || [];

  // Local state for real-time play count updates
  const [localPlayCounts, setLocalPlayCounts] = useState<{
    [key: string]: number;
  }>({});



  // Audio controls
  const togglePlay = (trackUrl: string, trackIndex?: number) => {
    const index =
      trackIndex !== undefined
        ? trackIndex
        : tracks.findIndex((t: any) => t.fileUrl === trackUrl);

    if (currentTrackUrl === trackUrl) {
      if (isPlaying) {
        audioRef.current?.pause();
        setIsPlaying(false);
      } else {
        audioRef.current?.play();
        setIsPlaying(true);
      }
    } else {
      setCurrentTrackUrl(trackUrl);
      setCurrentTrackIndex(index);
      setIsPlaying(true);
      if (audioRef.current) {
        audioRef.current.src = trackUrl;
        audioRef.current.play();
      }
      // Record play count and update local state
      const track = tracks.find((t: any) => t.fileUrl === trackUrl);
      if (track) {
        // Immediately increment local play count for real-time UI update
        setLocalPlayCounts((prev) => ({
          ...prev,
          [track._id]: (prev[track._id] || track.playCount || 0) + 1,
        }));

        // Record play count in backend
        contentApi.recordTrackPlay(track._id).catch((error) => {
          console.error('Failed to record track play:', error);
          // Revert local count on error
          setLocalPlayCounts((prev) => ({
            ...prev,
            [track._id]: Math.max(
              0,
              (prev[track._id] || track.playCount || 0) - 1,
            ),
          }));
        });
      }
    }
  };

  const handleNext = () => {
    if (!tracks.length || currentTrackIndex === -1) return;
    const nextIndex = (currentTrackIndex + 1) % tracks.length;
    togglePlay(tracks[nextIndex].fileUrl, nextIndex);
  };

  const handlePrev = () => {
    if (!tracks.length || currentTrackIndex === -1) return;
    const prevIndex =
      currentTrackIndex === 0 ? tracks.length - 1 : currentTrackIndex - 1;
    togglePlay(tracks[prevIndex].fileUrl, prevIndex);
  };

  const handleEnded = () => {
    if (isLooping) {
      audioRef.current?.play();
    } else {
      handleNext();
    }
  };

  console.log(profile, 'profile');

  // Update progress
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      setProgress(audio.currentTime);
      setDuration(audio.duration || 0);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', updateProgress);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', updateProgress);
    };
  }, [currentTrackUrl]);

  const formatTime = (time: number) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !duration) return;
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    audioRef.current.currentTime = newTime;
  };

  // Helper function to get current play count (local or original)
  const getCurrentPlayCount = (track: any) => {
    return localPlayCounts[track._id] !== undefined
      ? localPlayCounts[track._id]
      : track.playCount || 0;
  };

  // Icon mapping for social platforms
  const getSocialIcon = (platform: string) => {
    const iconMap: { [key: string]: JSX.Element } = {
      instagram: <FaInstagram className="text-pink-500" />,
      twitter: <FaXTwitter className="text-black" />,
      tiktok: <FaTiktok className="text-black" />,
      youtube: <FaYoutube className="text-red-600" />,
      spotify: <FaSpotify className="text-green-500" />,
      facebook: <FaFacebook className="text-blue-600" />,
    };
    return (
      iconMap[platform.toLowerCase()] || (
        <FaInstagram className="text-gray-500" />
      )
    );
  };

  // Get social links with proper formatting
  const getFormattedSocialLinks = () => {
    if (socialLinks.length > 0) {
      return socialLinks.map((link: any) => ({
        platform: link.platform,
        username: `@${profile.username}`,
        url: link.url,
        icon: getSocialIcon(link.platform),
      }));
    }

    // Default social links
    return [];
    // return [
    //   {   username: `@${profile.username}`,
    //     icon: getSocialIcon('instagram'),
    //   },
    //   {
    //     platform: 'Twitter',
    //     username: `@${profile.username}`,
    //     icon: getSocialIcon('twitter'),
    //   },
    //   {
    //     platform: 'TikTok',
    //     username: `@${profile.username}`,
    //     icon: getSocialIcon('tiktok'),
    //   },
    //   {
    //     platform: 'YouTube',
    //     username: `@${profile.username}`,
    //     icon: getSocialIcon('youtube'),
    //   },
    //   {
    //     platform: 'Spotify',
    //     username: `@${profile.username}`,
    //     icon: getSocialIcon('spotify'),
    //   },
    //   {
    //     platform: 'Facebook',
    //     username: `@${profile.username}`,
    //     icon: getSocialIcon('facebook'),
    //   },
    // ];
  };

  // Get top fans from real followers
  const getTopFans = () => {
    const followers = realFollowers?.followers || [];
    if (followers.length > 0) {
      return followers.slice(0, 4).map((follower: any, index: number) => ({
        id: follower?._id || follower?.id || `follower-${index}`,
        name: follower?.displayName || follower?.username || `Fan ${index + 1}`,
        avatar: follower?.avatarUrl || '/assets/images/artist/artist.png',
      }));
    }

    // Mock data if no real followers
    return [
      {
        id: 'fan-1',
        name: 'Fan 1',
        avatar: '/assets/images/artist/artist.png',
      },
      {
        id: 'fan-2',
        name: 'Fan 2',
        avatar: '/assets/images/artist/artist.png',
      },
      {
        id: 'fan-3',
        name: 'Fan 3',
        avatar: '/assets/images/artist/artist.png',
      },
      {
        id: 'fan-4',
        name: 'Fan 4',
        avatar: '/assets/images/artist/artist.png',
      },
    ];
  };

  const formattedSocialLinks = getFormattedSocialLinks();
  const topFans = getTopFans();
  const totalFollowers = followStats?.followersCount || 0;
  const remainingFollowers = Math.max(0, totalFollowers - 4);
  const { data } = useQuery({
    queryKey: ['artist_token'],
    queryFn: () => tokenService.getArtistToken(profile?._id),
  });

  // console.log(currentTab, 'tracks');
  const token = data?.data?.[0];
  // console.log(currentTab, 'tracks');
  return (
    <div className="flex flex-col justify-between gap-5 sm:gap-[30px] xl:flex-row">
      {/* Left Side - Artist Content */}
      <div className="w-full xl:w-[70%]">
        {/* artist description */}
        {profile.description && (
          <ArtistProfileDescription
            profile={profile}
            followStats={followStats}
          />
        )}

        <div className="rounded-2xl bg-white p-4 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-6">
          <h3 className="mb-4 text-lg font-semibold">Albums</h3>
          {/* <ArtistAlbum library={followStats} /> */}
          {currentTab == 'album' && (
            <ArtistAlbum
              profile={profile}
              setAlbumId={setAlbumId}
              setCurrentTab={setCurrentTab}
            />
          )}
          {/* Artist Tracks */}
          {currentTab == 'track' && (
            <div className="space-y-4">
              {tracks.length > 0 ? (
                tracks.map((track: any, index: number) => (
                  <div
                    key={track._id}
                    className={`group flex cursor-pointer items-center justify-between gap-4 rounded-lg border p-3 transition-all hover:shadow-md ${
                      currentTrackUrl === track.fileUrl
                        ? 'border-primary bg-orange-50'
                        : 'border-gray-100 hover:bg-gray-50'
                    }`}
                    onClick={() => {
                      togglePlay(track.fileUrl, index);
                      setShowControls(true);
                    }}
                  >
                    <div className="flex w-[80%] items-center gap-2 truncate sm:w-[90%] xl:w-[95%]">
                      <div className="relative h-10 w-10 flex-shrink-0 overflow-hidden rounded-lg bg-gray-200 sm:h-12 sm:w-12">
                        {track.thumbnailUrl ? (
                          <ImageComponent
                            src={track.thumbnailUrl}
                            alt={track.title}
                            // width={48}
                            // height={48}
                            fill
                            figClassName="h-full w-full"
                            className="h-full w-full rounded-md object-cover"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-primary to-yellow-400">
                            <svg
                              className="h-5 w-5 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                            </svg>
                          </div>
                        )}
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity group-hover:opacity-100">
                          {isPlaying && currentTrackUrl === track.fileUrl ? (
                            <IoPause className="text-lg text-white" />
                          ) : (
                            <FaPlay className="text-sm text-white" />
                          )}
                        </div>
                      </div>
                      <div className="w-full truncate">
                        <h4
                          className={`w-full truncate font-medium ${
                            currentTrackUrl === track.fileUrl
                              ? 'text-orange-900'
                              : 'text-gray-900'
                          }`}
                        >
                          {track.title}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {track.album?.title ||
                            profile.artistProfile?.genre ||
                            'Music'}{' '}
                          • {getCurrentPlayCount(track)} plays
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {/* Track Interactions */}
                      <TrackInteractionBar
                        trackId={track._id}
                        onCommentClick={() => {
                          setSelectedTrackForComments(track);
                          setCommentModalOpen(true);
                        }}
                        size="sm"
                      />

                      <span className="text-sm text-gray-500">
                        {formatTime(track.duration || 180)}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          togglePlay(track.fileUrl, index);
                          setShowControls(true);
                        }}
                        className={`p-2 transition-colors ${
                          currentTrackUrl === track.fileUrl
                            ? 'text-primary hover:text-orange-700'
                            : 'text-gray-400 hover:text-primary'
                        }`}
                      >
                        {isPlaying && currentTrackUrl === track.fileUrl ? (
                          <IoPause className="text-lg" />
                        ) : (
                          <FaPlay className="text-sm" />
                        )}
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                // No tracks available - show helpful message
                <div className="py-12 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                    <svg
                      className="h-8 w-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                      />
                    </svg>
                  </div>
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    No Tracks Yet
                  </h3>
                  <p className="mb-4 text-gray-600">
                    {profile.displayName} hasn&apos;t uploaded any tracks yet.
                  </p>
                  <p className="text-sm text-gray-500">
                    Check back later for new music releases!
                  </p>
                </div>
              )}
              <Button
                className="!h-12 !w-[120px]"
                onClick={() => setCurrentTab('album')}
              >
                Back
              </Button>
            </div>
          )}
          {/* // platform: 'Instagram', // */}
          {/* Token Gated Notice */}
          {/* <div className="mt-6 flex flex-col items-end justify-between gap-3 rounded-lg border border-orange-200 bg-orange-50 p-4 sm:flex-row sm:items-center">
            <div className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">
                  Premium Content Available
                </h4>
                <p className="text-sm text-gray-600">
                  Hold 15 {profile.displayName} Coins to unlock exclusive tracks
                  and content
                </p>
              </div>
            </div>
            <Button className="!h-[36px] !px-4 !text-sm font-semibold">
              GET COINS
            </Button>
          </div> */}
        </div>
      </div>

      {/* Right Side - Social Links + Top Fans */}
      <div className="w-full space-y-6 xl:w-[30%]">
        {/* atist summary */}
        <ArtistProfileSummary profile={profile} followStats={followStats} />
        {/* Social Links */}
        {formattedSocialLinks.length > 0 && (
          <div className="rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
            <h3 className="mb-4 text-sm font-normal sm:text-lg">
              Social Links
            </h3>
            <div className="space-y-3">
              {formattedSocialLinks.map((social: any, index: number) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="text-lg">{social.icon}</div>
                  {social.url ? (
                    <a
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary transition-colors hover:text-orange-600"
                    >
                      {social.username}
                    </a>
                  ) : (
                    <span className="text-sm text-primary">
                      {social.username}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Top Fans */}

        <div className="rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)]">
          <h3 className="mb-4 text-sm font-normal sm:text-lg">Top Fans</h3>

          <div className="mb-6 space-y-3">
            {totalFollowers > 0 ? (
              <>
                {topFans.map((fan: any) => (
                  <div key={fan.id} className="flex items-center gap-3">
                    <ImageComponent
                      src={fan.avatar}
                      alt={fan.name}
                      figClassName="w-8 h-8 rounded-full"
                      fill
                    />
                    <span className="text-sm font-medium text-gray-900">
                      {fan.name}
                    </span>
                  </div>
                ))}
                {remainingFollowers > 0 && (
                  <div className="flex items-center gap-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
                      <span className="text-xs font-medium text-gray-600">
                        +{remainingFollowers}
                      </span>
                    </div>
                    <span className="text-sm text-gray-600">and more...</span>
                  </div>
                )}
              </>
            ) : (
              <div className="py-6 text-center">
                <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                    />
                  </svg>
                </div>
                <h4 className="mb-1 text-sm font-medium text-gray-900">
                  No Fans Yet
                </h4>
                <p className="text-xs text-gray-600">
                  Be the first to follow {profile.displayName}!
                </p>
              </div>
            )}
          </div>
          {token?.status == 'launched' ? (
            <Button
              onClick={() => {
                const uniswapUrl = `https://app.uniswap.org/explore/tokens/ethereum_sepolia/${token?.address}`;
                window.open(uniswapUrl, '_blank');
              }}
              className="!hover:none group flex !h-[30px] !w-full items-center justify-center !border-none !text-[10px] text-white sm:!h-[46px] sm:!text-sm"
            >
              <span className="text-white">BUY ON</span> <UniSwap />
              <span className="text-white">Uniswap</span>
            </Button>
          ) : token?.status == 'presale' ? (
            <Button
              onClick={() => setBuyModal(true)}
              className="!h-[46px] w-full !text-sm font-semibold uppercase"
            >
              JOIN PRESALE
            </Button>
          ) : null}
        </div>
      </div>

      {/* Audio Element */}
      <audio
        ref={audioRef}
        onEnded={handleEnded}
        onLoadedMetadata={() => {
          if (audioRef.current) {
            setDuration(audioRef.current.duration);
          }
        }}
        onTimeUpdate={() => {
          if (audioRef.current) {
            setProgress(audioRef.current.currentTime);
          }
        }}
      />

      {/* Music Player Controls */}
      {showControls && currentTrackUrl && (
        <div className="fixed bottom-[86px] left-0 right-0 z-50 border-t border-gray-200 bg-white p-4 shadow-lg sm:bottom-0">
          <div className="mx-auto flex max-w-6xl items-center gap-4">
            {/* Current Track Info */}
            <div className="flex min-w-0 flex-1 items-center gap-3">
              <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-gradient-to-br from-primary to-yellow-400">
                <svg
                  className="h-5 w-5 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="truncate font-medium text-gray-900">
                  {tracks[currentTrackIndex]?.title || 'Unknown Track'}
                </h4>
                <p className="truncate text-sm text-gray-600">
                  {profile.displayName}
                </p>
              </div>
            </div>

            {/* Player Controls */}
            <div className="flex items-center gap-4">
              <button
                onClick={handlePrev}
                className="p-2 text-gray-600 transition-colors hover:text-primary"
                disabled={tracks.length <= 1}
              >
                <GiPreviousButton className="text-lg" />
              </button>

              <button
                onClick={() => togglePlay(currentTrackUrl)}
                className="rounded-full bg-primary p-3 text-white transition-colors hover:bg-orange-600"
              >
                {isPlaying ? (
                  <IoPause className="text-lg" />
                ) : (
                  <FaPlay className="ml-0.5 text-sm" />
                )}
              </button>

              <button
                onClick={handleNext}
                className="p-2 text-gray-600 transition-colors hover:text-primary"
                disabled={tracks.length <= 1}
              >
                <GiNextButton className="text-lg" />
              </button>

              <button
                onClick={() => setIsLooping(!isLooping)}
                className={`p-2 transition-colors ${
                  isLooping
                    ? 'text-primary'
                    : 'text-gray-600 hover:text-primary'
                }`}
              >
                <RxLoop className="text-lg" />
              </button>
            </div>

            {/* Progress Bar */}
            <div className="flex min-w-0 flex-1 items-center gap-2">
              <span className="w-10 text-right text-xs text-gray-500">
                {formatTime(progress)}
              </span>
              <div
                className="h-1 flex-1 cursor-pointer rounded-full bg-gray-200"
                onClick={handleProgressClick}
              >
                <div
                  className="h-full rounded-full bg-primary transition-all"
                  style={{
                    width: `${duration ? (progress / duration) * 100 : 0}%`,
                  }}
                />
              </div>
              <span className="w-10 text-xs text-gray-500">
                {formatTime(duration)}
              </span>
            </div>

            {/* Close Button */}
            <button
              onClick={() => {
                setShowControls(false);
                audioRef.current?.pause();
                setIsPlaying(false);
              }}
              className="p-2 text-gray-600 transition-colors hover:text-red-500"
            >
              <svg
                className="h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      )}

      <Modal
        className="hideScrollbar !h-max !max-w-[580px] !px-5 !pb-6 md:pb-0"
        show={buyModal}
        hide={setBuyModal}
      >
        <BuyModal profile={profile} />
      </Modal>

      {/* Comment Modal */}
      {selectedTrackForComments && (
        <CommentModal
          isOpen={commentModalOpen}
          onClose={() => {
            setCommentModalOpen(false);
            setSelectedTrackForComments(null);
          }}
          trackId={selectedTrackForComments._id}
          trackTitle={selectedTrackForComments.title}
        />
      )}
    </div>
  );
};

export default ArtistProfileSocialLinks;
