// import { tokenService } from '@/services/token.service';
// import { useQuery } from '@tanstack/react-query';
import React from 'react';

interface ArtistProfileSummaryProps {
  profile: any;
  followStats: any;
}

const ArtistProfileSummary: React.FC<ArtistProfileSummaryProps> = ({
  profile,
  followStats,
}) => {
  // const { data } = useQuery({
  //   queryKey: ['artist_token'],
  //   queryFn: () => tokenService.getArtistToken(profile?._id),
  // });

  // const token = data?.data?.[0];

  // Get artist stats for summary
  const getArtistStats = () => {
    const joinDate = new Date(profile.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return {
      joinDate,
      followers: followStats?.followersCount || 0,
      genre: profile.artistProfile?.genre || 'Music',
      verified: profile.artistProfile?.isVerified ? 'Verified' : 'Pending',
    };
  };

  const stats = getArtistStats();

  return (
    <div className="w-full rounded-2xl bg-white p-5 shadow-[0_5px_10px_rgba(0,0,0,0.1)] sm:p-6 sm:pb-4">
      <h3 className="text-sm font-normal sm:text-lg">Summary</h3>

      <div className="mt-2.5 flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5">
        <p className="text-xs font-normal text-[#666666] sm:text-base">
          Join date
        </p>
        <p className="text-xs font-normal text-primary sm:text-base">
          {stats.joinDate}
        </p>
      </div>

      <div className="flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5">
        <p className="text-xs font-normal text-[#666666] sm:text-base">Genre</p>
        <p className="text-xs font-normal text-primary sm:text-base">
          {stats.genre}
        </p>
      </div>

      <div className="flex justify-between border border-b border-l-0 border-r-0 border-t-0 py-2.5">
        <p className="text-xs font-normal text-[#666666] sm:text-base">
          Followers
        </p>
        <p className="text-xs font-normal text-primary sm:text-base">
          {stats.followers}
        </p>
      </div>

      <div className="flex justify-between py-2.5">
        <p className="text-xs font-normal text-[#666666] sm:text-base">
          Status
        </p>
        <p className="text-xs font-normal text-primary sm:text-base">
          {stats.verified}
        </p>
      </div>
    </div>
  );
};

export default ArtistProfileSummary;
