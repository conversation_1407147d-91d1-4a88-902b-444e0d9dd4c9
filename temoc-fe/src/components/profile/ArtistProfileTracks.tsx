import React, { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import { FaPlay } from 'react-icons/fa';
import { IoPause } from 'react-icons/io5';
// import { GiPreviousButton, GiNextButton } from 'react-icons/gi';
// import { RxLoop } from 'react-icons/rx';
import { contentApi } from '@/services/content';

interface ArtistProfileTracksProps {
  artistContent: any;
  profile: any;
}

const ArtistProfileTracks: React.FC<ArtistProfileTracksProps> = ({
  artistContent,
  profile,
}) => {
  const [currentTrackUrl, setCurrentTrackUrl] = useState<string | null>(null);
  const [currentTrackIndex, setCurrentTrackIndex] = useState<number>(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [, setProgress] = useState(0);
  const [, setDuration] = useState(0);
  const [isLooping] = useState(false);
  const [, setShowControls] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const tracks = artistContent?.tracks || [];

  // Audio controls
  const togglePlay = (trackUrl: string, trackIndex?: number) => {
    const index =
      trackIndex !== undefined
        ? trackIndex
        : tracks.findIndex((t: any) => t.fileUrl === trackUrl);

    if (currentTrackUrl === trackUrl) {
      if (isPlaying) {
        audioRef.current?.pause();
        setIsPlaying(false);
      } else {
        audioRef.current?.play();
        setIsPlaying(true);
      }
    } else {
      setCurrentTrackUrl(trackUrl);
      setCurrentTrackIndex(index);
      setIsPlaying(true);
      if (audioRef.current) {
        audioRef.current.src = trackUrl;
        audioRef.current.play();
      }
      // Record play count
      const track = tracks.find((t: any) => t.fileUrl === trackUrl);
      if (track) {
        contentApi.recordTrackPlay(track._id);
      }
    }
  };

  const handleNext = () => {
    if (!tracks.length || currentTrackIndex === -1) return;
    const nextIndex = (currentTrackIndex + 1) % tracks.length;
    togglePlay(tracks[nextIndex].fileUrl, nextIndex);
  };
  // const handlePrev = () => {
  //   if (!tracks.length || currentTrackIndex === -1) return;
  //   const prevIndex = currentTrackIndex === 0 ? tracks.length - 1 : currentTrackIndex - 1;
  //   togglePlay(tracks[prevIndex].fileUrl, prevIndex);
  // };

  const handleEnded = () => {
    if (isLooping) {
      audioRef.current?.play();
    } else {
      handleNext();
    }
  };

  // Update progress
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      setProgress(audio.currentTime);
      setDuration(audio.duration || 0);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', updateProgress);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', updateProgress);
    };
  }, [currentTrackUrl]);
  // const formatTime = (time: number) => {
  //   if (isNaN(time)) return '0:00';
  //   const minutes = Math.floor(time / 60);
  //   const seconds = Math.floor(time % 60);
  //   return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  // };

  // const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
  //   if (!audioRef.current || !duration) return;
  //   const rect = e.currentTarget.getBoundingClientRect();
  //   const clickX = e.clientX - rect.left;
  //   const newTime = (clickX / rect.width) * duration;
  //   audioRef.current.currentTime = newTime;
  // };

  // Create mock tracks if no real tracks available
  const getDisplayTracks = () => {
    if (tracks.length > 0) {
      return tracks;
    }

    // Mock tracks based on artist name and genre
    const artistName = profile.displayName;
    const genre = profile.artistProfile?.genre || 'Music';

    return [
      {
        _id: 'mock-1',
        title: `${artistName} - Latest Hit`,
        description: `New ${genre} track`,
        thumbnailUrl: profile.avatarUrl,
        fileUrl: '/assets/audio/sample-track.mp3', // You'd need to add a sample audio file
        playCount: Math.floor(Math.random() * 10000),
        album: { title: `${artistName} Collection` },
      },
      {
        _id: 'mock-2',
        title: `${artistName} - Fan Favorite`,
        description: `Popular ${genre} song`,
        thumbnailUrl: profile.avatarUrl,
        fileUrl: '/assets/audio/sample-track.mp3',
        playCount: Math.floor(Math.random() * 5000),
        album: { title: `${artistName} Collection` },
      },
      {
        _id: 'mock-3',
        title: `${artistName} - Exclusive`,
        description: `Exclusive ${genre} release`,
        thumbnailUrl: profile.avatarUrl,
        fileUrl: '/assets/audio/sample-track.mp3',
        playCount: Math.floor(Math.random() * 8000),
        album: { title: `${artistName} Collection` },
      },
    ];
  };

  const displayTracks = getDisplayTracks();

  return (
    <>
      <div className="mt-12">
        <h3 className="text-xl font-semibold uppercase">POPULAR TRACKS</h3>
        <div className="mt-4 space-y-4">
          {displayTracks.map((track: any, index: any) => (
            <div
              key={track._id}
              className={`rounded-lg border bg-white p-4 transition-shadow hover:shadow-md ${
                currentTrackUrl === track.fileUrl
                  ? 'border-primary bg-orange-50'
                  : 'border-gray-200'
              }`}
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <span
                    className={`font-medium ${
                      currentTrackUrl === track.fileUrl
                        ? 'text-primary'
                        : 'text-gray-500'
                    }`}
                  >
                    {index + 1}
                  </span>
                </div>

                <div
                  className="group relative h-12 w-12 cursor-pointer overflow-hidden rounded-lg bg-gray-200"
                  onClick={() => {
                    togglePlay(track.fileUrl, index);
                    setShowControls(true);
                  }}
                >
                  {track.thumbnailUrl ? (
                    <Image
                      src={track.thumbnailUrl}
                      alt={track.title}
                      width={48}
                      height={48}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center">
                      <svg
                        className="h-6 w-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                        />
                      </svg>
                    </div>
                  )}
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity group-hover:opacity-100">
                    {isPlaying && currentTrackUrl === track.fileUrl ? (
                      <IoPause className="text-lg text-white" />
                    ) : (
                      <FaPlay className="text-sm text-white" />
                    )}
                  </div>
                </div>

                <div className="min-w-0 flex-1">
                  <h4
                    className={`truncate font-medium ${
                      currentTrackUrl === track.fileUrl
                        ? 'text-orange-900'
                        : 'text-gray-900'
                    }`}
                  >
                    {track.title}
                  </h4>
                  <p className="truncate text-sm text-gray-500">
                    {track.album?.title}
                  </p>
                  {track.description && (
                    <p className="truncate text-sm text-gray-600">
                      {track.description}
                    </p>
                  )}
                  <p className="text-xs text-gray-500">
                    {track.playCount} plays
                  </p>
                </div>

                <div className="flex-shrink-0">
                  <button
                    onClick={() => {
                      togglePlay(track.fileUrl, index);
                      setShowControls(true);
                    }}
                    className={`p-2 transition-colors ${
                      currentTrackUrl === track.fileUrl
                        ? 'text-primary hover:text-orange-700'
                        : 'text-gray-400 hover:text-primary'
                    }`}
                  >
                    {isPlaying && currentTrackUrl === track.fileUrl ? (
                      <IoPause className="text-lg" />
                    ) : (
                      <FaPlay className="text-sm" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        onEnded={handleEnded}
        loop={isLooping}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
    </>
  );
};

export default ArtistProfileTracks;
