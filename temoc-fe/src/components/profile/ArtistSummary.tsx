import React from 'react';
import ArtistProfileDescription from './ArtistProfileDescription';
import ArtistProfileSummary from './ArtistProfileSummary';

interface ArtistProfileSummaryProps {
  profile: any;
  followStats: any;
}

const ArtistSummary: React.FC<ArtistProfileSummaryProps> = ({
  profile,
  followStats,
}) => {
  return (
    <>
      {profile.description && (
        <ArtistProfileDescription profile={profile} followStats={followStats} />
      )}

      <ArtistProfileSummary profile={profile} followStats={followStats} />
    </>
  );
};

export default ArtistSummary;
