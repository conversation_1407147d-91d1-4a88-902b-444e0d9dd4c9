'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { FaPlay } from 'react-icons/fa';
import { IoPause } from 'react-icons/io5';
import { GiPreviousButton, GiNextButton } from 'react-icons/gi';
import { RxLoop } from 'react-icons/rx';
import { contentApi, Library, Album } from '@/services/content';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ContentTabProps {
  userId: string;
}

export function ContentTab({ userId }: ContentTabProps) {
  const [selectedLibrary, setSelectedLibrary] = useState<Library | null>(null);
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [currentTrackUrl, setCurrentTrackUrl] = useState<string | null>(null);
  const [currentTrackIndex, setCurrentTrackIndex] = useState<number>(-1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLooping, setIsLooping] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Get all content for this user simultaneously
  const { data: librariesData, isLoading: librariesLoading } = useQuery({
    queryKey: ['content', 'libraries', userId],
    queryFn: () => contentApi.getLibrariesByUser(userId),
    enabled: !!userId,
  });

  const { data: albumsData, isLoading: albumsLoading } = useQuery({
    queryKey: ['content', 'albums-overview', userId],
    queryFn: () => contentApi.getAlbumsByUser(userId),
    enabled: !!userId,
  });

  const { data: tracksData, isLoading: tracksLoading } = useQuery({
    queryKey: ['content', 'tracks-overview', userId],
    queryFn: () => contentApi.getTracksByUser(userId),
    enabled: !!userId,
  });

  // Get albums for selected library (drill-down)
  const { data: libraryAlbumsData, isLoading: libraryAlbumsLoading } = useQuery(
    {
      queryKey: ['content', 'albums', selectedLibrary?._id],
      queryFn: () => contentApi.getPublicAlbums(selectedLibrary!._id),
      enabled: !!selectedLibrary,
    },
  );

  // Get tracks for selected album (drill-down)
  const { data: albumTracksData, isLoading: albumTracksLoading } = useQuery({
    queryKey: ['content', 'tracks', selectedAlbum?._id],
    queryFn: () => contentApi.getPublicTracks(selectedAlbum!._id),
    enabled: !!selectedAlbum,
  });

  const libraries = librariesData?.libraries || [];
  const albums = albumsData?.albums || [];
  const tracks = tracksData?.tracks || [];
  const libraryAlbums = libraryAlbumsData?.albums || [];
  const albumTracks = albumTracksData?.tracks || [];

  const isLoading = librariesLoading || albumsLoading || tracksLoading;

  // Get current track list (either album tracks or overview tracks)
  const getCurrentTrackList = () => {
    if (selectedAlbum && albumTracks.length > 0) {
      return albumTracks;
    }
    return tracks;
  };

  // Audio controls
  const togglePlay = (trackUrl: string, trackIndex?: number) => {
    const currentTrackList = getCurrentTrackList();
    const index =
      trackIndex !== undefined
        ? trackIndex
        : currentTrackList.findIndex((t: any) => t.fileUrl === trackUrl);

    if (currentTrackUrl === trackUrl) {
      if (isPlaying) {
        audioRef.current?.pause();
        setIsPlaying(false);
      } else {
        audioRef.current?.play();
        setIsPlaying(true);
      }
    } else {
      setCurrentTrackUrl(trackUrl);
      setCurrentTrackIndex(index);
      setIsPlaying(true);
      if (audioRef.current) {
        audioRef.current.src = trackUrl;
        audioRef.current.play();
      }
      // Record play count
      const track = currentTrackList.find((t: any) => t.fileUrl === trackUrl);
      if (track) {
        contentApi.recordTrackPlay(track._id);
      }
    }
  };

  const handleNext = () => {
    const currentTrackList = getCurrentTrackList();
    if (!currentTrackList.length || currentTrackIndex === -1) return;
    const nextIndex = (currentTrackIndex + 1) % currentTrackList.length;
    togglePlay(currentTrackList[nextIndex].fileUrl, nextIndex);
  };

  const handlePrev = () => {
    const currentTrackList = getCurrentTrackList();
    if (!currentTrackList.length || currentTrackIndex === -1) return;
    const prevIndex =
      currentTrackIndex === 0
        ? currentTrackList.length - 1
        : currentTrackIndex - 1;
    togglePlay(currentTrackList[prevIndex].fileUrl, prevIndex);
  };

  const handleEnded = () => {
    if (isLooping) {
      audioRef.current?.play();
    } else {
      handleNext();
    }
  };

  // Update progress
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      setProgress(audio.currentTime);
      setDuration(audio.duration || 0);
    };

    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', updateProgress);

    return () => {
      audio.removeEventListener('timeupdate', updateProgress);
      audio.removeEventListener('loadedmetadata', updateProgress);
    };
  }, [currentTrackUrl]);

  const formatTime = (time: number) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !duration) return;
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    audioRef.current.currentTime = newTime;
  };

  if (librariesLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (libraries.length === 0 && albums.length === 0 && tracks.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="mb-4 text-gray-400">
          <svg
            className="mx-auto h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
            />
          </svg>
        </div>
        <h3 className="mb-2 text-lg font-medium text-gray-900">
          No content available
        </h3>
        <p className="text-gray-600">
          This user hasn&apos;t published any music content yet.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Navigation breadcrumb */}
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <button
          onClick={() => {
            setSelectedLibrary(null);
            setSelectedAlbum(null);
          }}
          className={`hover:text-primary ${!selectedLibrary ? 'font-medium text-primary' : ''}`}
        >
          Content Overview
        </button>
        {selectedLibrary && (
          <>
            <span>/</span>
            <button
              onClick={() => setSelectedAlbum(null)}
              className={`hover:text-primary ${!selectedAlbum ? 'font-medium text-primary' : ''}`}
            >
              {selectedLibrary.title}
            </button>
          </>
        )}
        {selectedAlbum && (
          <>
            <span>/</span>
            <span className="font-medium text-primary">
              {selectedAlbum.title}
            </span>
          </>
        )}
      </div>

      {/* Libraries view */}
      {!selectedLibrary && libraries.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Libraries</h3>
            <span className="text-sm text-gray-500">
              {libraries.length} libraries
            </span>
          </div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {libraries.map((library) => (
              <div
                key={library._id}
                onClick={() => setSelectedLibrary(library)}
                className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-md"
              >
                <div className="aspect-square mb-3 overflow-hidden rounded-lg bg-gray-200">
                  {library.thumbnailUrl ? (
                    <Image
                      src={library.thumbnailUrl}
                      alt={library.title}
                      width={200}
                      height={200}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center">
                      <svg
                        className="h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <h4 className="mb-1 font-semibold text-gray-900">
                  {library.title}
                </h4>
                {library.description && (
                  <p className="line-clamp-2 text-sm text-gray-600">
                    {library.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Albums view for selected library */}
      {selectedLibrary && !selectedAlbum && (
        <div>
          {libraryAlbumsLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : libraryAlbums.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-gray-600">No albums in this library yet.</p>
            </div>
          ) : (
            <>
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Albums in {selectedLibrary.title}
                </h3>
                <span className="text-sm text-gray-500">
                  {libraryAlbums.length} albums
                </span>
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {libraryAlbums.map((album) => (
                  <div
                    key={album._id}
                    onClick={() => setSelectedAlbum(album)}
                    className="cursor-pointer rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-md"
                  >
                    <div className="aspect-square mb-3 overflow-hidden rounded-lg bg-gray-200">
                      {album.thumbnailUrl ? (
                        <Image
                          src={album.thumbnailUrl}
                          alt={album.title}
                          width={200}
                          height={200}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center">
                          <svg
                            className="h-12 w-12 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                    <h4 className="mb-1 font-semibold text-gray-900">
                      {album.title}
                    </h4>
                    {album.description && (
                      <p className="line-clamp-2 text-sm text-gray-600">
                        {album.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      )}

      {/* Tracks view for selected album */}
      {selectedAlbum && (
        <div>
          {albumTracksLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : albumTracks.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-gray-600">No tracks in this album yet.</p>
            </div>
          ) : (
            <>
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Tracks in {selectedAlbum.title}
                </h3>
                <span className="text-sm text-gray-500">
                  {albumTracks.length} tracks
                </span>
              </div>
              <div className="space-y-4">
                {albumTracks.map((track, index) => (
                  <div
                    key={track._id}
                    className={`rounded-lg border bg-white p-4 transition-shadow hover:shadow-md ${
                      currentTrackUrl === track.fileUrl
                        ? 'border-primary bg-orange-50'
                        : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <span
                          className={`font-medium ${
                            currentTrackUrl === track.fileUrl
                              ? 'text-primary'
                              : 'text-gray-500'
                          }`}
                        >
                          {index + 1}
                        </span>
                      </div>

                      <div
                        className="group relative h-12 w-12 cursor-pointer overflow-hidden rounded-lg bg-gray-200"
                        onClick={() => {
                          togglePlay(track.fileUrl, index);
                          setShowControls(true);
                        }}
                      >
                        {track.thumbnailUrl ? (
                          <Image
                            src={track.thumbnailUrl}
                            alt={track.title}
                            width={48}
                            height={48}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center">
                            <svg
                              className="h-6 w-6 text-gray-400"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                              />
                            </svg>
                          </div>
                        )}
                        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity group-hover:opacity-100">
                          {isPlaying && currentTrackUrl === track.fileUrl ? (
                            <IoPause className="text-lg text-white" />
                          ) : (
                            <FaPlay className="text-sm text-white" />
                          )}
                        </div>
                      </div>

                      <div className="min-w-0 flex-1">
                        <h4
                          className={`truncate font-medium ${
                            currentTrackUrl === track.fileUrl
                              ? 'text-orange-900'
                              : 'text-gray-900'
                          }`}
                        >
                          {track.title}
                        </h4>
                        {track.description && (
                          <p className="truncate text-sm text-gray-600">
                            {track.description}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          {track.playCount} plays
                        </p>
                      </div>

                      <div className="flex-shrink-0">
                        <button
                          onClick={() => {
                            togglePlay(track.fileUrl, index);
                            setShowControls(true);
                          }}
                          className={`p-2 transition-colors ${
                            currentTrackUrl === track.fileUrl
                              ? 'text-primary hover:text-orange-700'
                              : 'text-gray-400 hover:text-primary'
                          }`}
                        >
                          {isPlaying && currentTrackUrl === track.fileUrl ? (
                            <IoPause className="text-lg" />
                          ) : (
                            <FaPlay className="text-sm" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      )}

      {/* Albums Section - Top albums by track count (only show in overview) */}
      {!selectedLibrary && albums.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Popular Albums
            </h3>
            <span className="text-sm text-gray-500">
              {albums.length} albums
            </span>
          </div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {albums.map((album: any) => (
              <div
                key={album._id}
                className="rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:shadow-md"
              >
                <div className="aspect-square mb-3 overflow-hidden rounded-lg bg-gray-200">
                  {album.thumbnailUrl ? (
                    <Image
                      src={album.thumbnailUrl}
                      alt={album.title}
                      width={200}
                      height={200}
                      className="h-full w-full object-cover"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center">
                      <svg
                        className="h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                        />
                      </svg>
                    </div>
                  )}
                </div>
                <h4 className="mb-1 font-semibold text-gray-900">
                  {album.title}
                </h4>
                <p className="mb-2 text-sm text-gray-500">
                  {album.trackCount} tracks
                </p>
                {album.description && (
                  <p className="line-clamp-2 text-sm text-gray-600">
                    {album.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tracks Section - Recent tracks (only show in overview) */}
      {!selectedLibrary && tracks.length > 0 && (
        <div>
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Recent Tracks
            </h3>
            <span className="text-sm text-gray-500">
              {tracks.length} tracks
            </span>
          </div>
          <div className="space-y-4">
            {tracks.map((track: any, index) => (
              <div
                key={track._id}
                className={`rounded-lg border bg-white p-4 transition-shadow hover:shadow-md ${
                  currentTrackUrl === track.fileUrl
                    ? 'border-primary bg-orange-50'
                    : 'border-gray-200'
                }`}
              >
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <span
                      className={`font-medium ${
                        currentTrackUrl === track.fileUrl
                          ? 'text-primary'
                          : 'text-gray-500'
                      }`}
                    >
                      {index + 1}
                    </span>
                  </div>

                  <div
                    className="group relative h-12 w-12 cursor-pointer overflow-hidden rounded-lg bg-gray-200"
                    onClick={() => {
                      togglePlay(track.fileUrl, index);
                      setShowControls(true);
                    }}
                  >
                    {track.thumbnailUrl ? (
                      <Image
                        src={track.thumbnailUrl}
                        alt={track.title}
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center">
                        <svg
                          className="h-6 w-6 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                          />
                        </svg>
                      </div>
                    )}
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 transition-opacity group-hover:opacity-100">
                      {isPlaying && currentTrackUrl === track.fileUrl ? (
                        <IoPause className="text-lg text-white" />
                      ) : (
                        <FaPlay className="text-sm text-white" />
                      )}
                    </div>
                  </div>

                  <div className="min-w-0 flex-1">
                    <h4
                      className={`truncate font-medium ${
                        currentTrackUrl === track.fileUrl
                          ? 'text-orange-900'
                          : 'text-gray-900'
                      }`}
                    >
                      {track.title}
                    </h4>
                    <p className="truncate text-sm text-gray-500">
                      {typeof track.album === 'object'
                        ? track.album.title
                        : 'Unknown Album'}
                    </p>
                    {track.description && (
                      <p className="truncate text-sm text-gray-600">
                        {track.description}
                      </p>
                    )}
                    <p className="text-xs text-gray-500">
                      {track.playCount} plays
                    </p>
                  </div>

                  <div className="flex-shrink-0">
                    <button
                      onClick={() => {
                        togglePlay(track.fileUrl, index);
                        setShowControls(true);
                      }}
                      className={`p-2 transition-colors ${
                        currentTrackUrl === track.fileUrl
                          ? 'text-primary hover:text-orange-700'
                          : 'text-gray-400 hover:text-primary'
                      }`}
                    >
                      {isPlaying && currentTrackUrl === track.fileUrl ? (
                        <IoPause className="text-lg" />
                      ) : (
                        <FaPlay className="text-sm" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Music Player Controls */}
      {showControls && currentTrackUrl && (
        <div className="fixed bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4 shadow-lg">
          <div className="mx-auto max-w-6xl">
            <div className="flex items-center justify-between">
              {/* Current Track Info */}
              <div className="flex min-w-0 flex-1 items-center space-x-3">
                <div className="h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg bg-gray-200">
                  {(() => {
                    const currentTrackList = getCurrentTrackList();
                    const currentTrack = currentTrackList.find(
                      (t: any) => t.fileUrl === currentTrackUrl,
                    );
                    return currentTrack?.thumbnailUrl ? (
                      <Image
                        src={currentTrack.thumbnailUrl}
                        alt="Track"
                        width={48}
                        height={48}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center">
                        <svg
                          className="h-6 w-6 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
                          />
                        </svg>
                      </div>
                    );
                  })()}
                </div>
                <div className="min-w-0">
                  <p className="truncate font-medium text-gray-900">
                    {(() => {
                      const currentTrackList = getCurrentTrackList();
                      const currentTrack = currentTrackList.find(
                        (t: any) => t.fileUrl === currentTrackUrl,
                      );
                      return currentTrack?.title || 'Playing...';
                    })()}
                  </p>
                  <p className="truncate text-sm text-gray-600">
                    {(() => {
                      const currentTrackList = getCurrentTrackList();
                      const currentTrack = currentTrackList.find(
                        (t: any) => t.fileUrl === currentTrackUrl,
                      );

                      return (
                        (typeof currentTrack?.album === 'object'
                          ? currentTrack.album.title
                          : null) ||
                        selectedAlbum?.title ||
                        'Unknown Album'
                      );
                    })()}
                  </p>
                </div>
              </div>

              {/* Player Controls */}
              <div className="flex max-w-md flex-1 flex-col items-center space-y-2">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handlePrev}
                    className="text-gray-600 transition-colors hover:text-primary"
                  >
                    <GiPreviousButton className="text-lg" />
                  </button>

                  <button
                    onClick={() => {
                      if (isPlaying) {
                        audioRef.current?.pause();
                        setIsPlaying(false);
                      } else {
                        audioRef.current?.play();
                        setIsPlaying(true);
                      }
                    }}
                    className={`flex h-10 w-10 items-center justify-center rounded-full transition-colors ${
                      isPlaying
                        ? 'bg-primary text-white'
                        : 'bg-gray-200 text-gray-700'
                    }`}
                  >
                    {isPlaying ? (
                      <IoPause className="text-lg" />
                    ) : (
                      <FaPlay className="ml-0.5 text-sm" />
                    )}
                  </button>

                  <button
                    onClick={handleNext}
                    className="text-gray-600 transition-colors hover:text-primary"
                  >
                    <GiNextButton className="text-lg" />
                  </button>

                  <button
                    onClick={() => setIsLooping(!isLooping)}
                    className={`text-gray-600 transition-colors hover:text-primary ${
                      isLooping ? 'text-primary' : ''
                    }`}
                  >
                    <RxLoop className="text-lg" />
                  </button>
                </div>

                {/* Progress Bar */}
                <div className="flex w-full items-center space-x-2">
                  <span className="w-10 text-right text-xs text-gray-500">
                    {formatTime(progress)}
                  </span>
                  <div
                    className="h-1 flex-1 cursor-pointer rounded-full bg-gray-200"
                    onClick={handleProgressClick}
                  >
                    <div
                      className="h-full rounded-full bg-primary transition-all duration-100"
                      style={{
                        width: `${duration ? (progress / duration) * 100 : 0}%`,
                      }}
                    />
                  </div>
                  <span className="w-10 text-xs text-gray-500">
                    {formatTime(duration)}
                  </span>
                </div>
              </div>

              {/* Close Button */}
              <div className="flex flex-1 justify-end">
                <button
                  onClick={() => {
                    setShowControls(false);
                    audioRef.current?.pause();
                    setIsPlaying(false);
                    setCurrentTrackUrl(null);
                  }}
                  className="text-gray-400 transition-colors hover:text-gray-600"
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        onEnded={handleEnded}
        loop={isLooping}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
    </div>
  );
}
