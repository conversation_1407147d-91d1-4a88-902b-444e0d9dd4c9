'use client';

import { useState } from 'react';
import { ContentTab } from './ContentTab';

interface UserProfile {
  _id: string;
  username: string;
  displayName: string;
  role: 'fan' | 'artist';
  artistProfile?: {
    bio?: string;
    socialLinks: {
      platform: string;
      url: string;
    }[];
  };
}

interface ProfileTabsProps {
  profile: UserProfile;
}

export function ProfileTabs({ profile }: ProfileTabsProps) {
  const [activeTab, setActiveTab] = useState('about');

  const tabs = [
    { id: 'about', label: 'About', icon: '👤' },
    { id: 'content', label: 'Content', icon: '🎵' },
    { id: 'tokens', label: 'Tokens', icon: '🪙' },
    { id: 'activity', label: 'Activity', icon: '📊' },
  ];

  return (
    <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`border-b-2 px-1 py-4 text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'about' && (
          <div className="space-y-6">
            {/* Bio */}
            {profile.artistProfile?.bio && (
              <div>
                <h3 className="mb-3 text-lg font-semibold text-gray-900">
                  Bio
                </h3>
                <p className="leading-relaxed text-gray-700">
                  {profile.artistProfile.bio}
                </p>
              </div>
            )}

            {/* Social Links */}
            {profile.artistProfile?.socialLinks &&
              profile.artistProfile.socialLinks.length > 0 && (
                <div>
                  <h3 className="mb-3 text-lg font-semibold text-gray-900">
                    Social Links
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {profile.artistProfile.socialLinks.map((link, index) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                      >
                        <span className="capitalize">{link.platform}</span>
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                          />
                        </svg>
                      </a>
                    ))}
                  </div>
                </div>
              )}

            {/* Empty State */}
            {!profile.artistProfile?.bio &&
              (!profile.artistProfile?.socialLinks ||
                profile.artistProfile.socialLinks.length === 0) && (
                <div className="py-12 text-center">
                  <div className="mb-4 text-gray-400">
                    <svg
                      className="mx-auto h-12 w-12"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    No information available
                  </h3>
                  <p className="text-gray-600">
                    This user hasn&apos;t added any bio or social links yet.
                  </p>
                </div>
              )}
          </div>
        )}

        {activeTab === 'content' && <ContentTab userId={profile._id} />}

        {activeTab === 'tokens' && (
          <div className="py-12 text-center">
            <div className="mb-4 text-gray-400">
              <svg
                className="mx-auto h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900">
              Tokens coming soon
            </h3>
            <p className="text-gray-600">
              Artist tokens and trading information will be displayed here.
            </p>
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="py-12 text-center">
            <div className="mb-4 text-gray-400">
              <svg
                className="mx-auto h-12 w-12"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900">
              Activity coming soon
            </h3>
            <p className="text-gray-600">
              User activity and statistics will be displayed here.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
