'use client';
import React, { useState } from 'react';
import ImageComponent from '../common/ImageComponent';
import { Button } from '../common';
import Modal from '../common/Modal';
import BuyModal from '@/app/app/trading/Presale/BuyModal';
import { useQuery } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';

type Props = {
  show: any;
  hide: any;
  profile: any;
  albumData: any;
};

const TokenContentModal = ({ show, hide, profile, albumData }: Props) => {
  const [openModal, setOpenModal] = useState(false);
  const { data } = useQuery({
    queryKey: ['artist_token'],
    queryFn: () => tokenService.getArtistToken(profile?._id),
  });
  const token = data?.data?.[0];

  return (
    <>
      <Modal show={show} hide={hide} className="sm:max-w-[580px]">
        <div className="m-auto flex w-max flex-col items-center p-4 sm:p-[30px]">
          <ImageComponent
            src="/assets/images/artist/lock.svg"
            height={60}
            width={60}
          />
          <h3 className="mt-5 text-center text-xl font-semibold sm:text-[30px]">
            Token-Gated Content
          </h3>
          <p className="mt-3 text-center text-sm font-normal sm:text-base">
            Hold {albumData?.requiredTokenAmount} {token?.name} Coin to access
            this content
          </p>
          <Button
            className="mt-7 w-full sm:!h-[62px] sm:!w-[300px]"
            arrow={true}
            onClick={() => {
              if (token?.status === 'presale') {
                setOpenModal(true);
              } else if (token?.status === 'launched' && token?.address) {
                const uniswapUrl = `https://app.uniswap.org/explore/tokens/ethereum_sepolia/${token.address}`;
                window.open(uniswapUrl, '_blank');
              }
            }}
          >
            Buy Tokens
          </Button>
        </div>
      </Modal>
      <Modal
        show={openModal}
        hide={setOpenModal}
        className="!h-max !max-w-[600px] !bg-[#EEEEEE]"
      >
        <BuyModal
          profile={profile}
          album={albumData}
          hideFirstModal={hide}
          setOpenModal={setOpenModal}
        />
      </Modal>
    </>
  );
};

export default TokenContentModal;
