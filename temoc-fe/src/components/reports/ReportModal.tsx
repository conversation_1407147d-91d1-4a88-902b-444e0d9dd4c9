'use client';

import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import {
  reportsApi,
  ReportCategory,
  ReportSubcategory,
  ReportUrgency,
  CreateReportData,
} from '@/services/reports';
import Upload from '../common/Icons/Upload';
// import { BsMenuButton } from 'react-icons/bs';
import Button from '../common/Button';

interface ReportModalProps {
  reportedUserId?: string;
  reportedContentId?: string;
  onClose: () => void;
}

export function ReportModal({
  reportedUserId,
  reportedContentId,
  onClose,
}: ReportModalProps) {
  const [formData, setFormData] = useState<CreateReportData>({
    reportedUserId,
    reportedContentId,
    category: ReportCategory.PROFILE,
    subcategory: ReportSubcategory.FAKE_IMPERSONATION,
    description: '',
    urgency: ReportUrgency.MEDIUM,
    isAnonymous: false,
  });
  const [evidenceFiles, setEvidenceFiles] = useState<File[]>([]);

  const reportCategories = reportsApi.getReportCategories();

  const createReportMutation = useMutation({
    mutationFn: (data: { reportData: CreateReportData; files: File[] }) =>
      reportsApi.createReport(data.reportData, data.files),
    onSuccess: (response) => {
      toast.success(
        `Report submitted successfully! Ticket number: ${response.ticketNumber}`,
      );
      onClose();
    },
    onError: (error: any) => {
      console.error('Failed to submit report:', error);
      toast.error(
        error?.response?.data?.message ||
          'Failed to submit report. Please try again.',
      );
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.description.trim()) {
      toast.error('Please provide a description');
      return;
    }
    createReportMutation.mutate({ reportData: formData, files: evidenceFiles });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setEvidenceFiles(files.slice(0, 5)); // Limit to 5 files
  };

  const removeFile = (index: number) => {
    setEvidenceFiles((files) => files.filter((_, i) => i !== index));
  };

  return (
    // <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">

    <div className="sm:p-6">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-xl font-semibold uppercase text-gray-900 sm:text-2xl">
          Submit Report
        </h3>
        {/* <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <svg
            className="h-6 w-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button> */}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Category */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Category *
          </label>
          <select
            value={formData.category}
            onChange={(e) => {
              const category = e.target.value as ReportCategory;
              setFormData({
                ...formData,
                category,
                subcategory: reportCategories[category][0].value,
              });
            }}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            required
          >
            {Object.entries(reportCategories).map(([category]) => (
              <option key={category} value={category}>
                {reportsApi.getCategoryLabel(category as ReportCategory)}
              </option>
            ))}
          </select>
        </div>

        {/* Subcategory */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Specific Issue *
          </label>
          <select
            value={formData.subcategory}
            onChange={(e) =>
              setFormData({
                ...formData,
                subcategory: e.target.value as ReportSubcategory,
              })
            }
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            required
          >
            {reportCategories[formData.category].map((subcategory) => (
              <option key={subcategory.value} value={subcategory.value}>
                {subcategory.label}
              </option>
            ))}
          </select>
        </div>

        {/* Description */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            placeholder="Please provide detailed information about the issue..."
            rows={4}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
            required
          />
        </div>

        {/* Urgency */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Urgency
          </label>
          <select
            value={formData.urgency}
            onChange={(e) =>
              setFormData({
                ...formData,
                urgency: e.target.value as ReportUrgency,
              })
            }
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-primary"
          >
            <option value={ReportUrgency.LOW}>Low</option>
            <option value={ReportUrgency.MEDIUM}>Medium</option>
            <option value={ReportUrgency.HIGH}>High</option>
            <option value={ReportUrgency.CRITICAL}>Critical</option>
          </select>
        </div>

        {/* Evidence Files */}
        <div>
          {/* <label className="mb-2 block text-sm font-medium text-gray-700">
            Evidence (Optional)
          </label>
          <input
            type="file"
            multiple
            accept="image/*,video/*"
            onChange={handleFileChange}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
          /> */}

          <label className="flex h-[102px] w-[146px] cursor-pointer flex-col items-center justify-center gap-2 rounded-[14px] border border-[#CECECE] p-3 transition hover:border-primary">
            {/* <h3 className="max-w-[170px] truncate text-center text-xs text-[#777777]">
              {thumbnailName || 'Upload Screenshot'}
            </h3> */}
            <Upload />
            <div>
              <p className="text-center text-xs text-[#666666]">{'Upload'}</p>
            </div>
            <input
              type="file"
              multiple
              accept="image/*,video/*"
              onChange={handleFileChange}
              className="hidden"
            />
          </label>

          <p className="mt-1 text-xs text-gray-500">
            Upload up to 5 files (images or videos) as evidence
          </p>

          {/* File List */}
          {evidenceFiles.length > 0 && (
            <div className="mt-3 space-y-2">
              {evidenceFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded bg-gray-50 p-2"
                >
                  <span className="truncate text-sm text-gray-700">
                    {file.name}
                  </span>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Anonymous Option */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="anonymous"
            checked={formData.isAnonymous}
            onChange={(e) =>
              setFormData({ ...formData, isAnonymous: e.target.checked })
            }
            className="h-4 w-4 rounded border-gray-300 text-purple-600 focus:ring-purple-500"
          />
          <label htmlFor="anonymous" className="ml-2 text-sm text-gray-700">
            Submit this report anonymously
          </label>
        </div>

        {/* Actions */}
        <div className="flex justify-between gap-3 pt-4">
          <Button
            variant="outline"
            type="button"
            onClick={onClose}
            className="!h-12 !w-[150px]"
            // className="flex-1 rounded-md bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="!h-12 !w-[180px]"
            isLoading={createReportMutation.isPending}
            // disabled={createReportMutation.isPending}
            // className="flex-1 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-700 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {createReportMutation.isPending ? 'Submitting...' : 'Submit Report'}
          </Button>
        </div>
      </form>
    </div>

    // </div>
  );
}
