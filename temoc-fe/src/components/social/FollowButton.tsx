'use client';

import { cn } from '@/lib/utils';

interface FollowButtonProps {
  isFollowing: boolean;
  isLoading?: boolean;
  onClick: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  loadingAction?: 'following' | 'unfollowing';
}

export function FollowButton({
  isFollowing,
  isLoading = false,
  onClick,
  className,
  size = 'md',
  loadingAction,
}: FollowButtonProps) {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base',
  };

  return (
    <button
      onClick={onClick}
      disabled={isLoading}
      className={cn(
        'flex items-center justify-center gap-2 rounded-md font-medium transition-all duration-200',
        sizeClasses[size],
        isFollowing
          ? 'border border-gray-300 bg-gray-100 text-gray-700 hover:bg-red-50 hover:text-red-600'
          : 'border border-primary bg-primary text-white hover:bg-orange-700',
        isLoading && 'cursor-not-allowed opacity-50',
        className,
      )}
    >
      {isLoading ? (
        <>
          <svg className="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          {loadingAction === 'unfollowing' ? 'Unfollowing...' : 'Following...'}
        </>
      ) : (
        <>
          {isFollowing ? (
            <>
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              <span className="hidden sm:inline">Following</span>
              <span className="sm:hidden">Following</span>
            </>
          ) : (
            <>
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <span className="hidden sm:inline">Follow</span>
              <span className="sm:hidden">Follow</span>
            </>
          )}
        </>
      )}
    </button>
  );
}
