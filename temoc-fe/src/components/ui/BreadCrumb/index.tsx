'use client';
import React from 'react';
type BreadcrumbItem = {
  label: string;
  onClick?: () => void;
  disabled?: boolean;
  isActive?: boolean;
};

const BreadCrumb = ({ items }: { items: BreadcrumbItem[] }) => {
  return (
    <nav aria-label="breadcrumb" className="hidden sm:block">
      <ol className="flex items-center gap-1">
        {items.map((item, idx) => (
          <li key={idx}>
            {item.disabled ? (
              <span className="cursor-default text-sm text-[#999999]">
                {item.label}
              </span>
            ) : item.onClick ? (
              <button
                onClick={item.onClick}
                className={`text-sm ${
                  item.isActive
                    ? 'text-[#333333]' // active color here
                    : 'text-[#999999] hover:text-primary'
                }`}
              >
                {item.label}
              </button>
            ) : (
              <span
                className={`text-sm ${
                  item.isActive ? 'text-[#333333]' : 'text-[#999999]'
                }`}
              >
                {item.label}
              </span>
            )}

            {idx < items.length - 1 && (
              <span className="mx-1 text-gray-400">/</span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};
export default BreadCrumb;
