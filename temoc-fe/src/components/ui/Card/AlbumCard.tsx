import ImageComponent from '@/components/common/ImageComponent';
import React from 'react';

interface CardProps {
  imageSrc?: string;
  name?: string;
  followers?: string;
  profileSrc?: string;
  artistName?: string;
}

const AlbumCard: React.FC<CardProps> = ({
  imageSrc,
  name,
  followers,
  profileSrc,
  artistName,
}) => {
  return (
    <div className="w-full rounded-[10px] bg-white p-2 shadow-[0_15px_2px_rgba(0,0,0,0.1)] hover:bg-[#E3E3E3] hover:shadow-[0_15px_15px_rgba(0,0,0,0.2)] xl:p-4">
      <div className="relative">
        <div className="aspect-h-1 aspect-w-1 overflow-hidden rounded-md">
          <img
            src={imageSrc || '/assets/images/artist/tranding1.png'}
            alt={'card'}
            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
          />
        </div>
      </div>
      <div className="mt-1 sm:mt-4">
        <p className="!text-[7px] !font-medium md:!text-xs xl:!text-sm">
          {name}
        </p>
        <div
          className={`${profileSrc && 'xl:gap-2'} flex w-full items-center gap-1 sm:mt-2`}
        >
          {profileSrc && (
            <ImageComponent
              src={profileSrc}
              figClassName="sm:h-[20px] h-2.5 w-2.5 sm:w-[20px] flex-shrink-0"
              className="rounded-full object-cover"
              fill
            />
          )}
          {artistName && (
            <p className="w-[80%] truncate text-[10px] font-normal !text-[#666666] xl:!text-xs">
              {artistName}
            </p>
          )}
          {followers && (
            <p className="!text-[6px] font-normal !text-[#666666] xl:!text-xs">
              {followers}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AlbumCard;
