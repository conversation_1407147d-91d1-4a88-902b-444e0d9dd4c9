import ImageComponent from '@/components/common/ImageComponent';
import React from 'react';

interface CardProps {
  imageSrc?: string;
  name?: string;
  followers?: string;
  profileSrc?: string;
  artistName?: string;
}

const Card: React.FC<CardProps> = ({
  imageSrc,
  name,
  followers,
  profileSrc,
  artistName,
}) => {
  return (
    <div className="w-full rounded-[10px] bg-white p-2 shadow-[0_20px_10px_rgba(0,0,0,0.1)] xl:p-4">
      <div className="relative">
        <div className="aspect-h-1 aspect-w-1 overflow-hidden rounded-md">
          <img
            src={imageSrc}
            alt={'card'}
            className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
          />
        </div>
      </div>
      <div className="mt-1 sm:mt-4">
        <p className="!text-[10px] !font-medium md:!text-xs xl:!text-sm">
          {name}
        </p>
        <div
          className={`${profileSrc && 'xl:gap-2'} flex w-full items-center sm:mt-2`}
        >
          {profileSrc && (
            <ImageComponent
              src={profileSrc}
              figClassName="sm:h-[20px] h-2.5 w-2.5 sm:w-[20px] flex-shrink-0"
              className="rounded-full object-cover"
              fill
            />
          )}
          {artistName && (
            <p className="w-[80%] truncate !text-[10px] font-normal !text-[#666666] xl:!text-xs">
              {artistName}
            </p>
          )}
          {followers && (
            <p className="w-[80%] truncate !text-[10px] font-normal !text-[#666666] xl:!text-xs">
              {followers}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Card;
