import React from 'react';
import Countdown, { CountdownRenderProps } from 'react-countdown';

interface CountdownProps {
  className?: string;
  size?: string;
  key?: any;
  onComplete: () => void;
}

const CountdownComponent: React.FC<CountdownProps> = ({
  className,
  size,
  key,
  onComplete,
}) => {
  // Function to ensure leading zeros are added correctly
  function addLeadingZeros(value: number) {
    return value < 10 ? `0${value}` : value.toString();
  }

  // Renderer function for the countdown
  const renderer = ({ minutes, seconds }: CountdownRenderProps) => {
    const timeStyle = `${size || ''} text-xs sm:text-base lg:text-[22px] font-bold text-primary`;

    return (
      <div className={`${className || ''} flex items-center gap-3`}>
        <div className={timeStyle}>{addLeadingZeros(minutes)}</div>
        <span className="-mt-1 text-xl text-black md:text-2xl">:</span>
        <div className={timeStyle}>{addLeadingZeros(seconds)}</div>
      </div>
    );
  };

  return (
    <Countdown
      key={key}
      date={Date.now() + 2 * 60 * 1000}
      renderer={renderer}
      onComplete={onComplete}
    />
  );
};

export default CountdownComponent;
