import { Menu, Menu<PERSON>utton, MenuItem, MenuItems } from '@headlessui/react';

import React from 'react';
import { IoIosMore } from 'react-icons/io';
import { BsFlag } from 'react-icons/bs';

interface DropdownProps {
  onReportClick?: () => void;
}

export default function Dropdown({ onReportClick }: DropdownProps) {
  return (
    <Menu>
      {/* {({}) => ( */}
      <>
        <MenuButton className="">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
            <IoIosMore className="text-white" size={25} />
          </div>
        </MenuButton>
        <MenuItems
          transition
          anchor="bottom end"
          className="z-50 mt-1 flex origin-top-right flex-col items-center rounded-lg bg-white p-0 shadow-[4px_4px_30px_0px_#0000001A] transition duration-100 ease-out [--anchor-gap:var(--spacing-1)] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0"
        >
          <MenuItem>
            <button
              onClick={onReportClick}
              className="border-tranparent flex h-[50px] w-[172px] cursor-pointer items-center justify-center gap-2.5 rounded-lg border-2 text-primary transition-colors hover:bg-primary/20"
            >
              <span className="text-base font-bold">REPORT</span>
              <BsFlag size={20} />
            </button>
          </MenuItem>
        </MenuItems>
      </>
      {/* )} */}
    </Menu>
  );
}
