import { BiLogoTelegram } from 'react-icons/bi';
import { Container } from '@/components/common';
import ImageComponent from '@/components/common/ImageComponent';
import { FaXTwitter } from 'react-icons/fa6';
// import { FaFacebookF } from 'react-icons/fa';
// import { FaWhatsapp } from 'react-icons/fa';
// import { IoLogoInstagram } from 'react-icons/io5';
import React from 'react';
import Link from 'next/link';
import { FaTiktok } from 'react-icons/fa';

const Footer = () => {
  return (
    <>
      <div className="bg-[url(/assets/images/home/<USER>">
        <Container>
          <div className="flex flex-col justify-between md:flex-row">
            <div className="w-full max-w-[500px]">
              <div className="flex items-end gap-2">
                <Link href="/">
                  <ImageComponent
                    src="/assets/images/home/<USER>"
                    height={50}
                    width={50}
                    alt="temoc"
                  />
                </Link>
                <p className="text-2xl font-normal text-white">TEMOC</p>
              </div>
              <p className="p mt-[30px] text-base !font-light !text-white sm:!text-lg">
                Don&apos;t miss out on this exclusive opportunity! Our
                limited-time presale gives you early access to premium features,
                special discounts, and VIP perks before the official launch. Act
                fast—spots are filling up quickly, and once they&apos;re gone,
                they&apos;re gone!
              </p>
            </div>
            <div className="mt-[30px] flex flex-col justify-between md:mt-0">
              <ul className="p space-y-5 text-white">
                <Link href="/">
                  <li>Home</li>
                </Link>
                <Link href="/#abouts">
                  <li>About</li>
                </Link>
                <Link href="/#explore">
                  <li>Explore</li>
                </Link>
                <Link href="/#tokenomics">
                  <li>Tokenomics</li>
                </Link>
              </ul>

              <ul className="p mt-12 flex gap-2.5 text-white md:mt-0">
                {/* <li className="bg-[#FF6E00] h-8 w-8 cursor-pointer rounded-full text-white flex justify-center items-center text-base">
                  <FaWhatsapp />
                </li> */}
                <Link
                  href="https://x.com/TemocHQ"
                  target="_blank"
                  className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#FF6E00] text-base text-white"
                >
                  <FaXTwitter />
                </Link>
                <li className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#FF6E00] text-base text-white">
                  <Link
                    href="https://t.me/temochq"
                    target="_blank"
                    className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#FF6E00] text-base text-white"
                  >
                    <BiLogoTelegram />
                  </Link>
                  {/* <FaFacebookF /> */}
                </li>
                <li className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#FF6E00] text-base text-white">
                  {/* <IoLogoInstagram /> */}
                  <Link
                    href="https://www.tiktok.com/@temochq"
                    target="_blank"
                    className="flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-[#FF6E00] text-base text-white"
                  >
                    <FaTiktok />
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-[30px] h-1 w-full bg-white opacity-[20%]"></div>
          <div className="mt-[30px] flex flex-col items-center justify-center gap-7 md:flex-row">
            <Link href="/">
              <p className="p !text-white">
                © 2025 TEMOC • All Rights Reserved
              </p>
            </Link>
            <p className="p hidden text-white md:block">|</p>
            <div className="flex flex-wrap justify-center gap-2.5">
              <Link href="/terms-and-conditions">
                <p className="p !text-white">Terms &amp; Conditions</p>
              </Link>

              {/* <p className="p text-white">|</p> */}
              <Link href="/privacy">
                <p className="p whitespace-nowrap !text-white">
                  Privacy Policy
                </p>
              </Link>
              {/* <p className="p whitespace-nowrap text-white">|</p>
              <Link href="/SAFT-Agreement">
                <p className="p !text-white">SAFT Agreement</p>
              </Link> */}
            </div>
          </div>
        </Container>
      </div>
    </>
  );
};

export default Footer;
