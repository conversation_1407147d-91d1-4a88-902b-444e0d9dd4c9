'use client';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/common';
import { DynamicWidget } from '@dynamic-labs/sdk-react-core';
const Navigation = [
  {
    name: 'Home',
    href: `/#main`,
  },
  {
    name: 'Discover',
    href: `/#discover`,
  },
  {
    name: 'About',
    href: `/#about`,
  },
];
interface HeaderProps {
  activeTab?: number;
  setActiveTab: Dispatch<SetStateAction<number>>;
}
const Header = ({ activeTab, setActiveTab }: HeaderProps) => {
  const [navbar, setNavbar] = useState(false);
  const [scrollBackground, setScrollBackground] = useState(false);
  const [isScrolledDown] = useState(false);
  useEffect(() => {
    if (navbar) {
      if (typeof window !== 'undefined') {
        document.body.classList.add('no-scroll');
      }
    } else {
      if (typeof window !== 'undefined') {
        document.body.classList.remove('no-scroll');
      }
    }
  }, [navbar]);
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = typeof window !== 'undefined' ? window.scrollY : 0;
      if (scrollPosition > 10) {
        setScrollBackground(true);
      } else {
        setScrollBackground(false);
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  return (
    <div className="h-20 lg:h-[104px]">
      <div
        className={` ${
          isScrolledDown ? `top-0` : `top-0`
        } fixed z-[200] h-20 w-full border-b border-[#212428] bg-white px-4 duration-300 ease-linear lg:h-[104px] lg:px-12 xl:px-20 xs:px-4`}
      >
        <div
          className={` ${
            scrollBackground ? 'mt-0' : ''
          } flex h-full items-center justify-between`}
        >
          <div
            className={` ${
              scrollBackground ? '' : ''
            } flex w-full items-center justify-between lg:block lg:w-auto`}
          >
            <Link href="/">
              <img src="/assets/images/logo.svg" alt="temoc" />
            </Link>

            <div className="flex items-center gap-3 lg:hidden">
              <div className="flex-wrap gap-2.5">
                <DynamicWidget
                  variant="modal"
                  innerButtonComponent={
                    <Button className="AtConnect">Get Started</Button>
                  }
                  buttonClassName="!bg-[#C9FA49] !text-[#333333] font-bold rounded-full"
                />
              </div>
              <button
                className="relative z-50 flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-md bg-primary p-1.5 text-white outline-none"
                onClick={() => setNavbar(!navbar)}
              >
                {navbar ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293
                          4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    strokeWidth={2}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                )}
              </button>
            </div>
          </div>

          <div
            className={`bgBlur absolute top-20 flex items-center duration-300 ease-linear lg:relative lg:top-0 lg:border-none ${
              navbar
                ? 'h-[95vh] w-full overflow-auto border-[#FACA00] bg-white/90 py-6 lg:h-auto lg:w-auto lg:overflow-visible'
                : 'right-0 mt-0 h-[95vh] w-0 overflow-hidden border-transparent px-0 lg:h-auto lg:w-auto lg:overflow-visible'
            } `}
          >
            <ul className="relative top-0 flex h-full w-full flex-col items-center justify-start gap-0 space-y-5 font-display font-medium lg:flex-row lg:gap-10 lg:space-y-0">
              {Navigation?.map((item, i) => (
                <Link href={item.href} key={i}>
                  <li
                    role="button"
                    className={`${
                      activeTab === i
                        ? 'border-[#FF6E00] hover:brightness-110'
                        : 'border-transparent text-[#212428] hover:text-primary hover:brightness-110'
                    } relative cursor-pointer whitespace-nowrap border-b-[2px] pb-3 text-base font-normal lg:top-2`}
                    onClick={() => {
                      setNavbar(false);
                      setActiveTab(i);
                    }}
                  >
                    {item.name}
                  </li>
                </Link>
              ))}
              <div className="hidden flex-wrap gap-2.5 lg:flex">
                <DynamicWidget
                  variant="modal"
                  innerButtonComponent={
                    <Button className="AtConnect">Get Started</Button>
                  }
                  buttonClassName="!bg-[#C9FA49] !text-[#333333] font-bold rounded-full"
                />
              </div>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
