'use client';
import React, { useRef, useState } from 'react';
import ImageComponent from '@/components/common/ImageComponent';
import { FaPlay } from 'react-icons/fa6';
import { IoPause } from 'react-icons/io5';
import { BsThreeDotsVertical } from 'react-icons/bs';

interface MobileLibraryItemProps {
  index?: any;
  track?: any;
}

const MobileLibraryItem: React.FC<MobileLibraryItemProps> = ({
  index,
  track,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const togglePlay = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
    } else {
      audio.play();
      setIsPlaying(true);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
  };

  return (
    <div className="flex items-start justify-between space-y-3 sm:items-center">
      <div className="flex items-center gap-3">
        {index + 1}
        <div
          className="group relative w-fit cursor-pointer"
          onClick={togglePlay}
        >
          <ImageComponent
            src={track?.thumbnailUrl || '/assets/images/trading/tableimg.svg'}
            height={48}
            width={48}
          />
          <div className="absolute left-1/2 top-1/2 flex h-5 w-5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary">
            {isPlaying ? (
              <IoPause className="text-[12px] text-white" />
            ) : (
              <FaPlay className="text-[10px] text-white" />
            )}
          </div>
        </div>
        <div>
          <p
            className="inline-block max-w-[100px] truncate text-sm text-[#333333]"
            title={track?.title}
          >
            {track?.title || 'Freak In Me'}
          </p>
          {/* <p
            className="inline-block max-w-[100px] truncate text-xs text-[#666666]"
            title={track?.title}
          >
            {track?.title || 'Album Name Here'}
          </p> */}
        </div>
        <audio
          ref={audioRef}
          src={track?.fileUrl}
          className="hidden"
          onEnded={handleEnded}
        />
      </div>
      <p className="text-sm text-[#333333]">{track?.duration}</p>
      <BsThreeDotsVertical className="text-base text-[#666666]" />
    </div>
  );
};

export default MobileLibraryItem;
