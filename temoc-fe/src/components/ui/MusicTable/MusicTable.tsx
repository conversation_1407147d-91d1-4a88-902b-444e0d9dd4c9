import React from 'react';
import { Heart, Share2, MessageCircle } from 'lucide-react';
import Button from '@/components/common/Button';
import ImageComponent from '@/components/common/ImageComponent';

interface Column {
  key: string;
  label: string;
}

interface DataItem {
  id: number;
  title: string;
  plays: string;
  album: string;
  duration: string;
  likes: string;
  shares: string;
  comments: string;
  popular: string;
  [key: string]: any; // Allow arbitrary properties
}

interface MusicTableProps {
  columns: Column[];
  data: DataItem[];
}

const MusicTable: React.FC<MusicTableProps> = ({ columns, data }) => {
  return (
    <div className="overflow-x-auto">
      <table className="">
        <thead className="text-sm font-normal text-black">
          <tr>
            {columns.map((column) => (
              <th
                key={column.key}
                scope="col"
                className="py-3 pl-5 text-left font-medium"
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="">
          {data.map((item) => (
            <tr key={item.id} className="text-center text-sm">
              {columns.map((column) => {
                let cellContent;
                switch (column.key) {
                  case 'title':
                    cellContent = (
                      <div className="flex items-center gap-2">
                        {item.nmber}
                        <ImageComponent src={item.src} height={48} width={48} />
                        {item.title}
                      </div>
                    );
                    break;
                  case 'share':
                    cellContent = (
                      <div className="flex items-center gap-2">
                        <div className="flex flex-col items-center">
                          <Heart className="h-4 w-4 text-red-500" />
                          <span>{item.likes}</span>
                        </div>
                        <div className="flex flex-col items-center">
                          <Share2 className="h-4 w-4" />
                          <span>{item.shares}</span>
                        </div>{' '}
                        <div className="flex flex-col items-center">
                          <MessageCircle className="h-4 w-4" />
                          <span>{item.comments}</span>
                        </div>
                        <Button className="h-[27px] !text-[10px]">
                          Add To Wishlist
                        </Button>
                      </div>
                    );
                    break;
                  default:
                    cellContent = item[column.key];
                }
                return (
                  <td key={column.key} className="px-4 py-4">
                    {cellContent}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
export default MusicTable;
