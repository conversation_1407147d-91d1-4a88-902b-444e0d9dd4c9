'use client';
import React from 'react';

interface NoDataProps {
  icon?: React.ReactNode;
  heading?: string;
  description?: string;
  showFooter?: boolean;
}

const NoData: React.FC<NoDataProps> = ({
  icon,
  heading = 'No collections yet',
  description = `Collections is an improved way to organize your posts and it helps
  your members explore all your great work.`,
  showFooter = true,
}) => {
  return (
    <div>
      <div className="mt-8 flex flex-col items-center justify-center gap-2">
        {icon}
        <h3 className="text-center text-xl text-[#333333] sm:text-[22px]">
          {heading}
        </h3>
        <p className="text-center text-base text-[#666666]">{description}</p>
      </div>
      {showFooter && (
        <p className="mt-20 text-center text-sm text-[#666666] sm:mt-40">
          © 2025 TEMOC • All Rights Reserved
        </p>
      )}
    </div>
  );
};

export default NoData;
