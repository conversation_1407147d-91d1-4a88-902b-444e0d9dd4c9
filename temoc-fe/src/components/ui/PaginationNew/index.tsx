import { MdKeyboardArrowRight, MdKeyboardArrowLeft } from 'react-icons/md';
import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const PaginationNew: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const getPages = () => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        pages.push(1, 2, 3, 4, '...', totalPages);
      } else if (currentPage > totalPages - 3) {
        pages.push(
          1,
          '...',
          totalPages - 3,
          totalPages - 2,
          totalPages - 1,
          totalPages,
        );
      } else {
        pages.push(
          1,
          '...',
          currentPage - 1,
          currentPage,
          currentPage + 1,
          '...',
          totalPages,
        );
      }
    }
    return pages;
  };

  const handlePageChange = (event: React.MouseEvent, page: number | string) => {
    event.preventDefault();
    if (typeof page === 'number' && page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  const pages = getPages();
  return (
    <nav className="flex items-center justify-center px-4 py-3 sm:px-6">
      <div className="overflow-x-auto">
        <div className="flex w-full max-w-[400px] justify-center gap-3">
          {/* Previous Page Button */}
          <button
            onClick={(event) => handlePageChange(event, currentPage - 1)}
            disabled={currentPage === 1}
            className="flex h-10 w-10 items-center justify-center rounded-md border border-black text-black disabled:cursor-not-allowed disabled:opacity-50 xs:h-8 xs:w-8 xs:text-xs"
          >
            <span className="sr-only">Previous</span>
            <MdKeyboardArrowLeft className="text-xl" />
          </button>
          {/* Pages */}
          {pages.map((page, index) => (
            <button
              key={index}
              onClick={(event) => handlePageChange(event, page)}
              className={`flex h-10 w-10 items-center justify-center rounded-md border border-black text-black xs:h-8 xs:w-8 xs:text-xs ${
                currentPage === page ? 'z-10 !border-primary !text-primary' : ''
              } ${page === '...' ? 'pointer-events-none' : ''}`}
            >
              {page}
            </button>
          ))}
          {/* Next Page Button */}
          <button
            onClick={(event) => handlePageChange(event, currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex h-10 w-10 items-center justify-center rounded-md border border-black text-black disabled:cursor-not-allowed disabled:opacity-50 xs:h-8 xs:w-8 xs:text-xs"
          >
            <span className="sr-only">Next</span>
            <MdKeyboardArrowRight />
          </button>
        </div>
      </div>
    </nav>
  );
};

export default PaginationNew;
