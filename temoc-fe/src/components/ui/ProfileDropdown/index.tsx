import ImageComponent from '@/components/common/ImageComponent';
import { useAuth } from '@/hooks/useAuth';
import { useDynamicContext } from '@dynamic-labs/sdk-react-core';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import Link from 'next/link';
import React, { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { truncateAddress } from '@/lib/functions';
import { useRouter } from 'next/navigation';
import { CgProfile } from 'react-icons/cg';
import { MdLogout } from 'react-icons/md';

const data = [
  { id: 1, icon: <CgProfile />, heading: 'Profile', href: '/app/profile' },
  { id: 2, icon: <MdLogout />, heading: 'Logout', href: '#' },
];

export default function ProfileDropdown() {
  const { primaryWallet, handleLogOut } = useDynamicContext();
  const { logout, user } = useAuth();
  const queryClient = useQueryClient();
  const router = useRouter();
  // const handleLogout = () => {};

  useEffect(() => {
    if (primaryWallet?.isConnected) {
      queryClient.refetchQueries({
        queryKey: ['auth-user'],
      });
    }
  }, [primaryWallet?.isConnected, queryClient]);

  // Remove the second useEffect that was causing unnecessary refetches

  const fullName =
    (user?.firstName ?? '') + ' ' + (user?.lastName ?? '') || 'Andy Haskin';

  let profilePic = null;

  if (
    user?.role === 'artist' &&
    user?.artistProfile?.profilePic != null &&
    user.artistProfile.profilePic.replace(/\s/g, '') !== ''
  ) {
    profilePic = user.artistProfile.profilePic;
  } else if (
    user?.avatarUrl != null &&
    user.avatarUrl.replace(/\s/g, '') !== ''
  ) {
    profilePic = user.avatarUrl;
  }

  const firstLetter =
    user?.firstName && user.firstName.length > 0
      ? user.firstName.charAt(0).toUpperCase()
      : '';

  return (
    <Menu>
      {({ close }) => (
        <>
          <MenuButton className="flex items-center gap-2">
            {profilePic ? (
              <ImageComponent
                src={profilePic}
                alt="Avatar"
                fill
                figClassName="h-10 w-10"
                className="h-10 w-10 rounded-full object-cover"
              />
            ) : (
              <div className="flex h-10 w-10 select-none items-center justify-center rounded-full bg-primary text-lg font-semibold text-white">
                {firstLetter}
              </div>
            )}

            <div>
              <h6 className="hidden max-w-[100px] truncate whitespace-nowrap text-xs capitalize text-[#333333] sm:block sm:text-sm md:max-w-[140px]">
                {fullName}
              </h6>
              <p className="mt-0.5 hidden text-left text-[10px] font-medium text-primary sm:block sm:text-xs">
                {truncateAddress(user?.wallets?.[0]?.address ?? '')}
              </p>
            </div>
          </MenuButton>
          <MenuItems
            transition
            anchor="bottom end"
            className="z-50 mt-1 min-w-[200px] origin-top-right rounded-xl bg-white shadow-[4px_4px_30px_0px_#0000001A] transition duration-100 ease-out [--anchor-gap:var(--spacing-1)] focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0"
          >
            <MenuItem>
              <button className="flex w-full flex-col rounded-lg">
                <div className="group block w-full border-b border-borderColor px-3 py-2 sm:hidden">
                  <h6 className="min-w-[200px] pt-2 text-left text-xs text-[#333333]">
                    {fullName}
                  </h6>
                  <p className="mt-1 text-left text-[10px] font-medium text-primary sm:text-xs">
                    {truncateAddress(user?.wallets?.[0]?.address ?? '')}
                  </p>
                </div>
                {data?.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <Link
                      href={item.href || '#'}
                      onClick={() => {
                        if (item.id === 2) {
                          handleLogOut();
                          localStorage.removeItem('auth-user');
                          router.replace('/');
                          logout();
                        }
                        close();
                      }}
                    >
                      <div
                        className={`${item.heading == 'Logout' && ''} ${index !== data.length - 1 ? 'border-b' : 'border-none'} group w-full border-borderColor px-3 py-3 hover:bg-gray-200`}
                      >
                        <h2 className="flex items-center gap-2 text-left font-display !text-xs text-secondary group-hover:text-primary sm:text-center md:!text-sm">
                          <span>{item.icon}</span>
                          {item.heading}
                        </h2>
                      </div>
                    </Link>
                  </React.Fragment>
                ))}
              </button>
            </MenuItem>
          </MenuItems>
        </>
      )}
    </Menu>
  );
}
