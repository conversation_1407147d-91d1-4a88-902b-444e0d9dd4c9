import React from 'react';
import {
  TokenCreation,
  PeopleAlts,
  MusicCard,
} from '@/components/common/Icons';
import { useQuery } from '@tanstack/react-query';
import { tokenService } from '@/services/token.service';

const ReferralCard = ({ presaleData }: any) => {
  const { data: tokenData } = useQuery({
    queryKey: ['token'],
    queryFn: () => tokenService.getToken(),
  });

  const tokens = tokenData?.data?.[0];

  const cardsData = [
    {
      icon: <TokenCreation />,
      title: 'Token Price',
      value: `$ ${presaleData?.fairLaunchAmount ?? '0'}`,
    },
    {
      icon: <PeopleAlts />,
      title: 'Total Supply',
      value: tokens?.totalSupply ?? '0',
    },
    {
      icon: <MusicCard />,
      title: 'Listing On',
      value: `$ ${presaleData?.listingOn ?? '0'}`,
    },
  ];
  return (
    <div>
      <div className="mt-6 grid gap-5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5">
        {cardsData.map((item, index) => {
          return (
            <div
              key={index}
              className="w-full rounded-[20px] bg-white p-5"
              style={{ boxShadow: '0px 0px 20px 0px #0000000' }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {item.icon}
                  <h3 className="text-base font-normal text-black-300">
                    {item.title}
                  </h3>
                </div>
              </div>
              <h4 className="mt-5 text-[32px] font-normal text-primary">
                {item.value}
              </h4>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ReferralCard;
