'use client';

import * as React from 'react';
import { Listbox } from '@headlessui/react';
import { Check, ChevronDown } from 'lucide-react';
import clsx from 'clsx';

type Option = {
  label: string;
  value: string;
};

type SelectComponentProps = {
  options: Option[];
  selected: Option;
  onSelect: (value: Option) => void;
  placeholder?: string;
  className?: string;
};

export default function Select({
  options,
  selected,
  onSelect,
  placeholder = 'Select',
  className = '',
}: SelectComponentProps) {
  return (
    <Listbox value={selected} onChange={onSelect}>
      <div className={clsx('relative', className)}>
        <Listbox.Button className="relative w-full cursor-default rounded-xl border border-gray-300 bg-white py-2 pl-4 pr-10 text-left text-base font-medium text-black focus:outline-none">
          <span className="block truncate">
            {selected?.label || placeholder}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-3 flex items-center">
            <ChevronDown className="h-5 w-5 text-black" />
          </span>
        </Listbox.Button>

        <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-xl bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          {options.map((option) => (
            <Listbox.Option
              key={option.value}
              value={option}
              className={({ active }) =>
                clsx(
                  'relative cursor-default select-none px-4 py-2',
                  active ? 'bg-gray-100 text-black' : 'text-gray-900',
                )
              }
            >
              {({ selected }) => (
                <>
                  <span
                    className={clsx(
                      'block truncate',
                      selected && 'font-semibold',
                    )}
                  >
                    {option.label}
                  </span>
                  {selected && (
                    <span className="absolute inset-y-0 right-4 flex items-center text-black">
                      <Check className="h-4 w-4" />
                    </span>
                  )}
                </>
              )}
            </Listbox.Option>
          ))}
        </Listbox.Options>
      </div>
    </Listbox>
  );
}
