import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export function AboutSkeleton() {
  return (
    <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
      <form className="mx-auto mt-7 w-full max-w-[1040px] rounded-[20px] bg-gray-300 p-5 sm:p-10">
        <div className="grid grid-cols-1 gap-4 border-[#CECECE] pb-4 sm:grid-cols-2 sm:gap-12 sm:border-b sm:pb-5">
          <div>
            <Skeleton width={100} height={20} />
            <Skeleton className="mt-2" width="100%" height={30} />
          </div>
          <div>
            <Skeleton width={100} height={20} />
            <Skeleton className="mt-2" width="100%" height={30} />
          </div>
        </div>
        <div className="grid grid-cols-1 gap-4 border-[#CECECE] pb-4 sm:my-5 sm:grid-cols-2 sm:gap-12 sm:border-b sm:pb-5">
          <div>
            <Skeleton width={100} height={20} />
            <Skeleton className="mt-2" width="100%" height={30} />
          </div>
          <div>
            <Skeleton width={100} height={20} />
            <Skeleton className="mt-2" width="100%" height={30} />
          </div>
        </div>
        <div>
          <Skeleton width={100} height={20} />
          <Skeleton className="mt-2" width="100%" height={80} />
        </div>
        <div className="mt-5 flex justify-center sm:mt-8 sm:justify-end">
          <Skeleton width={120} height={40} />
        </div>
      </form>
    </SkeletonTheme>
  );
}
