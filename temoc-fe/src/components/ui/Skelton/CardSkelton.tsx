import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import React from 'react';

const CardSkeleton: React.FC = () => {
  return (
    <div className="w-full rounded-[10px] bg-gray-300 p-2 xl:p-4">
      <div className="relative">
        <div className="h-[80px] w-[120px] overflow-hidden rounded-md sm:h-[180px] sm:w-[212px]">
          <Skeleton height="100%" className="h-full w-full" />
        </div>
      </div>
      <div className="mt-1 sm:mt-4">
        <Skeleton width={100} height={12} className="!text-[10px]" />
        <div className="flex w-full items-center gap-1 sm:mt-2 xl:gap-2">
          <Skeleton circle height={20} width={20} />
          <Skeleton width={80} height={10} />
        </div>
        <Skeleton width={60} height={10} className="mt-1" />
      </div>
    </div>
  );
};

export default CardSkeleton;
