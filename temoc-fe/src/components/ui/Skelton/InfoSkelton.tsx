import Skeleton, { SkeletonTheme } from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

export default function InfoSkeleton() {
  return (
    <div className="bg-gray-300">
      <SkeletonTheme baseColor="#f3f4f6" highlightColor="#e5e7eb">
        <div className="relative h-[300px] rounded-xl px-4 py-5 shadow-sm sm:px-8">
          <div className="flex h-full w-full items-center justify-between gap-2 sm:items-end sm:gap-4">
            <div className="flex items-center gap-2 sm:gap-4">
              <div className="relative w-[100px] sm:w-[160px]">
                <div className="flex h-[100px] w-[100px] items-center justify-center overflow-hidden rounded-full bg-gray-100 sm:h-[160px] sm:w-[160px]">
                  <Skeleton circle width="100%" height="100%" />
                </div>
                <div className="mt-3 flex items-center justify-center">
                  <Skeleton width={100} height={16} />
                </div>
              </div>
              <div className="space-y-3">
                <Skeleton width={110} height={20} />
                <Skeleton width={130} height={16} />
                <Skeleton width={100} height={16} />
              </div>
            </div>
            <div className="flex flex-col items-center justify-center gap-2">
              <div className="h-[40px] w-[40px] overflow-hidden rounded-xl bg-gray-100 sm:h-[80px] sm:w-[80px]">
                <Skeleton width="100%" height="100%" />
              </div>
              <Skeleton width={40} height={12} />
            </div>
          </div>
        </div>
      </SkeletonTheme>
    </div>
  );
}
