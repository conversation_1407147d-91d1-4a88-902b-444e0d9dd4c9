import { Verfied } from '@/components/common/Icons';
// import ImageComponent from '@/components/common/ImageComponent';
import React from 'react';

interface SliderCardProps {
  imageSrc: any;
  name: any;
  followers: any;
  pricePerToken: any;
  itemCount: any;
}

const SliderCard: React.FC<SliderCardProps> = ({
  imageSrc,
  name,
  followers,
  pricePerToken,
  itemCount,
}) => {
  return (
    <div className="group w-full rounded-[10px] bg-white p-4 shadow-[0_20px_1px_rgba(0,0,0,0.1)]">
      <div className="aspect-h-1 aspect-w-1 overflow-hidden rounded-md">
        <img
          src={imageSrc}
          alt={'card'}
          className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
      </div>
      {/* <ImageComponent
        src={imageSrc}
        height={220}
        width={220}
        className="w-full"
      /> */}
      <div className="mt-4">
        <div className="flex items-center justify-between gap-2">
          <div className="flex max-w-[60%] items-center gap-1 sm:max-w-[75%] xs1:max-w-[55%]">
            <p className="truncate text-[10px] !font-medium sm:!text-xs">
              {name}
            </p>
            <span className="flex-shrink-0">
              <Verfied />
            </span>
          </div>
          <p className="max-w-[50%] truncate !text-[8px] !font-medium !text-[#FF8000] sm:!text-[10px]">
            {pricePerToken}
          </p>
        </div>
        <div className="mt-1 flex items-center justify-between gap-2">
          <p className="text-[10px] font-normal text-[#666666] sm:!text-xs">
            {followers}
          </p>
          <p className="text-right text-[10px] font-normal text-[#666666] sm:text-xs">
            {itemCount}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SliderCard;
