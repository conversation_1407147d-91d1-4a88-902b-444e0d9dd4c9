import React, { useState } from 'react';
import Facebook from '@/components/common/Icons/Facebook';
import Insta from '@/components/common/Icons/Insta';
import Youtube from '@/components/common/Icons/Youtube';
import X from '@/components/common/Icons/X';
import TikTok from '@/components/common/Icons/TikTok';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { userService } from '@/services/user.service';
import { Input } from '@/components/common/Forms/Input';
import { BiSolidEdit } from 'react-icons/bi';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { MdDelete } from 'react-icons/md';

const socialPlatforms = [
  { name: 'facebook', icon: <Facebook /> },
  { name: 'instagram', icon: <Insta /> },
  { name: 'youtube', icon: <Youtube /> },
  { name: 'twitter', icon: <X /> },
  { name: 'tiktok', icon: <TikTok /> },
];

const SocialLinks = () => {
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [newLink, setNewLink] = useState('');
  const [isValid, setIsValid] = useState(true);

  const queryClient = useQueryClient();

  const { data: socialLinksData } = useQuery({
    queryKey: ['artist-links'],
    queryFn: () => userService.getArtistSocialLinks(),
  });

  const links = socialLinksData?.data?.socialLinks || [];

  const { mutate: addLink } = useMutation({
    mutationFn: (data: { platform: string; url: string }) =>
      userService.addArtistSocialLink(data),
    onSuccess: () => {
      toast.success('Link added successfully.');
      queryClient.invalidateQueries({ queryKey: ['artist-links'] });
      setEditIndex(null);
    },
    // onError: () => toast.error('Failed to add link.'),
  });

  const { mutate: updateLink } = useMutation({
    mutationFn: (data: { id: string; url: string }) =>
      userService.updateArtistSocialLink(data.id, { url: data.url }),
    onSuccess: () => {
      toast.success('Link updated successfully.');
      queryClient.invalidateQueries({ queryKey: ['artist-links'] });
      setEditIndex(null);
    },
  });
  const { mutate: deleteLink } = useMutation({
    mutationFn: (id: string) => userService.deleteArtistSocialLink(id),
    onSuccess: () => {
      toast.success('Link deleted successfully.');
      queryClient.invalidateQueries({ queryKey: ['artist-links'] });
    },
  });
  const isValidProfileUrl = (url: string) => {
    const profileUrlPattern =
      /^(https?:\/\/)?(www\.)?(facebook\.com|instagram\.com|youtube\.com|tiktok\.com|twitter\.com)\/(@?[A-Za-z0-9._-]+)\/?$/i;
    return profileUrlPattern.test(url);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.trim(); // optional: trim to remove spaces
    setNewLink(value);
    // Check if value is not empty and matches valid pattern
    const isValidInput = value !== '' && isValidProfileUrl(value);
    setIsValid(isValidInput);
  };

  const handleSave = (platform: string, existing: boolean, id?: string) => {
    if (!isValid) {
      toast.error('Invalid URL format. Please enter a valid profile link.');
      return;
    }
    if (existing && id) {
      updateLink({ id, url: newLink });
    } else {
      addLink({ platform, url: newLink });
    }
  };
  return (
    <div>
      <h3 className="text-center text-2xl sm:text-3xl">
        ADD YOUR SOCIAL LINKS
      </h3>
      <p className="mt-5 text-center text-base text-gray-600">
        Help people find you wherever you are. Connect your other accounts to
        show them on your page. We&apos;ll never post on your behalf.
      </p>
      <ul className="mt-8 space-y-4">
        {socialPlatforms.map((platform, index) => {
          const savedLink = links.find(
            (l: any) => l.platform === platform.name,
          );
          const savedId = savedLink && savedLink?._id;

          return (
            <li
              key={platform.name}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <span className="h-5 w-5">{platform.icon}</span>
                <span className="capitalize">{platform.name}</span>
              </div>

              <div className="flex items-center gap-2">
                {editIndex === index ? (
                  <>
                    <Input
                      name="link"
                      value={newLink}
                      onChange={handleChange}
                      placeholder="Enter profile link..."
                      className={`!py-1 !text-base ${!isValid ? 'border-red-500' : ''}`}
                    />
                    <button
                      onClick={() =>
                        handleSave(platform.name, !!savedLink, savedId)
                      }
                      className="rounded bg-primary px-3 py-1 text-sm text-white"
                    >
                      Save
                    </button>
                  </>
                ) : savedLink ? (
                  <>
                    <BiSolidEdit
                      onClick={() => {
                        setEditIndex(index);
                        setNewLink(savedLink.url);
                      }}
                      size={18}
                      className="cursor-pointer text-gray-600 hover:text-primary"
                    />
                    <MdDelete
                      onClick={() => deleteLink(savedId ?? '')}
                      className="h-5 w-5 cursor-pointer text-red-600"
                    />
                  </>
                ) : (
                  <button
                    onClick={() => {
                      setEditIndex(index);
                      setNewLink('');
                    }}
                    className="h-[27px] w-[89px] rounded-full border border-gray-600 text-xs font-semibold text-gray-600"
                  >
                    CONNECT
                  </button>
                )}
              </div>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default SocialLinks;
