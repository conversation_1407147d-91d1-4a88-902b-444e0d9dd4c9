import { Switch } from '@headlessui/react';

interface IProps {
  enable?: boolean;
  loading?: boolean;
  onChange?: (checked: boolean) => void;
  parentClass?: string;
  childClass?: string;
  translateClass?: string;
  disabled?: boolean;
}

export default function SwitchButton({
  enable = false,
  loading = false,
  onChange,
  parentClass = '',
  childClass = '',
  translateClass,
  disabled = false,
}: IProps) {
  const isDisabled = disabled || loading;

  return (
    <Switch
      checked={enable}
      disabled={isDisabled}
      onChange={onChange}
      className={`relative inline-flex h-[20px] w-[40px] shrink-0 items-center rounded-full border transition-colors duration-200 ease-in-out focus:outline-none ${enable ? 'border-primary' : 'border-[#D9D9D9]'} ${parentClass} `}
    >
      <span
        aria-hidden="true"
        className={`inline-block h-4 w-4 transform rounded-full border shadow-lg ring-0 transition duration-200 ease-in-out ${enable ? 'bg-primary' : 'bg-[#D9D9D9]'} ${enable ? (translateClass ?? 'translate-x-5') : 'translate-x-0'} ${childClass} `}
      />
    </Switch>
  );
}
