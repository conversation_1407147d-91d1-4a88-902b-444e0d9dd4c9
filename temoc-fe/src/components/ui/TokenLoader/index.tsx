import Loader from '@/components/common/Loader';
import Modal from '@/components/common/Modal';
import React from 'react';
interface Iprops {
  setOpenModal: any;
  openModal: any;
  desc?: string;
  title?: string;
}
const TokenLoader = ({ setOpenModal, openModal, desc, title }: Iprops) => {
  return (
    <Modal
      className="hideScrollbar !h-max !max-w-[600px]"
      show={openModal}
      hide={() => setOpenModal(false)}
    >
      <div className="mb-pb-8 flex flex-col items-center justify-center gap-4 pb-5 text-center">
        <Loader loading={true} size={50} />
        <h2 className="!font-display text-3xl text-black sm:text-3xl">
          Please wait {title}
        </h2>
        <p className="text-xl text-black">
          {desc}
          We are initializing the payment widget.
        </p>
      </div>
    </Modal>
  );
};

export default TokenLoader;
