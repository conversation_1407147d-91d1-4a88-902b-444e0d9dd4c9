// components/TokenPerformanceChart.jsx
'use client';

import {
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { FaCoins } from 'react-icons/fa';

const data = [
  { name: 'Jan', value: 0.001, prediction: 0.001 },
  { name: 'Feb', value: 0.0005, prediction: 0.001 },
  { name: 'Mar', value: 0.0011, prediction: 0.002 },
  { name: 'Apr', value: 0.0025, prediction: 0.0012 },
  { name: 'May', value: 0.0031, prediction: 0.0023 },
  { name: '<PERSON>', value: 0.0022, prediction: 0.003 },
  { name: 'Jul', value: 0.0026, prediction: 0.0035 },
];

export default function TokenPerformanceChart({ className }: any) {
  return (
    <div className={`w-full rounded-2xl bg-white px-5 shadow-md ${className}`}>
      {/* Header */}
      <div className="mb-4 flex flex-col items-start justify-between md:flex-row md:items-center">
        <div className="flex items-center gap-2 text-lg font-semibold text-gray-800">
          <span className="h-5 w-5 rounded-full bg-[url('/assets/images/token.svg')] bg-cover" />
          Andy Haskin Token Performance
        </div>
        <div className="mt-4 flex items-center gap-4 md:mt-0">
          <div className="flex items-center gap-1 font-medium text-gray-700">
            <FaCoins className="text-gray-500" />
            Token Price{' '}
            <span className="font-semibold text-orange-500">$0.023</span>
          </div>

          <select
            className="cursor-pointer rounded-md border border-gray-300 px-10 py-1 text-sm text-gray-700 focus:outline-none"
            defaultValue="Year"
          >
            <option value="Week">Week</option>
            <option value="Month">Month</option>
            <option value="Year">Year</option>
          </select>
        </div>
      </div>

      {/* Chart */}
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#f97316" stopOpacity={0.2} />
              <stop offset="95%" stopColor="#f97316" stopOpacity={0} />
            </linearGradient>
          </defs>
          <XAxis dataKey="name" tick={{ fill: '#999' }} />
          <YAxis tick={{ fill: '#999' }} />
          <Tooltip />
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <Area
            type="monotone"
            dataKey="value"
            stroke="#f97316"
            fillOpacity={1}
            fill="url(#colorValue)"
          />
          <Line
            type="monotone"
            dataKey="prediction"
            stroke="#cbd5e1"
            strokeDasharray="4 4"
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}
