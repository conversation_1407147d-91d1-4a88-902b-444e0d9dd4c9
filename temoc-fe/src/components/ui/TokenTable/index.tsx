import React from 'react';
import clsx from 'clsx';
import NoData from '../NoData';
import NoCollection from '@/components/common/Icons/NoCollection';

type Column = {
  key: string;
  label: React.ReactNode;
};

type RowData = {
  [key: string]: string | number | React.ReactNode;
};

type TokenTableProps = {
  columns: Column[];
  data: RowData[];
  bg?: any;
  className?: string;
  headClass?: string;
  bodyClass?: string;
  isPlaying?: boolean;
  currentTrackUrl?: string | null;
};

const TokenTable: React.FC<TokenTableProps> = ({
  columns,
  data,
  bg,
  className,
  headClass,
  bodyClass,
  isPlaying,
  currentTrackUrl,
}) => {
  return (
    <div
      className={`overflow-x-auto ${className} rounded-xl ${bg ? 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]' : ''} `}
    >
      <table className="min-w-full text-left text-sm">
        <thead className="">
          <tr className="">
            {columns.map((col) => (
              <th
                key={col.key}
                className={`border border-b border-l-0 border-r-0 border-t-0 px-6 py-3 text-base font-normal text-[#333333] ${headClass}`}
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody
          className={`${bg ? 'shadow-[0_4px_10px_rgba(0,0,0,0.08)]' : ''}`}
        >
          {data.length === 0 ? (
            <tr>
              <td colSpan={columns.length}>
                <NoData
                  icon={<NoCollection />}
                  heading="No songs found"
                  description="You haven’t added any songs yet. Upload your first track to share your music with the world and start building your collection."
                />
              </td>
            </tr>
          ) : (
            data.map((row, idx) => (
              <tr
                key={idx}
                className={clsx(
                  'px-5 hover:bg-gray-200',
                  currentTrackUrl === row?.trackUrl &&
                    (isPlaying ? 'bg-gray-200' : 'bg-gray-50'),
                )}
                // className={`hover:bg-gray-200 ${isPlaying && currentTrackUrl === row?.trackUrl && 'bg-gray-200'}`}
              >
                {columns.map((col) => {
                  const value = row[col.key];
                  return (
                    <td
                      key={col.key}
                      className={clsx(
                        'whitespace-nowrap px-5 py-3 text-base text-black-300',
                        col.key === 'type' &&
                          value === 'Buy' &&
                          'text-green-500',
                        col.key === 'type' &&
                          value === 'Sell' &&
                          'text-red-500',
                        bodyClass,
                      )}
                    >
                      {value}
                    </td>
                  );
                })}
              </tr>
            ))
          )}
        </tbody>
      </table>
    </div>
  );
};

export default TokenTable;
