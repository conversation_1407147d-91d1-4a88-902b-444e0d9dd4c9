import React from 'react';
import Insta from '@/components/common/Icons/Insta';
import X from '@/components/common/Icons/X';
import TikTok from '@/components/common/Icons/TikTok';
import Youtube from '@/components/common/Icons/Youtube';
import Facebook from '@/components/common/Icons/Facebook';
import { useQuery } from '@tanstack/react-query';
import { ArtistSocialRes } from '@/types/user.interface';
import { userService } from '@/services/user.service';
import Link from 'next/link';

const SocialMedia = () => {
  const { data: socialLinks } = useQuery<ArtistSocialRes>({
    queryKey: ['artist-links'],
    queryFn: () => userService.getArtistSocialLinks(),
  });

  const SocialLinks = socialLinks?.data?.socialLinks;
  const showIcons = (item: any) => {
    return item == 'instagram' ? (
      <Insta width={'14px'} height={'14px'} />
    ) : item == 'twitter' ? (
      <X width={'14px'} height={'14px'} />
    ) : item == 'tiktok' ? (
      <TikTok width={'14px'} height={'14px'} />
    ) : item == 'youtube' ? (
      <Youtube width={'14px'} height={'14px'} />
    ) : (
      <Facebook width={'14px'} height={'14px'} />
    );
  };
  return (
    <div>
      <div className="mt-8 space-y-2">
        {SocialLinks &&
          SocialLinks.map((item: any, i: any) => (
            <div key={i} className="flex items-center justify-between">
              <p className="flex items-center gap-2 capitalize text-[#181818]">
                {showIcons(item?.platform)}
                {item?.platform}
              </p>
              <Link
                target="
                  _blank"
                href={item.url}
                className="w-[70%] cursor-pointer truncate text-end text-blue-600"
              >
                {item.url}
              </Link>
            </div>
          ))}
      </div>
    </div>
  );
};

export default SocialMedia;
