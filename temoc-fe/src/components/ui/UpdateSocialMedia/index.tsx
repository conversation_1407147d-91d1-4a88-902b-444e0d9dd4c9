import Facebook from '@/components/common/Icons/Facebook';
import Insta from '@/components/common/Icons/Insta';
import TikTok from '@/components/common/Icons/TikTok';
import X from '@/components/common/Icons/X';
import Youtube from '@/components/common/Icons/Youtube';
import React, { useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import SocialLinks from '../SocialLinks';
import { Button } from '@/components/common';
import { MdOutlineEdit } from 'react-icons/md';
const UpdateSocialMedia = ({ ArtistData, mode }: any) => {
  const [showModal, setShowModal] = useState(false);

  const showIcons = (item: any) => {
    return item == 'instagram' ? (
      <Insta width={'14px'} height={'14px'} />
    ) : item == 'twitter' ? (
      <div className="text-white">
        <X width={'14px'} height={'14px'} />
      </div>
    ) : item == 'tiktok' ? (
      <TikTok width={'14px'} height={'14px'} />
    ) : item == 'youtube' ? (
      <Youtube width={'14px'} height={'14px'} />
    ) : (
      <Facebook width={'14px'} height={'14px'} />
    );
  };

  return (
    <>
      {(ArtistData?.socialLinks?.length ?? 0) > 0 ? (
        <div className="mt-2 flex items-center justify-center gap-1.5">
          {ArtistData?.socialLinks?.map((link: any, index: number) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="h-[14px] w-[14px]"
              title={link.platform}
            >
              {showIcons(link.platform.toLowerCase())}
            </a>
          ))}
          {mode ? (
            ''
          ) : (
            <button
              onClick={() => setShowModal(true)}
              className="flex h-[14px] w-[14px] items-center justify-center rounded-sm bg-gray-100 hover:bg-gray-200"
              title="Edit Social Links"
            >
              <MdOutlineEdit size={10} className="text-primary" />
            </button>
          )}
        </div>
      ) : (
        <div
          onClick={() => setShowModal(true)}
          className="flex cursor-pointer items-center justify-center gap-x-2"
        >
          <FaPlus className="text-xs text-[#666666]" />
          <p className="text-white">Add Socials</p>
        </div>
      )}

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 px-3">
          <div className="w-full max-w-[574px] rounded-[20px] bg-white p-5 shadow-lg sm:p-7">
            <SocialLinks />
            <div className="mt-[30px] flex justify-center">
              {/* <Button onClick={handleSaveAndClose} className="h-[62px]">SAVE</Button> */}
              <Button
                onClick={() => setShowModal(false)}
                variant="outline"
                className="w-[250px]"
              >
                CANCEL
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default UpdateSocialMedia;
