import ImageComponent from '@/components/common/ImageComponent';
import React from 'react';

interface cardProps {
  src?: string;
  songName?: string;
  songType?: string;
}

const VideoCard: React.FC<cardProps> = ({ src, songName, songType }) => {
  return (
    <div className="rounded bg-white p-3 shadow-[0_5px_20px_rgba(0,0,0,0.1)]">
      <ImageComponent src={src} height={128} width={128} />
      <p className="mt-3 text-xs font-normal">{songName}</p>
      <p className="mt-1 text-xs font-normal">{songType}</p>
    </div>
  );
};

export default VideoCard;
