import React, { createContext, useState, useCallback } from 'react';
// import * as jwt from 'jsonwebtoken';
import { authService } from '@/services/auth.service';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Cookies from 'js-cookie';
import { User } from '@/types/user.interface';
import { useRouter } from 'next/navigation';
interface AuthContextProps {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  // login: (token: string) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);
const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [token, setToken] = useState<string | null>(null);

  const queryClient = useQueryClient();
  const router = useRouter();

  let dynamic_authentication_token: string | null = null;
  if (typeof window !== 'undefined') {
    dynamic_authentication_token = localStorage.getItem(
      'dynamic_authentication_token',
    );
  }

  const { data } = useQuery({
    queryKey: ['auth-user'],
    queryFn: authService.getUser,
    enabled: !!dynamic_authentication_token,
    staleTime: 1 * 1000, // 1 second - very fast updates for immediate header response
    refetchInterval: 60 * 1000, // Refetch every minute to catch role changes
    refetchOnMount: true, // Always refetch on mount for fresh data
    refetchOnWindowFocus: false,
  });

  const user = data?.data || [];
  const clearAuthData = useCallback(() => {
    localStorage.removeItem('dynamic_authentication_token');
    Cookies.remove('dynamic_authentication_token');
    queryClient.removeQueries({
      queryKey: ['auth-user'],
    });

    setToken(null);
    setIsAuthenticated(false);
  }, [queryClient]);

  // useEffect(() => {
  //   const savedToken =
  //     localStorage.getItem('dynamic_authentication_token') ||
  //     Cookies.get('dynamic_authentication_token');
  //   if (savedToken && isValidToken(savedToken)) {
  //     const decodedUser = jwt.decode(savedToken);
  //     queryClient.setQueryData(['auth-user'], decodedUser);
  //     setToken(savedToken);
  //     setIsAuthenticated(true);
  //   }
  // }, [queryClient, data]);

  // const isValidToken = (token: string): boolean => {
  //   try {
  //     const decoded: any = jwt.decode(token);
  //     return decoded && decoded.exp * 1000 > Date.now();
  //   } catch (error) {
  //     console.error('Invalid token', error);
  //     return false;
  //   }
  // };

  // const login = useCallback(
  //   (token: string) => {
  //     queryClient.refetchQueries({
  //       queryKey: ['dynamic_authentication_token'],
  //     });
  //     setToken(token);
  //     setIsAuthenticated(true);
  //   },
  //   [queryClient],
  // );

  const logout = useCallback(() => {
    localStorage.removeItem('dynamic_authentication_token');
    Cookies.remove('dynamic_authentication_token');
    setToken(null);
    setIsAuthenticated(false);
    setTimeout(() => {
      router.replace('/');
    }, 0);
    clearAuthData();
  }, [clearAuthData, router]);

  return (
    <AuthContext.Provider value={{ user, token, isAuthenticated, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export { AuthProvider, AuthContext };
