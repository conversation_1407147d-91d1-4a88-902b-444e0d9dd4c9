'use client';
import { Default<PERSON>hain } from '@/utils/chains';
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Chain } from 'viem';

// interface Chain {
//     id: (typeof config)["chains"][number]["id"];
//     name: string;
// }

interface ChainContextType {
  activeChain: Chain;
  setActiveChain: (chain: Chain) => void;
}

const ChainContext = createContext<ChainContextType | undefined>(undefined);

export const ChainProvider = ({ children }: { children: ReactNode }) => {
  const [activeChain, setActiveChain] = useState<Chain>(DefaultChain);

  return (
    <ChainContext.Provider value={{ activeChain, setActiveChain }}>
      {children}
    </ChainContext.Provider>
  );
};

export const useActiveChain = () => {
  const context = useContext(ChainContext);
  if (!context) {
    throw new Error('useChain must be used within a ChainProvider');
  }
  return context;
};
