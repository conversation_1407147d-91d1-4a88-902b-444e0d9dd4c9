import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { socialApi } from '@/services/social';
import { useState } from 'react';

export interface UseFollowSyncProps {
  userId: string;
  displayName?: string;
  initialFollowStatus?: boolean;
}

export function useFollowSync({
  userId,
  displayName,
  initialFollowStatus,
}: UseFollowSyncProps) {
  const queryClient = useQueryClient();
  const [loadingAction, setLoadingAction] = useState<
    'following' | 'unfollowing' | null
  >(null);

  // Get real-time follow status
  const { data: followStatus, isLoading: isCheckingFollow } = useQuery({
    queryKey: ['social', 'is-following', userId],
    queryFn: async () => {
      console.log(
        '🔄 [useFollowSync] Checking follow status for user:',
        userId,
      );
      const result = await socialApi.isFollowing(userId);
      console.log('🔄 [useFollowSync] Follow status result:', result);
      return result;
    },
    enabled: !!userId && initialFollowStatus === undefined, // Only fetch if no initial status provided
    staleTime: 30000, // Consider data fresh for 30 seconds
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    initialData:
      initialFollowStatus !== undefined
        ? { isFollowing: initialFollowStatus }
        : undefined,
  });

  // Get follow stats
  const { data: followStats } = useQuery({
    queryKey: ['social', 'stats', userId],
    queryFn: () => socialApi.getFollowStats(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes - prevent excessive refetching
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const isFollowing = followStatus?.isFollowing || false;

  // Follow mutation
  const followMutation = useMutation({
    mutationFn: async (targetUserId: string) => {
      console.log(
        '🔄 [useFollowSync] Starting follow mutation for:',
        targetUserId,
      );
      try {
        const result = await socialApi.followUser(targetUserId);
        console.log('🔄 [useFollowSync] Follow API response:', result);
        return result;
      } catch (error) {
        console.error('🔄 [useFollowSync] Follow API error:', error);
        throw error;
      }
    },
    onMutate: async (targetUserId: string) => {
      // Set loading action
      setLoadingAction('following');

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ['social', 'is-following', targetUserId],
      });

      // Snapshot the previous value
      const previousFollowStatus = queryClient.getQueryData([
        'social',
        'is-following',
        targetUserId,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(['social', 'is-following', targetUserId], {
        isFollowing: true,
      });

      return { previousFollowStatus };
    },
    onSuccess: (data, targetUserId) => {
      console.log(
        '🔄 [useFollowSync] Follow success, invalidating queries for:',
        targetUserId,
      );
      setLoadingAction(null);
      toast.success(`You are now following ${displayName || 'this user'}!`);

      // Set the query data directly to ensure it stays true
      queryClient.setQueryData(['social', 'is-following', targetUserId], {
        isFollowing: true,
      });

      // Update discovery cache directly instead of invalidating
      queryClient.setQueriesData(
        { queryKey: ['discovery'] },
        (oldData: any) => {
          if (!oldData?.artists) return oldData;

          return {
            ...oldData,
            artists: oldData.artists.map((artist: any) =>
              artist._id === targetUserId
                ? { ...artist, isFollowing: true }
                : artist,
            ),
          };
        },
      );

      // Only invalidate stats and profile
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['profile'] });
        queryClient.invalidateQueries({
          queryKey: ['social', 'stats', targetUserId],
        });
      }, 500);
    },
    onError: (error: any, targetUserId: string, context: any) => {
      console.error('🔄 [useFollowSync] Follow error:', error);
      setLoadingAction(null);
      // Rollback optimistic update
      if (context?.previousFollowStatus) {
        queryClient.setQueryData(
          ['social', 'is-following', targetUserId],
          context.previousFollowStatus,
        );
      }
      toast.error(error?.response?.data?.message || 'Failed to follow user');
    },
    onSettled: (data, error, targetUserId) => {
      // Only invalidate on error to refetch correct state
      if (error) {
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ['social', 'is-following', targetUserId],
          });
        }, 100);
      }
    },
  });

  // Unfollow mutation
  const unfollowMutation = useMutation({
    mutationFn: async (targetUserId: string) => {
      console.log(
        '🔄 [useFollowSync] Starting unfollow mutation for:',
        targetUserId,
      );
      try {
        const result = await socialApi.unfollowUser(targetUserId);
        console.log('🔄 [useFollowSync] Unfollow API response:', result);
        return result;
      } catch (error) {
        console.error('🔄 [useFollowSync] Unfollow API error:', error);
        throw error;
      }
    },
    onMutate: async (targetUserId: string) => {
      // Set loading action
      setLoadingAction('unfollowing');

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ['social', 'is-following', targetUserId],
      });

      // Snapshot the previous value
      const previousFollowStatus = queryClient.getQueryData([
        'social',
        'is-following',
        targetUserId,
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(['social', 'is-following', targetUserId], {
        isFollowing: false,
      });

      return { previousFollowStatus };
    },
    onSuccess: (data, targetUserId) => {
      console.log(
        '🔄 [useFollowSync] Unfollow success, invalidating queries for:',
        targetUserId,
      );
      setLoadingAction(null);
      toast.success(`You have unfollowed ${displayName || 'this user'}`);

      // Set the query data directly to ensure it stays false
      queryClient.setQueryData(['social', 'is-following', targetUserId], {
        isFollowing: false,
      });

      // Update discovery cache directly instead of invalidating
      queryClient.setQueriesData(
        { queryKey: ['discovery'] },
        (oldData: any) => {
          if (!oldData?.artists) return oldData;

          return {
            ...oldData,
            artists: oldData.artists.map((artist: any) =>
              artist._id === targetUserId
                ? { ...artist, isFollowing: false }
                : artist,
            ),
          };
        },
      );

      // Only invalidate stats and profile
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['profile'] });
        queryClient.invalidateQueries({
          queryKey: ['social', 'stats', targetUserId],
        });
      }, 500);
    },
    onError: (error: any, targetUserId: string, context: any) => {
      console.error('🔄 [useFollowSync] Unfollow error:', error);
      setLoadingAction(null);
      // Rollback optimistic update
      if (context?.previousFollowStatus) {
        queryClient.setQueryData(
          ['social', 'is-following', targetUserId],
          context.previousFollowStatus,
        );
      }
      toast.error(error?.response?.data?.message || 'Failed to unfollow user');
    },
    onSettled: (data, error, targetUserId) => {
      // Only invalidate on error to refetch correct state
      if (error) {
        setTimeout(() => {
          queryClient.invalidateQueries({
            queryKey: ['social', 'is-following', targetUserId],
          });
        }, 100);
      }
    },
  });

  const handleFollowToggle = () => {
    console.log(
      '🔄 [useFollowSync] Follow toggle clicked - isFollowing:',
      isFollowing,
      'userId:',
      userId,
    );
    if (!userId) {
      console.log('🔄 [useFollowSync] No userId provided, returning');
      return;
    }

    if (isFollowing) {
      console.log('🔄 [useFollowSync] Triggering unfollow mutation');
      unfollowMutation.mutate(userId);
    } else {
      console.log('🔄 [useFollowSync] Triggering follow mutation');
      followMutation.mutate(userId);
    }
  };

  const isLoading =
    followMutation.isPending || unfollowMutation.isPending || isCheckingFollow;

  return {
    isFollowing,
    isLoading,
    followStats,
    handleFollowToggle,
    followMutation,
    unfollowMutation,
    loadingAction,
  };
}
