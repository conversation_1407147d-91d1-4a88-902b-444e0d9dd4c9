import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

// Define the expected API response structure
interface PriceData {
  quote: {
    USD: {
      price: number;
    };
  };
}

interface PriceApiResponse {
  data: {
    [symbol: string]: PriceData;
  };
}

const fetchPrice = async (symbol = 'RFC'): Promise<PriceData | undefined> => {
  const response = await axios.get<PriceApiResponse>('/api/rfc-price', {
    params: {
      symbol,
    },
  });

  return response?.data?.data[symbol];
};

export const usePrice = (symbol?: string) => {
  return useQuery({
    queryKey: ['rfcPrice', symbol],
    queryFn: () => fetchPrice(symbol),
    staleTime: 60000, // 1 minute
  });
};

// 'rfcPrice', fetchRFCPrice, {
//     staleTime: 60000, // 1 minute
//     cacheTime: 300000, // 5 minutes
//   }
