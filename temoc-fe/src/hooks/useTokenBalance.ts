import { useEffect, useState } from 'react';
import { Contract, ethers, formatUnits } from 'ethers';
import { erc20Abi } from 'viem';

export const useTokenBalance = (
  signer: ethers.JsonRpcSigner | undefined,
  walletAddress: string | undefined,
  tokenAddress: string | undefined,
  minTokenRequired: number = 1,
  deps: any[] = [],
) => {
  const [balance, setBalance] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);

  const checkTokenBalance = async () => {
    if (!signer || !walletAddress || !tokenAddress) return;

    setLoading(true);
    try {
      const contract = new Contract(tokenAddress, erc20Abi, signer);

      const [rawBalance, decimals] = await Promise.all([
        contract.balanceOf(walletAddress),
        contract.decimals?.() ?? 18, // fallback to 18 if decimals method not defined
      ]);

      const formatted = parseFloat(formatUnits(rawBalance, decimals));
      setBalance(formatted);
      setHasAccess(formatted >= minTokenRequired);
    } catch (error) {
      console.error('Error checking dynamic token balance:', error);
      setBalance(0);
      setHasAccess(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkTokenBalance();
  }, [signer, walletAddress, tokenAddress, ...deps]);

  return { balance, loading, hasAccess, checkTokenBalance };
};
