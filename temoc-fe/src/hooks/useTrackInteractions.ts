import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { trackInteractionService, TrackStats } from '@/services/track-interaction.service';
import { toast } from 'react-toastify';

interface UseTrackInteractionsProps {
  trackId: string;
  userId?: string;
}

export const useTrackInteractions = ({ trackId, userId }: UseTrackInteractionsProps) => {
  const queryClient = useQueryClient();
  const [optimisticStats, setOptimisticStats] = useState<Partial<TrackStats>>({});

  // Get track stats
  const { data: trackStats, isLoading } = useQuery({
    queryKey: ['track-stats', trackId],
    queryFn: () => trackInteractionService.getTrackStats(trackId, userId),
    enabled: !!trackId,
  });

  // Merge optimistic updates with server data
  const currentStats = {
    ...trackStats,
    ...optimisticStats,
  };

  // Like mutation
  const likeMutation = useMutation({
    mutationFn: () => trackInteractionService.toggleLike(trackId),
    onMutate: async () => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['track-stats', trackId] });

      // Snapshot the previous value
      const previousStats = queryClient.getQueryData(['track-stats', trackId]) as TrackStats;

      // Optimistically update
      const newLiked = !currentStats.userLiked;
      const newCount = newLiked 
        ? (currentStats.likesCount || 0) + 1 
        : Math.max(0, (currentStats.likesCount || 0) - 1);

      setOptimisticStats({
        userLiked: newLiked,
        likesCount: newCount,
      });

      return { previousStats };
    },
    onError: (err, variables, context) => {
      // Revert optimistic update
      setOptimisticStats({});
      if (context?.previousStats) {
        queryClient.setQueryData(['track-stats', trackId], context.previousStats);
      }
      toast.error('Failed to update like status');
    },
    onSuccess: (data) => {
      // Clear optimistic updates and refetch
      setOptimisticStats({});
      queryClient.invalidateQueries({ queryKey: ['track-stats', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats-bulk'] });
      
      toast.success(data.liked ? 'Track liked!' : 'Track unliked');
    },
  });

  // Comment creation mutation
  const createCommentMutation = useMutation({
    mutationFn: (data: { content: string; parentComment?: string; mentions?: string[] }) =>
      trackInteractionService.createComment(trackId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['track-comments', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats-bulk'] });
      toast.success('Comment added successfully!');
    },
    onError: () => {
      toast.error('Failed to add comment');
    },
  });

  const toggleLike = useCallback(() => {
    likeMutation.mutate();
  }, [likeMutation]);

  const addComment = useCallback((content: string, parentComment?: string, mentions?: string[]) => {
    createCommentMutation.mutate({ content, parentComment, mentions });
  }, [createCommentMutation]);

  return {
    // Data
    stats: currentStats,
    isLoading,
    
    // Actions
    toggleLike,
    addComment,
    
    // Mutation states
    isLiking: likeMutation.isPending,
    isCommenting: createCommentMutation.isPending,
  };
};
