import { useState } from 'react';
import { useSendTransaction, useWriteContract } from 'wagmi';
import { erc20Abi, parseEther, parseUnits } from 'viem';
import { SendTransactionData } from 'wagmi/query';

interface UseTransferTokenProps {
  tokenAddress?: `0x${string}`; // undefined for ETH
  recipient: `0x${string}`;
  amount: string; // human readable (e.g. '0.01')
  decimals?: number; // default to 18 if not provided
}

export const useTransferToken = () => {
  const [status, setStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');

  const { sendTransactionAsync } = useSendTransaction();
  const { writeContractAsync } = useWriteContract();

  const transfer = async ({
    tokenAddress,
    recipient,
    amount,
    decimals = 18,
  }: UseTransferTokenProps) => {
    try {
      setStatus('loading');
      const isEth = !tokenAddress;

      let hash: SendTransactionData;
      if (isEth) {
        hash = await sendTransactionAsync({
          to: recipient,
          value: parseEther(amount),
        });
      } else {
        hash = await writeContractAsync({
          address: tokenAddress,
          abi: erc20Abi,
          functionName: 'transfer',
          args: [recipient, parseUnits(amount, decimals)],
        });
      }

      setStatus('success');
      return hash;
    } catch (error: any) {
      console.error('Transfer failed:', error);
      setStatus('error');
      throw new Error(error);
    }
  };

  return {
    transfer,
    status,
    isLoading: status === 'loading',
    isSuccess: status === 'success',
    isError: status === 'error',
  };
};
