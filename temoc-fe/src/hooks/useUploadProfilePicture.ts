import { userService } from '@/services/user.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-toastify';

export const useUploadProfilePicture = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (file: File) => userService.uploadProfilePicture(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth-user'] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });
};

export const useUploadCoverPicture = () => {
  return useMutation({
    mutationFn: (file: File) => userService.uploadCoverPicture(file),
    onSuccess: () => {},
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });
};

export const useUploadArtistProfile = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (file: File) => userService.updateArtistProfile(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['auth-user'] });
    },
    onError: (error: any) => {
      toast.error(error.response.data.message);
    },
  });
};

export const useUploadArtistCover = () => {
  // const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (file: File) => userService.updateArtistCover(file),
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: ['artist-profile'] });
    },
    onError: (error: any) => {
      toast.error(error);
    },
  });
};
