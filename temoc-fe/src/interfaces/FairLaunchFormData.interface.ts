export interface FairLaunchFormData {
  // Token Info
  tokenName?: string;
  tokenSymbol?: string;
  tokenAddress?: string;
  tokenDecimals?: number;
  totalSupply?: string;

  // Presale Details
  presaleRate?: string;
  chainId: number;
  softCap: number;
  hardCap: string;
  minBuy: number;
  maxBuy: number;
  currency?: 'eth' | 'usdt' | 'usdc';
  presaleEnd: Date;
  presaleStart: Date; // ISO string
  fairLaunchAmount: number; // Nullable for optional field

  // Listing Details
  listingRate: number;
  listingOn: string;
  autoListing?: boolean;
  liquidityPercent: number;
  listingOptions?: 'manual-listing' | 'auto-listing';
  liquidityLockupDays?: number;
  poolType: 'presale' | 'private-sale' | 'seed-sale';
  liquidityUnlock: Date;
  startInterval?: string;
  endInterval?: string;
  unlockInterval?: number | undefined;
  dex: string;

  // Access / Verification
  whitelistAddresses?: string[];

  // Vesting
  hasVesting?: boolean;
  vestingStartTime?: string;
  vestingCliffMonths?: number;
  vestingDurationMonths?: number;

  // Status Flags (optional in form; managed by backend)
  isActive?: boolean;
  isFinalized?: boolean;
  isCancelled?: boolean;

  // Owner/Network Info
  ownerAddress?: string;
  network?: string;
  walletAddress: string;

  // Project & Media
  auditReportUrl?: string;
  projectWebsite?: string;
  telegramLink?: string;
  twitterLink?: string;
  logoUrl?: any;
  description?: string;

  affiliateProgram?: 'disable-affiliate' | 'enable-affiliate';
}
