import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const dateTimeFormat = (date: string) => {
  const currentTime = new Date(date);

  const formattedTime = currentTime.toLocaleDateString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    month: '2-digit',
    year: '2-digit',
    day: '2-digit',
    hour12: true,
  });
  return formattedTime;
};

export function formatTimestamp(timestamp: any) {
  // Auto-detect if timestamp is in seconds (10 digits)
  if (String(timestamp).length === 10) {
    timestamp *= 1000; // Convert to milliseconds
  }

  const date = new Date(timestamp);

  return date.toLocaleString('en-GB', {
    day: '2-digit',
    month: 'long',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
}
export function formatNumber(value: number, decimalPlaces: number = 4): string {
  return value % 1 === 0
    ? value.toString()
    : value.toFixed(decimalPlaces).replace(/\.?0+$/, '');
}

export const hasSufficientBalance = (
  userBalance: string | number,
  requiredAmount: number,
  setInsufficientFund: (value: boolean) => void,
): boolean => {
  const numericBalance = parseFloat(userBalance as string);

  if (isNaN(numericBalance) || numericBalance < requiredAmount) {
    setInsufficientFund(true);
    return false;
  }

  return true;
};

export const formatDisplayAmount = (amount: string | number): string => {
  const num = parseFloat(amount as string);

  // If the number is 0 or too close to 0, return empty
  if (isNaN(num) || num < 0.000001) return '';

  // Otherwise, show up to 6 decimals and trim trailing zeroes
  return num.toFixed(6).replace(/\.?0+$/, '');
};

export const getExplorerUrl = (chainId: number): string => {
  switch (chainId) {
    case 1: // Ethereum Mainnet
      return 'https://etherscan.io/tx/';
    case 8453: // Base Mainnet
      return 'https://basescan.org/tx/';
    case 84532: // Base Sepolia
      return 'https://sepolia.basescan.org/tx/';
    case 11155111: // Ethereum Sepolia
      return 'https://sepolia.etherscan.io/tx/';
    case 137: // Polygon Mainnet
      return 'https://polygonscan.com/tx/';
    case 10: // Optimism
      return 'https://optimistic.etherscan.io/tx/';
    case 42161: // Arbitrum One
      return 'https://arbiscan.io/tx/';
    default:
      return '';
  }
};
