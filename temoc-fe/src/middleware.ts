import { NextResponse, type NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

export function middleware(request: NextRequest) {
  const token = request.cookies.get('dynamic_authentication_token')?.value;
  const { pathname } = request.nextUrl;

  // If no token is found, redirect user to login page for protected routes like /kyc, /app, or /artist
  if (!token) {
    // If user is not authenticated and trying to access protected routes, redirect to login
    if (
      pathname.startsWith('/kyc') ||
      pathname.startsWith('/app') ||
      pathname.startsWith('/artist')
    ) {
      return NextResponse.redirect(new URL('/', request.url)); // Redirect to login
    }
    return NextResponse.next(); // Allow public/auth routes like /login, /signup
  }

  try {
    // Verify and decode the token
    const decodedToken = jwt.decode(token) as jwt.JwtPayload;

    // Check if the token is expired
    if (decodedToken?.exp && decodedToken.exp * 1000 < Date.now()) {
      // Token has expired, redirect to login
      return NextResponse.redirect(new URL('/', request.url));
    }

    // If user is authenticated and trying to access auth routes, redirect to explore page
    if (pathname === '/' || pathname === '/app') {
      return NextResponse.redirect(new URL('/app/explore', request.url));
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error: any) {
    // If there's an error decoding or verifying the token, redirect to login
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Allow the request to proceed to protected routes like /kyc, /app, etc.
  return NextResponse.next();
}

export const config = {
  matcher: ['/app/:path*', '/artist/:path*', '/kyc', '/:path*'], // Apply middleware to /app, /artist, /kyc, and other routes
};
