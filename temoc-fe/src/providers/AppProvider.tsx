'use client';
import React, { PropsWithChildren, useEffect, useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { EthereumWalletConnectors } from '@dynamic-labs/ethereum';
// Web3-related imports - remove if not using web3
import { WagmiProvider } from 'wagmi';
import ChainContextProvider from '@/context/ChainContextProvider';
import { supportedChains } from '@/utils/chains';
import '@rainbow-me/rainbowkit/styles.css';
import { getDefaultConfig, RainbowKitProvider } from '@rainbow-me/rainbowkit';
import { AuthProvider } from '@/context/AuthContext';
import { GoogleOAuthProvider } from '@react-oauth/google';
import * as Cookies from 'js-cookie';
import {
  DynamicContextProvider,
  getAuthToken,
  // getAuthToken,
  RemoveWallets,
} from '@dynamic-labs/sdk-react-core';
import { HttpService } from '@/services/base.service';
import { useRouter } from 'next/navigation';
// import { useRouter } from 'next/navigation';
// import { HttpService } from '@/services/base.service';
// import { userService } from '@/services/user.service';
// Web3-specific configuration - remove if using only web2
export const config = getDefaultConfig({
  appName: 'My RainbowKit App',
  projectId: 'YOUR_PROJECT_ID',
  chains: supportedChains as any,
  ssr: true,
});

const AppProvider = ({ children }: PropsWithChildren) => {
  const [client] = useState(new QueryClient());
  const router = useRouter();
  const cssOverrides = `
.button--padding-small
{
padding:.9rem 1rem !important;
font-size:16px !important;
font-weight:700 !important
}

`;
  // const locale = {
  //   en: {
  //     dyn_login: {
  //       title: {
  //         all: 'Get Started',
  //       },
  //     },
  //   },
  // };

  useEffect(() => {
    const token = Cookies.default.get('dynamic_authentication_token');
    if (token && window.location.pathname === '/') {
      router.replace('/app/explore');
      HttpService.setToken(token);
    }
  }, []);
  return (
    <>
      <DynamicContextProvider
        settings={{
          environmentId: process.env.NEXT_PUBLIC_DYNAMIC_ID!,
          walletConnectors: [EthereumWalletConnectors],
          walletsFilter: RemoveWallets(['phantomevm']),
          events: {
            onAuthSuccess: async () => {
              const authToken = getAuthToken();
              Cookies.default.set('dynamic_authentication_token', authToken!);
              HttpService.setToken(authToken!);

              // Trigger a custom event to notify components that auth is successful
              window.dispatchEvent(new CustomEvent('authSuccess'));

              // Always redirect to explore page after login - all users start as fans
              console.log('Auth success - redirecting to explore page');
              router.replace('/app/explore');
            },
            onLogout: async () => {
              localStorage.removeItem('dynamic_authentication_token');
              Cookies.default.remove('dynamic_authentication_token');
            },
          },
          cssOverrides,
        }}
        // locale={locale}
        // locale={locale}
      >
        <WagmiProvider config={config}>
          <ChainContextProvider>
            <QueryClientProvider client={client}>
              <RainbowKitProvider>
                <GoogleOAuthProvider clientId="AIzaSyDq8JvQREVgxEVFTEDk8YjotJ8OsoEvR9o">
                  <AuthProvider>{children}</AuthProvider>
                </GoogleOAuthProvider>
                <ReactQueryDevtools initialIsOpen={false} />
              </RainbowKitProvider>
            </QueryClientProvider>
          </ChainContextProvider>
        </WagmiProvider>
      </DynamicContextProvider>
    </>
  );
};

export default AppProvider;
