import axios from 'axios';

// Use localhost for testing, fallback to env variable
const baseURL = process.env.NEXT_PUBLIC_API_URL;

// Create axios instance
export const api = axios.create({
  baseURL,
  timeout: 10000,
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    // Only add auth token if we're in the browser
    if (typeof window !== 'undefined') {
      const dynamic_authentication_token = localStorage.getItem(
        'dynamic_authentication_token',
      );
      const token = dynamic_authentication_token?.replace(/^"|"$/g, '');
      if (token) {
        config.headers = config.headers || {};
        config.headers['Authorization'] = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // Handle unauthorized access
      if (typeof window !== 'undefined') {
        localStorage.removeItem('dynamic_authentication_token');
      }
      // Optionally redirect to login
    }
    return Promise.reject(error);
  },
);
