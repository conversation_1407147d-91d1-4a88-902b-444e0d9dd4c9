import axios from 'axios';
const baseURL = process.env.NEXT_PUBLIC_API_URL;

export class HttpService {
  private abortController: AbortController;

  constructor() {
    this.abortController = new AbortController();

    // Add a response interceptor
    axios.interceptors.response.use(
      (response) => response, // Pass through successful responses
      (error: any) => {
        if (error.response && error.response.status === 401) {
          // User is unauthorized, trigger logout
          this.logout();
        }
        return Promise.reject(error); // Handle other errors normally
      },
    );

    axios.interceptors.request.use(
      (config) => {
        const dynamic_authentication_token = localStorage.getItem(
          'dynamic_authentication_token',
        );
        const token = dynamic_authentication_token?.replace(/^"|"$/g, ''); // Adjusted regex
        if (token) {
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
          if (config.headers) {
            config.headers['Authorization'] = `Bearer ${token}`;
          }
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  /**
   * Set Token On Header
   * @param token
   */
  static setToken(token: string): void {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Fetch data from server
   * @param url Endpoint link
   * @param params Optional query parameters
   * @return Promise
   */
  protected get = (url: string, params?: any) =>
    axios.get(`${baseURL}/${url}`, {
      params,
    });

  /**
   * Write data to server
   * @param url Endpoint link
   * @param body Data to send over server
   * @return Promise
   */
  protected post = (url: string, body: any, options = {}) =>
    axios.post(`${baseURL}/${url}`, body, {
      ...options,
    });

  /**
   * Delete data from server
   * @param url Endpoint link
   * @param params Optional query parameters
   * @return Promise
   */
  protected delete = (url: string, params?: any) =>
    axios.delete(`${baseURL}/${url}`, {
      params,
    });

  /**
   * Update data on server via PUT
   * @param url Endpoint link
   * @param body Optional body data
   * @param params Optional query parameters
   * @return Promise
   */
  protected put = (url: string, body?: any, params?: any) => {
    return axios.put(`${baseURL}/${url}`, body, {
      ...params,
    });
  };

  /**
   * Partially update data on server via PATCH
   * @param url Endpoint link
   * @param body Optional body data
   * @param params Optional query parameters
   * @return Promise
   */
  protected patch = (url: string, body?: any, params?: any) => {
    return axios.patch(`${baseURL}/${url}`, body, {
      ...params,
    });
  };

  /**
   * Cancel ongoing HTTP requests
   */
  public updateAbortController() {
    this.abortController = new AbortController();
  }

  cancel = () => {
    this.abortController.abort();
    this.updateAbortController();
  };

  /**
   * Logout the user when unauthorized
   */
  private logout(): void {
    // Clear the token from localStorage/sessionStorage
    localStorage.removeItem('dynamic_authentication_token');

    // Remove token from cookies
    this.removeCookie('dynamic_authentication_token');

    // Redirect to login or home page (adjust as per your routing logic)
    // window.location.href = '/login';
  }

  /**
   * Helper function to remove a cookie by name
   * @param name Name of the cookie to remove
   */
  private removeCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }
}
