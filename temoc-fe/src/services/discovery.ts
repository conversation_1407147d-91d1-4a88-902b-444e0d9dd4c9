import { api } from './api';

export interface Artist {
  _id: string;
  username: string;
  displayName: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  coverPicture?: string;
  location?: string;
  lastActiveAt: string;
  artistProfile: {
    genre?: string;
    isVerified: boolean;
    bio?: string;
    coverPhoto?: string;
    profilePic?: string;
  };
  followersCount: number;
  hasToken: boolean;
  tokenCount: number;
  tokens: {
    name: string;
    symbol: string;
    address: string;
  }[];
  isFollowing?: boolean;
}

export interface DiscoveryFilters {
  genre?: string[];
  isVerified?: boolean;
  hasToken?: boolean;
  location?: string;
  search?: string;
  sortBy?: 'popular' | 'newest' | 'alphabetical' | 'tokenPrice' | 'mostActive';
  sortOrder?: 'asc' | 'desc';
}

export interface DiscoveryResponse {
  artists: Artist[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface TrendingArtist {
  _id: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  coverPicture?: string;
  artistProfile: {
    genre?: string;
    isVerified: boolean;
    coverPhoto?: string;
    profilePic?: string;
  };
  recentFollowersCount: number;
  activityScore: number;
}

export interface PopularArtist {
  _id: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  coverPicture?: string;
  artistProfile: {
    genre?: string;
    isVerified: boolean;
    coverPhoto?: string;
    profilePic?: string;
  };
  followersCount: number;
}

export interface RecommendedArtist {
  _id: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  coverPicture?: string;
  artistProfile: {
    genre?: string;
    isVerified: boolean;
    coverPhoto?: string;
    profilePic?: string;
  };
  mutualConnections: number;
}

export const discoveryApi = {
  // Main discovery endpoint
  discoverArtists: async (
    filters: DiscoveryFilters = {},
    page: number = 1,
    limit: number = 20,
  ): Promise<DiscoveryResponse> => {
    const params = new URLSearchParams();

    params.append('page', page.toString());
    params.append('limit', limit.toString());

    if (filters.genre && filters.genre.length > 0) {
      filters.genre.forEach((g) => params.append('genre', g));
    }

    if (filters.isVerified !== undefined) {
      params.append('isVerified', filters.isVerified.toString());
    }

    if (filters.hasToken !== undefined) {
      params.append('hasToken', filters.hasToken.toString());
    }

    if (filters.location) {
      params.append('location', filters.location);
    }

    if (filters.search) {
      params.append('search', filters.search);
    }

    if (filters.sortBy) {
      params.append('sortBy', filters.sortBy);
    }

    if (filters.sortOrder) {
      params.append('sortOrder', filters.sortOrder);
    }

    const response = await api.get(`/discovery/artists?${params.toString()}`);
    return response.data as DiscoveryResponse;
  },

  // Search artists
  searchArtists: async (
    query: string,
    filters: Partial<DiscoveryFilters> = {},
    page: number = 1,
    limit: number = 20,
  ): Promise<DiscoveryResponse> => {
    const params = new URLSearchParams();

    params.append('q', query);
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    if (filters.genre && filters.genre.length > 0) {
      filters.genre.forEach((g) => params.append('genre', g));
    }

    if (filters.isVerified !== undefined) {
      params.append('isVerified', filters.isVerified.toString());
    }

    const response = await api.get(`/discovery/search?${params.toString()}`);
    return response.data as DiscoveryResponse;
  },

  // Get trending artists
  getTrendingArtists: async (limit: number = 10): Promise<TrendingArtist[]> => {
    const response = await api.get(`/discovery/trending?limit=${limit}`);
    return response.data as TrendingArtist[];
  },

  // Get newly verified artists
  getNewVerifiedArtists: async (limit: number = 10): Promise<Artist[]> => {
    const response = await api.get(`/discovery/new-verified?limit=${limit}`);
    return response.data as Artist[];
  },

  // Get popular artists
  getPopularArtists: async (limit: number = 10): Promise<PopularArtist[]> => {
    const response = await api.get(`/discovery/popular?limit=${limit}`);
    return response.data as PopularArtist[];
  },

  // Get recommended artists (requires authentication)
  getRecommendedArtists: async (
    limit: number = 10,
  ): Promise<RecommendedArtist[]> => {
    const response = await api.get(`/discovery/recommended?limit=${limit}`);
    return response.data as RecommendedArtist[];
  },

  // Get available genres
  getGenres: async (): Promise<{ genres: string[] }> => {
    const response = await api.get('/discovery/genres');
    return response.data as { genres: string[] };
  },
};
