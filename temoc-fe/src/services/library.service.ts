import { HttpService } from './base.service';

class LibraryService extends HttpService {
  private readonly prefix: string = 'content';

  /**
   * Sign in
   * @param data
   */
  createLibraries = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/libraries`, data);
  };

  /**
   * Sign in
   * @param data
   */
  createAlbums = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/albums`, data);
  };

  /**
   * Sign in
   * @param data
   */
  createTracks = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/tracks`, data);
  };

  /**
   * Sign in
   * @param data
   */
  updateAlbumAccessControl = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/update-access`, data);
  };

  /**
   * Sign in
   * @param data
   */
  getAllLibraries = (): Promise<any> => {
    return this.get(`${this.prefix}/libraries`);
  };

  /**
   * Sign in
   * @param data
   */
  getSingleLibrarie = (id: string): Promise<any> => {
    return this.get(`${this.prefix}/libraries/${id}`);
  };

  /**
   * Sign in
   * @param data
   */
  getAlbumsByCreator = (id: string): Promise<any> => {
    return this.get(`${this.prefix}/creator/${id}`);
  };

  /**
   * Sign in
   * @param data
   */
  getTracks = (id: string): Promise<any> => {
    return this.get(`${this.prefix}/albums/${id}/tracks`);
  };

  /**
   * Get all albums by library ID
   * @param libraryId
   */
  getAlbumsByLibraryId = (libraryId: string): Promise<any> => {
    return this.get(`${this.prefix}/libraries/${libraryId}/albums`);
  };

  /**
   * Update album by ID
   * @param id
   * @param data
   */
  updateAlbum = (id: string, data: any): Promise<any> => {
    return this.put(`${this.prefix}/albums/${id}`, data);
  };

  /**
   * Update album by ID
   * @param id
   * @param data
   */
  updateTrack = (id: string, data: any): Promise<any> => {
    console.log('Updating track with ID:', id, 'and data:', data);
    return this.put(`${this.prefix}/tracks/${id}`, data);
  };

  /**
   * Delete album by ID
   * @param id
   */
  deleteAlbum = (id: string): Promise<any> => {
    return this.delete(`${this.prefix}/albums/${id}`);
  };
  deleteTrack = (trackId: string): Promise<any> => {
    return this.delete(`${this.prefix}/tracks/${trackId}`);
  };

  /**
   * Delete Library by ID
   * @param id
   */
  deleteLibrary = (id: string): Promise<any> => {
    return this.delete(`${this.prefix}/libraries/${id}`);
  };
}

export const libraryService = new LibraryService();
