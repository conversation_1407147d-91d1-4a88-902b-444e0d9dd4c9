import { HttpService } from '@/services/base.service';
// import { IToken } from '@/types/token.interface';

class LiquidityService extends HttpService {
  private readonly prefix: string = 'liquidity';

  /**
   * Add a new social link
   * @param data
   */
  getAllLiquidity = (): Promise<any> => {
    return this.get(`${this.prefix}`);
  };
}

export const liquidityService = new LiquidityService();
