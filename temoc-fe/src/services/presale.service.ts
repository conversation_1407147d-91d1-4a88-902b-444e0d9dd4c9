import { FairLaunchFormData } from '@/interfaces/FairLaunchFormData.interface';
import { HttpService } from '@/services/base.service';

class PresaleService extends HttpService {
  private readonly prefix: string = 'presale';

  /**
   * Add a new social link
   * @param data
   */
  createPresale = (data: FairLaunchFormData): Promise<FairLaunchFormData> => {
    // @ts-ignore

    return this.post(`${this.prefix}/create`, data);
  };

  /**
   * Add a new social link
   * @param data
   */
  getPresale = (userId: string): Promise<any> => {
    console.log(userId);
    return this.get(`${this.prefix}/${userId}`);
  };
}

export const presaleService = new PresaleService();
