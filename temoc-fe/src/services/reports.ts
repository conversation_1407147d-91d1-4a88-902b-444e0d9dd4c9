import { api } from './api';

export enum ReportCategory {
  PROFILE = 'profile',
  CONTENT = 'content',
  BEHAVIOR = 'behavior',
  TECHNICAL = 'technical',
}

export enum ReportSubcategory {
  // Profile subcategories
  FAKE_IMPERSONATION = 'fake_impersonation',
  INAPPROPRIATE_CONTENT = 'inappropriate_content',
  HARASSMENT_ABUSE = 'harassment_abuse',
  SPAM_SCAM = 'spam_scam',

  // Content subcategories
  COPYRIGHT_INFRINGEMENT = 'copyright_infringement',
  INAPPROPRIATE_AUDIO = 'inappropriate_audio',
  QUALITY_ISSUES = 'quality_issues',
  MISLEADING_INFORMATION = 'misleading_information',

  // Behavior subcategories
  SPAM_BEHAVIOR = 'spam_behavior',
  HARASSMENT = 'harassment',
  COMMUNITY_VIOLATIONS = 'community_violations',

  // Technical subcategories
  PLATFORM_BUGS = 'platform_bugs',
  PAYMENT_TOKEN_ISSUES = 'payment_token_issues',
  ACCESSIBILITY_ISSUES = 'accessibility_issues',
}

export enum ReportStatus {
  PENDING = 'pending',
  UNDER_REVIEW = 'under_review',
  RESOLVED = 'resolved',
  DISMISSED = 'dismissed',
  ESCALATED = 'escalated',
}

export enum ReportUrgency {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface CreateReportData {
  reportedUserId?: string;
  reportedContentId?: string;
  category: ReportCategory;
  subcategory: ReportSubcategory;
  description: string;
  urgency?: ReportUrgency;
  isAnonymous?: boolean;
  metadata?: Record<string, any>;
}

export interface Report {
  _id: string;
  reporter: {
    _id: string;
    username: string;
    displayName: string;
    email: string;
  };
  reportedUser?: {
    _id: string;
    username: string;
    displayName: string;
    email: string;
  };
  reportedContent?: string;
  category: ReportCategory;
  subcategory: ReportSubcategory;
  description: string;
  evidenceUrls: string[];
  urgency: ReportUrgency;
  status: ReportStatus;
  assignedModerator?: {
    _id: string;
    username: string;
    displayName: string;
  };
  moderatorNotes?: string;
  resolutionDate?: string;
  resolutionAction?: string;
  metadata?: Record<string, any>;
  isAnonymous: boolean;
  ticketNumber: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateReportResponse {
  success: boolean;
  ticketNumber: string;
  reportId: string;
  message: string;
}

export interface ReportsResponse {
  reports: Report[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export const reportsApi = {
  // Create a new report
  createReport: async (
    reportData: CreateReportData,
    evidenceFiles?: File[],
  ): Promise<CreateReportResponse> => {
    const formData = new FormData();

    // Add report data
    Object.entries(reportData).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(
          key,
          typeof value === 'object' ? JSON.stringify(value) : value.toString(),
        );
      }
    });

    // Add evidence files
    if (evidenceFiles && evidenceFiles.length > 0) {
      evidenceFiles.forEach((file) => {
        formData.append('evidence', file);
      });
    }

    const response = await api.post('/reports', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data as CreateReportResponse;
  },

  // Get current user's reports
  getMyReports: async (
    page: number = 1,
    limit: number = 20,
  ): Promise<ReportsResponse> => {
    const response = await api.get(
      `/reports/my-reports?page=${page}&limit=${limit}`,
    );
    return response.data as ReportsResponse;
  },

  // Get report by ticket number
  getReportByTicket: async (ticketNumber: string): Promise<Report> => {
    const response = await api.get(`/reports/ticket/${ticketNumber}`);
    return response.data as Report;
  },

  // Get report by ID
  getReportById: async (reportId: string): Promise<Report> => {
    const response = await api.get(`/reports/${reportId}`);
    return response.data as Report;
  },

  // Get report categories and subcategories
  getReportCategories: () => {
    return {
      [ReportCategory.PROFILE]: [
        {
          value: ReportSubcategory.FAKE_IMPERSONATION,
          label: 'Fake Account/Impersonation',
        },
        {
          value: ReportSubcategory.INAPPROPRIATE_CONTENT,
          label: 'Inappropriate Profile Content',
        },
        {
          value: ReportSubcategory.HARASSMENT_ABUSE,
          label: 'Harassment/Abuse',
        },
        { value: ReportSubcategory.SPAM_SCAM, label: 'Spam/Scam' },
      ],
      [ReportCategory.CONTENT]: [
        {
          value: ReportSubcategory.COPYRIGHT_INFRINGEMENT,
          label: 'Copyright Infringement',
        },
        {
          value: ReportSubcategory.INAPPROPRIATE_AUDIO,
          label: 'Inappropriate Audio Content',
        },
        { value: ReportSubcategory.QUALITY_ISSUES, label: 'Quality Issues' },
        {
          value: ReportSubcategory.MISLEADING_INFORMATION,
          label: 'Misleading Information',
        },
      ],
      [ReportCategory.BEHAVIOR]: [
        { value: ReportSubcategory.SPAM_BEHAVIOR, label: 'Spam Behavior' },
        { value: ReportSubcategory.HARASSMENT, label: 'Harassment' },
        {
          value: ReportSubcategory.COMMUNITY_VIOLATIONS,
          label: 'Community Guidelines Violation',
        },
      ],
      [ReportCategory.TECHNICAL]: [
        { value: ReportSubcategory.PLATFORM_BUGS, label: 'Platform Bugs' },
        {
          value: ReportSubcategory.PAYMENT_TOKEN_ISSUES,
          label: 'Payment/Token Issues',
        },
        {
          value: ReportSubcategory.ACCESSIBILITY_ISSUES,
          label: 'Accessibility Issues',
        },
      ],
    };
  },

  // Get category labels
  getCategoryLabel: (category: ReportCategory): string => {
    const labels = {
      [ReportCategory.PROFILE]: 'Profile Issues',
      [ReportCategory.CONTENT]: 'Content Issues',
      [ReportCategory.BEHAVIOR]: 'Behavior Issues',
      [ReportCategory.TECHNICAL]: 'Technical Issues',
    };
    return labels[category];
  },

  // Get subcategory label
  getSubcategoryLabel: (subcategory: ReportSubcategory): string => {
    const categories = reportsApi.getReportCategories();
    for (const categoryItems of Object.values(categories)) {
      const item = categoryItems.find((item) => item.value === subcategory);
      if (item) return item.label;
    }
    return subcategory;
  },
};
