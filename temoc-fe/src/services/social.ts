import { api } from './api';

export interface FollowStats {
  followersCount: number;
  followingCount: number;
}

export interface User {
  _id: string;
  username: string;
  displayName: string;
  firstName: string;
  lastName: string;
  avatarUrl?: string;
  role: 'fan' | 'artist';
}

export interface FollowResponse {
  success: boolean;
  message: string;
}

export interface FollowersResponse {
  followers: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface FollowingResponse {
  following: User[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export const socialApi = {
  // Follow/Unfollow operations
  followUser: async (userId: string): Promise<FollowResponse> => {
    const response = await api.post(`/social/follow/${userId}`);
    return response.data as FollowResponse;
  },

  unfollowUser: async (userId: string): Promise<FollowResponse> => {
    const response = await api.delete(`/social/follow/${userId}`);
    return response.data as FollowResponse;
  },

  // Check follow status
  isFollowing: async (userId: string): Promise<{ isFollowing: boolean }> => {
    const response = await api.get(`/social/is-following/${userId}`);
    return response.data as { isFollowing: boolean };
  },

  // Get followers/following lists
  getFollowers: async (
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<FollowersResponse> => {
    const response = await api.get(
      `/social/followers/${userId}?page=${page}&limit=${limit}`,
    );
    return response.data as FollowersResponse;
  },

  getFollowing: async (
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<FollowingResponse> => {
    const response = await api.get(
      `/social/following/${userId}?page=${page}&limit=${limit}`,
    );
    return response.data as FollowingResponse;
  },

  // Get current user's followers/following
  getMyFollowers: async (
    page: number = 1,
    limit: number = 20,
  ): Promise<FollowersResponse> => {
    const response = await api.get(
      `/social/my-followers?page=${page}&limit=${limit}`,
    );
    return response.data as FollowersResponse;
  },

  getMyFollowing: async (
    page: number = 1,
    limit: number = 20,
  ): Promise<FollowingResponse> => {
    const response = await api.get(
      `/social/my-following?page=${page}&limit=${limit}`,
    );
    return response.data as FollowingResponse;
  },

  // Get follow statistics
  getFollowStats: async (userId: string): Promise<FollowStats> => {
    const response = await api.get(`/social/stats/${userId}`);
    return response.data as FollowStats;
  },

  getMyStats: async (): Promise<FollowStats> => {
    const response = await api.get('/social/my-stats');
    return response.data as FollowStats;
  },

  // Block/Unblock operations
  blockUser: async (userId: string): Promise<FollowResponse> => {
    const response = await api.post(`/social/block/${userId}`);
    return response.data as FollowResponse;
  },

  unblockUser: async (userId: string): Promise<FollowResponse> => {
    const response = await api.delete(`/social/block/${userId}`);
    return response.data as FollowResponse;
  },
};
