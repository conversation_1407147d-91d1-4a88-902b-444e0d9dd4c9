import { HttpService } from '@/services/base.service';

interface SocialLinkDto {
  platform: string;
  url: string;
}

class SocialLinksService extends HttpService {
  private readonly prefix: string = 'users';

  /**
   * Get all social links for the artist
   */
  getSocialLinks = (): Promise<any> => {
    return this.get(`${this.prefix}/artist/social-links`);
  };

  /**
   * Add a new social link
   * @param data
   */
  addSocialLink = (data: SocialLinkDto): Promise<any> => {
    return this.post(`${this.prefix}/artist/social-links`, data);
  };

  /**
   * Update an existing social link
   * @param index
   * @param data
   */
  updateSocialLink = (
    index: number,
    data: Partial<SocialLinkDto>,
  ): Promise<any> => {
    return this.put(`${this.prefix}/artist/social-links/${index}`, data);
  };

  /**
   * Delete a social link
   * @param index
   */
  deleteSocialLink = (index: number): Promise<any> => {
    return this.delete(`${this.prefix}/artist/social-links/${index}`);
  };
}

export const socialLinksService = new SocialLinksService();
