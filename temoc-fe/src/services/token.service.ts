import { HttpService } from '@/services/base.service';

class TokenService extends HttpService {
  private readonly prefix: string = 'token';

  /**
   * Add a new social link
   * @param data
   */
  createToken = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/create`, data);
  };

  /**
   * Add a new social link
   * @param data
   */
  getToken = (): Promise<any> => {
    return this.get(`${this.prefix}/my-tokens`);
  };

  /**
   * Add a new social link
   * @param data
   */
  getArtistToken = (userId: string): Promise<any> => {
    return this.get(`${this.prefix}/artist-tokens/${userId}`);
  };
}

export const tokenService = new TokenService();
