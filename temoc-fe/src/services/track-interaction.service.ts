import { api } from './api';

export interface TrackStats {
  likesCount: number;
  commentsCount: number;
  userLiked: boolean;
}

export interface Comment {
  _id: string;
  content: string;
  user: {
    _id: string;
    username: string;
    displayName: string;
    avatarUrl?: string;
  };
  mentions: {
    _id: string;
    username: string;
    displayName: string;
  }[];
  likes: string[];
  parentComment?: string;
  replyCount?: number;
  isEdited: boolean;
  editedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CommentsResponse {
  comments: Comment[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface CreateCommentData {
  content: string;
  parentComment?: string;
  mentions?: string[];
}

export interface UpdateCommentData {
  content: string;
  mentions?: string[];
}

export const trackInteractionService = {
  // Like functionality
  async toggleLike(trackId: string) {
    const response = await api.post(`/tracks/${trackId}/like`);
    return response.data;
  },

  async getLikeStatus(trackId: string) {
    const response = await api.get(`/tracks/${trackId}/like-status`);
    return response.data;
  },

  async getLikesCount(trackId: string) {
    const response = await api.get(`/tracks/${trackId}/likes-count`);
    return response.data;
  },

  // Comment functionality
  async createComment(trackId: string, data: CreateCommentData): Promise<Comment> {
    const response = await api.post(`/tracks/${trackId}/comments`, data);
    return response.data;
  },

  async getComments(
    trackId: string,
    page: number = 1,
    limit: number = 20,
    parentComment?: string
  ): Promise<CommentsResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    
    if (parentComment) {
      params.append('parentComment', parentComment);
    }

    const response = await api.get(`/tracks/${trackId}/comments?${params}`);
    return response.data;
  },

  async updateComment(commentId: string, data: UpdateCommentData): Promise<Comment> {
    const response = await api.put(`/tracks/comments/${commentId}`, data);
    return response.data;
  },

  async deleteComment(commentId: string) {
    const response = await api.delete(`/tracks/comments/${commentId}`);
    return response.data;
  },

  async toggleCommentLike(commentId: string) {
    const response = await api.post(`/tracks/comments/${commentId}/like`);
    return response.data;
  },

  async getTrackStats(trackId: string, userId?: string): Promise<TrackStats> {
    const params = userId ? `?userId=${userId}` : '';
    const response = await api.get(`/tracks/${trackId}/stats${params}`);
    return response.data;
  },
};
