import { SalesData } from '@/types/transaction.interface';
import { HttpService } from './base.service';

class TransactionsService extends HttpService {
  private readonly prefix: string = 'transactions';

  /**
   * Get ALL trends
   * @param params
   */
  getFanTransactions = (params: any): Promise<any> => {
    return this.get(`${this.prefix}/by-fan`, params);
  };

  /**
   * Get ALL trends
   * @param params
   */
  getArtistTransactions = (params: any): Promise<any> => {
    return this.get(`${this.prefix}/by-artist`, params);
  };

  /**
   * Get ALL trends
   * @param params
   */
  getUserAccess = (albumId: any): Promise<any> => {
    return this.get(`${this.prefix}/albumAccess/${albumId}`);
  };

  getTotalTokenSales = (): Promise<SalesData> => {
    return this.get(`${this.prefix}/total-token-sales`);
  };

  /**
   * Get total tokens for a specific user by email or address
   * @param identifier email or wallet address
   */
  getFanTotalTokens = (userId: string, presaleId: string): Promise<any> => {
    return this.get(`${this.prefix}/fan/${userId}/total-tokens`, { presaleId });
  };

  /**
   * Get total tokens for a specific user by email or address
   * @param identifier email or wallet address
   */
  getArtistTotalTokensSales = (artistId: string): Promise<any> => {
    console.log(artistId);

    return this.get(
      `${this.prefix}/artist/682c61800ef8430e14b20ffb/total-tokens`,
    );
  };

  /**
   * Create a new transaction
   * @param data - transaction payload
   */
  createTransaction = (data: any): Promise<any> => {
    return this.post(`${this.prefix}/create/transaction`, data);
  };
}
export const transactionsService = new TransactionsService();
