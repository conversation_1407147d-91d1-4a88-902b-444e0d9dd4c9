import {
  ArtistProfileRes,
  ArtistProgressRes,
  ArtistRes,
  ArtistSocialRes,
  ArtistStepOneData,
  ArtistStepsRes,
  ArtistStepTwoData,
  CurrentViewRes,
  SocialRes,
  UserRes,
} from '@/types/user.interface';
import { HttpService } from './base.service';

class UserService extends HttpService {
  private readonly prefix: string = 'users';

  becomeArtist = (): Promise<UserRes> => {
    const reason = {
      reason: 'I want to share my music with fans',
    };
    return this.post(`${this.prefix}/become-artist`, reason);
  };

  getCurrentView = (): Promise<CurrentViewRes> => {
    return this.get(`${this.prefix}/current-view`);
  };

  switchView = (): Promise<any> => {
    return this.post(`${this.prefix}/switch-view`, null);
  };

  uploadProfilePicture = (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('profilePicture', file);
    return this.post(`${this.prefix}/upload-profile-picture`, formData);
  };

  uploadCoverPicture = (file: File): Promise<any> => {
    const formData = new FormData();
    formData.append('file', file);
    return this.post(`${this.prefix}/upload-cover-photo`, formData);
  };

  getUsersProfile = (): Promise<UserRes> => {
    return this.get(`${this.prefix}/profile`);
  };

  updateUserProfile = (data: any): Promise<UserRes> => {
    return this.put(`${this.prefix}/profile`, data);
  };

  getArtistProfile = (): Promise<ArtistProfileRes> => {
    return this.get(`${this.prefix}/artist-profile`);
  };

  updateArtistProfile = (file: any): Promise<any> => {
    const formData = new FormData();
    formData.append('profilePicture', file);
    return this.put(`${this.prefix}/update-artist-profile`, formData);
  };

  updateArtistCover = (file: any): Promise<any> => {
    const formData = new FormData();
    formData.append('coverPhoto', file);
    return this.put(`${this.prefix}/update-artist-profile`, formData);
  };

  updateArtistData = (data: any): Promise<ArtistRes> => {
    return this.put(`${this.prefix}/update-artist-profile`, data);
  };

  getArtistSocialLinks = (): Promise<ArtistSocialRes> => {
    return this.get(`${this.prefix}/artist/social-links`);
  };

  addArtistSocialLink = (data: any): Promise<SocialRes> => {
    return this.post(`${this.prefix}/artist/social-links`, data);
  };

  updateArtistSocialLink = (id: string, data: any): Promise<any> => {
    return this.put(`${this.prefix}/artist/social-links/${id}`, data);
  };

  deleteArtistSocialLink = (id: string): Promise<any> => {
    return this.delete(`${this.prefix}/artist/social-links/${id}`);
  };

  getArtistApplicationProgress = (): Promise<ArtistProgressRes> => {
    return this.get(`${this.prefix}/artist-application/progress`);
  };

  addArtistStepOne = (data: ArtistStepOneData): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/step-1`, data);
  };

  addArtistStepTwo = (data: ArtistStepTwoData): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/step-2`, data);
  };

  addArtistStepThree = (data: any): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/step-3/links`, data);
  };

  getKycSocialLinks = (): Promise<ArtistSocialRes> => {
    return this.get(`${this.prefix}/artist-application/step-3/links`);
  };

  updateKycSocialLink = (id: string, data: any): Promise<any> => {
    return this.put(
      `${this.prefix}/artist-application/step-3/links/${id}`,
      data,
    );
  };

  deleteKycSocialLink = (id: string): Promise<any> => {
    return this.delete(`${this.prefix}/artist-application/step-3/links/${id}`);
  };

  completeKycStepThree = (): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/step-3/complete`, null);
  };

  addArtistStepFour = (data: any): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/step-4`, data);
  };

  submitArtistApplication = (): Promise<ArtistStepsRes> => {
    return this.post(`${this.prefix}/artist-application/submit`, null);
  };
}

export const userService = new UserService();
