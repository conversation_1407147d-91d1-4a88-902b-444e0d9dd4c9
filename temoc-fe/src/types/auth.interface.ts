export interface ILogin {
  email: string;
  password: string;
}

export interface ISingUp {
  email: string;
}

type User = {
  _id: string;
  fullName: string;
  email: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

type Data = {
  data: any;
  user: User;
  access_token: string;
};

export interface ILoginResponse {
  statusCode: number;
  message: string;
  data: Data;
}

export interface ISignUpResponse {
  access_token: string;
  user: User;
  message: string;
  statusCode: number;
  data: Data;
}

export interface IVerifyEmail {
  email: string;
  code: string;
}

export interface IResendCode {
  email: string;
}

export interface IForgot {
  email: string;
}

export interface IReset {
  email: string;
  code: number;
  password: string;
  confirmPassword: string;
}
