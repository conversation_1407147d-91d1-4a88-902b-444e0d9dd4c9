// export type Option = {
//   value?: string;
//   label?: ReactNode;
// };

export interface CreateTokenFormData {
  symbol: string;
  name: string;
  totalSupply: number;
  decimals: number;
  logoUrl?: File | null;
  // router?: Option | null;
  // yieldFee?: number;
  // liquidityFee?: number;
  // charityAddress?: string;
  // anotherFee?: number;
}

export interface IToken {
  name: string;
  symbol: string;
  address: string;
  walletAddress: string;
  decimals: number;
  totalSupply: string;
  network?: string;
  logo?: File | null;
}
