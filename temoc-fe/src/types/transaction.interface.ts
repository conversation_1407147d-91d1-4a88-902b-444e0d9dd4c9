export interface TResponseData {
  data: {
    statusCode: number;
    message: string;
    data: PaginatedData<TransactionData>;
  };
}

export interface SalesData {
  data: {
    statusCode: number;
    message: string;
    data: 0;
  };
}

export interface PaginatedData<T> {
  data: T[];
  totalCount: number;
  totalPages: number;
  page: number;
  limit: number;
}

export interface TransactionData {
  _id: string;
  txHash: string;
  contract: string;
  address: string;
  tokenAddress: string;
  type: string;
  amount: number;
  stage: number;
  tokens: number;
  timestamp: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
}
