type Wallet = {
  address: string;
  chainId: number;
  provider: 'browserExtension' | string;
};

type Metadata = {
  dynamic: {
    firstVisit: string; // ISO date string
    lastVisit: string; // ISO date string
    lists: any[]; // You can refine this if you know the structure of items in lists
  };
};

export interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  username: string;
  avatarUrl: string;
  role: string;
  wallets: Wallet[];
  dynamicUserId: string;
  displayName: string;
  ownedTokens: any[]; // Adjust type based on token structure if known
  authProvider: string;
  artistProfile: ArtistFormData;
  description: string;
  isActive: boolean;
  currentArtistFormStep: number;
  isAdmin: boolean;
  lastLogin: string; // ISO date string
  metadata: Metadata;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  __v: number;
  address: string;
  status: string;
}

export interface UserRes {
  data: {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
    username: string;
    avatarUrl: string;
    coverPicture: string;
    role: string;
    wallets: Wallet[];
    artistProfile: ArtistFormData;
    dynamicUserId: string;
    displayName: string;
    ownedTokens: any[]; // Adjust type based on token structure if known
    authProvider: string;
    isActive: boolean;
    isAdmin: boolean;
    description: any;
    lastLogin: string;
    metadata: Metadata;
    createdAt: string;
    updatedAt: string;
    __v: number;
    address: string;
    status: string;
  };
}

export interface CurrentViewRes {
  data: {
    role: string;
    viewMode: string;
    canSwitch: boolean;
  };
}

type SocialLink = {
  platform: string;
  url: string;
  _id?: string; // Optional, used for existing links
};

export interface ArtistSocialRes {
  data: {
    id: string;
    avatarUrl: string;
    profilePic: string;
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    role: string;
    bio: string;
    genre: string;
    socialLinks: SocialLink[];
    coverPhoto: string;
  };
}
export interface ArtistProfileRes {
  data: {
    id: string;
    avatarUrl: string;
    profilePic: string;
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    role: string;
    bio: string;
    genre: string;
    socialLinks: SocialLink[];
    coverPhoto: string;
  };
  socialLinks?: {
    platform: string;
    url: string;
  }[];
}

export interface ArtistFormData {
  id?: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  role?: string;
  bio?: string;
  genre?: string;
  socialLinks?: SocialLink[];
  coverPhoto?: string;
  profilePic?: string;
  status?: string;
}
export interface ArtistRes {
  data: {
    message: string;
    profile: ArtistFormData;
    success: boolean;
  };
}
export interface SocialRes {
  data: {
    message: string;
    profile: ArtistFormData;
    success: boolean;
  };
}

export interface FanProfileRes {
  data: {
    message: string;
    profile: UserRes;
    success: boolean;
  };
}

type BasicInfo = {
  firstName: string;
  lastName: string;
  bio: string;
};

type Progress = {
  isInProgress: boolean;
  currentStep: number;
  isSubmitted: boolean;
  status: string;
  basicInfo: BasicInfo;
  username: string;
  socialLinks: SocialLink[];
  demoTracks: any[]; // You can replace `any` with a more specific type if known
};

export interface ArtistProgressRes {
  data: {
    success: boolean;
    progress: Progress;
  };
}

export interface ArtistStepOneData {
  firstName: string;
  lastName: string;
  bio?: string;
}

export interface ArtistStepTwoData {
  username: string;
}

type StepSocialLink = {
  platform: 'instagram' | 'twitter' | 'youtube' | 'soundcloud';
  url: string;
};

export interface ArtistStepThreeData {
  socialLinks: StepSocialLink[];
}
export interface ArtistStepsRes {
  data: {
    success: boolean;
    message: string;
    currentStep?: boolean;
  };
}
