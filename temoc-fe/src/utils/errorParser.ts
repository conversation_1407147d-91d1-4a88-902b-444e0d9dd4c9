export const parseBlockchainError = (error: any) => {
  if (!error || !error.message) return 'An unknown error occurred';

  const errorMessage = error.message;

  // Check for common errors and return a friendly message
  if (errorMessage.includes('user rejected transaction')) {
    return 'You have decided to reject the transaction.';
  }
  // Check for common errors and return a friendly message
  if (errorMessage.includes('user rejected action')) {
    return 'You have decided to reject the transaction.';
  }
  if (errorMessage.includes('Contract address or signer is missing')) {
    return 'Your wallet is not connected.';
  }
  if (errorMessage.includes('User denied transaction')) {
    return 'You have decided to reject the transaction.';
  }
  if (errorMessage.includes('insufficient funds')) {
    return 'You have insufficient funds to complete this transaction.';
  }
  if (errorMessage.includes('estimateGas')) {
    return 'You have insufficient funds to transfer tokens.';
  }
  if (errorMessage.includes('nonce too low')) {
    return 'The nonce of the transaction is too low. Try incrementing the nonce.';
  }
  if (errorMessage.includes('replacement transaction underpriced')) {
    return 'There is a pending transaction with a higher gas price. Increase the gas price or wait for confirmation.';
  }
  if (errorMessage.includes('execution reverted')) {
    // Attempt to extract the revert reason if provided
    const matches = errorMessage.match(/execution reverted: (.*)/);
    const revertReason =
      matches && matches[1]
        ? matches[1]
        : 'The transaction has been reverted by the EVM.';
    return `Smart contract error: ${revertReason}`;
  }
  if (errorMessage.includes('RPC Error')) {
    return 'There was an error communicating with the blockchain.';
  }
  // Add other error checks as necessary...
  console.log(errorMessage, 'errorMessage');

  // If the error is not recognized, return the original error message
  // return errorMessage;
  return 'Something went wrong.';
};
