export const truncateAddress = (address: any) => {
  if (address?.length <= 10) return address;
  return `${address?.slice(0, 4)}...${address?.slice(-4)}`;
};

export function formatToLocalDateTimeString(dateStr: any): string {
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0');
  const day = `${date.getDate()}`.padStart(2, '0');
  const hours = `${date.getHours()}`.padStart(2, '0');
  const minutes = `${date.getMinutes()}`.padStart(2, '0');
  const seconds = `${date.getSeconds()}`.padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
}
