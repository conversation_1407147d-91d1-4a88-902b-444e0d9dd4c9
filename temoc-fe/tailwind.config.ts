import type { Config } from 'tailwindcss';
const { fontFamily } = require('tailwindcss/defaultTheme');
import forms from '@tailwindcss/forms';

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    extend: {
      colors: {
        background: '#198754',
        primary: {
          DEFAULT: '#FF6E00',
          foreground: '#6c757d',
        },
        secondary: {
          DEFAULT: '#9d9d9d',
          foreground: '#ffc107',
        },
        black: {
          DEFAULT: '#000000',
          300: '#333333',
          600: '#666666',
          900: '#999999',
        },
        borderColor: '#CECECE',
      },
      fontFamily: {
        display: ['var(--font-dmsans)', ...fontFamily.sans],
        DreamAvenue: ['var(--font-dreamavenue)', ...fontFamily.sans],
      },
      screens: {
        xs: { min: '300px', max: '540px' },
        xs1: { min: '300px', max: '430px' },
      },
    },
  },
  plugins: [forms, require('@tailwindcss/aspect-ratio')],
} satisfies Config;

export default config;
